using Messaging.Core.Models;

namespace Messaging.Core.Configuration;

/// <summary>
/// Root configuration class for messaging services containing all platform-specific settings.
/// Provides centralized configuration management for Azure Service Bus and Google Pub/Sub messaging platforms.
/// </summary>
/// <remarks>
/// This configuration class serves as the main container for all messaging-related settings,
/// organizing them into logical sections for different aspects of the messaging system:
/// - Database connection settings for message persistence
/// - Azure Service Bus configuration for Microsoft cloud messaging
/// - Google Pub/Sub configuration for Google Cloud messaging
/// - Processing parameters for message handling and performance tuning
/// - Environment-specific behaviors and feature flags
///
/// The configuration supports both local development and cloud deployment scenarios,
/// with automatic environment detection and appropriate secret loading strategies.
/// </remarks>
public class MessagingConfiguration
{
    /// <summary>
    /// Configuration section name used in appsettings.json and other configuration sources.
    /// </summary>
    public const string SectionName = "MessagingConfiguration";

    /// <summary>
    /// Gets or sets the current environment (Development, Production, Cloud).
    /// Used to determine configuration loading strategies and feature availability.
    /// </summary>
    public string Environment { get; set; } = "Development";

    /// <summary>
    /// Gets or sets whether the application is running in a cloud environment.
    /// When explicitly set, overrides the automatic environment detection.
    /// </summary>
    public bool? IsCloudEnvironmentOverride { get; set; } = null;

    /// <summary>
    /// Gets or sets the Google Cloud Platform project ID.
    /// Required for Google Pub/Sub operations and secret manager access.
    /// </summary>
    public string GcpProjectId { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the database configuration settings.
    /// Contains connection parameters for message persistence and logging.
    /// </summary>
    public DatabaseConfiguration Database { get; set; } = new();

    /// <summary>
    /// Gets or sets the Azure Service Bus configuration settings.
    /// Contains connection parameters, queue/topic settings, and processing options.
    /// </summary>
    public AzureServiceBusConfiguration AzureServiceBus { get; set; } = new();

    /// <summary>
    /// Gets or sets the Google Pub/Sub configuration settings.
    /// Contains project settings, subscription parameters, and processing options.
    /// </summary>
    public GooglePubSubConfiguration GooglePubSub { get; set; } = new();

    /// <summary>
    /// Gets or sets the message processing configuration settings.
    /// Contains batch sizes, concurrency limits, and processing behavior parameters.
    /// </summary>
    public ProcessingConfiguration Processing { get; set; } = new();

    /// <summary>
    /// Gets a value indicating whether the application is running in a cloud environment.
    /// Used to determine whether to use cloud-specific features like secret manager.
    /// If IsCloudEnvironmentOverride is set, uses that value; otherwise determines from Environment.
    /// </summary>
    public bool IsCloudEnvironment => IsCloudEnvironmentOverride ??
                                     (Environment.Equals("Production", StringComparison.OrdinalIgnoreCase) ||
                                      Environment.Equals("Cloud", StringComparison.OrdinalIgnoreCase));
}

/// <summary>
/// Configuration class for database connection settings used by messaging services.
/// Supports both connection string and individual parameter approaches for database connectivity.
/// </summary>
/// <remarks>
/// This configuration class provides flexible database connection options for message persistence:
/// - Direct connection string for simple scenarios
/// - Individual parameters for programmatic construction
/// - Automatic preference for connection string when available
///
/// Used by messaging services for storing processed messages, logs, and audit information.
/// </remarks>
public class DatabaseConfiguration
{
    /// <summary>
    /// Gets or sets the complete database connection string.
    /// When provided, takes precedence over individual connection parameters.
    /// </summary>
    public string ConnectionString { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the database server hostname or IP address.
    /// Used when constructing connection string from individual parameters.
    /// </summary>
    public string Host { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the database server port number.
    /// Defaults to 5432 (standard PostgreSQL port).
    /// </summary>
    public int Port { get; set; } = 5432;

    /// <summary>
    /// Gets or sets the database name to connect to.
    /// Used when constructing connection string from individual parameters.
    /// </summary>
    public string Database { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the database username for authentication.
    /// Used when constructing connection string from individual parameters.
    /// </summary>
    public string Username { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the database password for authentication.
    /// Should be loaded from secure sources in production environments.
    /// </summary>
    public string Password { get; set; } = string.Empty;

    /// <summary>
    /// Gets a value indicating whether to use the connection string directly
    /// rather than constructing it from individual parameters.
    /// </summary>
    public bool UseConnectionString => !string.IsNullOrEmpty(ConnectionString);
}

/// <summary>
/// Configuration class for Azure Service Bus messaging platform settings.
/// Supports both queue-based and topic-based messaging patterns with advanced routing capabilities.
/// </summary>
/// <remarks>
/// This configuration class manages Azure Service Bus settings including:
/// - Connection parameters and authentication
/// - Queue and topic configuration options
/// - Message processing and concurrency settings
/// - Advanced topic subscription and routing features
/// - Performance tuning and retry policies
///
/// Supports multiple messaging patterns:
/// - Simple queue-based messaging for point-to-point communication
/// - Topic-based messaging for publish-subscribe scenarios
/// - Multiple topic subscriptions for complex routing scenarios
/// </remarks>
public class AzureServiceBusConfiguration
{
    /// <summary>
    /// Gets or sets the Azure Service Bus connection string.
    /// Contains authentication and endpoint information for the Service Bus namespace.
    /// </summary>
    public string ConnectionString { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the queue name for queue-based messaging.
    /// Used when implementing point-to-point messaging patterns.
    /// </summary>
    public string QueueName { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the topic name for topic-based messaging.
    /// Used when implementing publish-subscribe messaging patterns.
    /// </summary>
    public string TopicName { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the subscription name for topic-based messaging.
    /// Used to identify the specific subscription within a topic.
    /// </summary>
    public string SubscriptionName { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the maximum number of messages to process concurrently.
    /// Controls the level of parallelism for message processing.
    /// </summary>
    public int MaxConcurrentMessages { get; set; } = 10;

    /// <summary>
    /// Gets or sets the message lock duration in minutes.
    /// Determines how long a message is locked for processing before becoming available again.
    /// </summary>
    public int MessageLockDurationMinutes { get; set; } = 5;

    /// <summary>
    /// Gets or sets the maximum number of retry attempts for failed messages.
    /// Controls how many times a message will be retried before being dead-lettered.
    /// </summary>
    public int MaxRetryAttempts { get; set; } = 3;

    /// <summary>
    /// Gets or sets the configuration for multiple topic subscriptions.
    /// Enables advanced routing scenarios with multiple topics and subscriptions.
    /// </summary>
    public List<TopicSubscriptionConfig> TopicSubscriptions { get; set; } = new();

    /// <summary>
    /// Gets or sets the message routing configuration.
    /// Defines how messages are routed between different topics and processors.
    /// </summary>
    public TopicRoutingConfig Routing { get; set; } = new();

    /// <summary>
    /// Gets or sets a value indicating whether to enable multiple topic subscription mode.
    /// When enabled, allows processing messages from multiple topics simultaneously.
    /// </summary>
    public bool EnableMultipleTopics { get; set; } = false;

    /// <summary>
    /// Gets a value indicating whether queue-based messaging is configured.
    /// </summary>
    public bool UseQueue => !string.IsNullOrEmpty(QueueName);

    /// <summary>
    /// Gets a value indicating whether topic-based messaging is configured.
    /// </summary>
    public bool UseTopic => !string.IsNullOrEmpty(TopicName) && !string.IsNullOrEmpty(SubscriptionName);

    /// <summary>
    /// Gets a value indicating whether multiple topic subscriptions are configured and enabled.
    /// </summary>
    public bool UseMultipleTopics => EnableMultipleTopics && TopicSubscriptions.Any();
}

public class GooglePubSubConfiguration
{
    public string ProjectId { get; set; } = string.Empty;
    public string SubscriptionName { get; set; } = string.Empty;
    public string TopicName { get; set; } = string.Empty;
    public int MaxMessages { get; set; } = 100;
    public int AckDeadlineSeconds { get; set; } = 600;
    public int MaxRetryAttempts { get; set; } = 3;
    public bool EnableMessageOrdering { get; set; } = false;

    /// <summary>
    /// Communication protocol to use: "grpc" or "http"
    /// Default is "grpc" for better performance, use "http" to bypass proxy issues
    /// </summary>
    public string Protocol { get; set; } = "grpc";

    /// <summary>
    /// Whether to use HTTP/REST instead of gRPC for communication
    /// </summary>
    public bool UseHttpProtocol => Protocol.Equals("http", StringComparison.OrdinalIgnoreCase);

    /// <summary>
    /// Multiple topic subscriptions configuration
    /// </summary>
    public List<TopicSubscriptionConfig> TopicSubscriptions { get; set; } = new();

    /// <summary>
    /// Message routing configuration
    /// </summary>
    public TopicRoutingConfig Routing { get; set; } = new();

    /// <summary>
    /// Whether to enable multiple topic subscription mode
    /// </summary>
    public bool EnableMultipleTopics { get; set; } = false;

    /// <summary>
    /// Whether to use multiple topic subscriptions
    /// </summary>
    public bool UseMultipleTopics => EnableMultipleTopics && TopicSubscriptions.Any();
}

public class ProcessingConfiguration
{
    public int BatchSize { get; set; } = 100;
    public int MaxConcurrentBatches { get; set; } = 5;
    public int ProcessingTimeoutMinutes { get; set; } = 30;
    public bool EnableDeadLetterQueue { get; set; } = true;
    public int RetryDelaySeconds { get; set; } = 30;
    public string TempDirectory { get; set; } = Path.GetTempPath();
}
