namespace Messaging.Core.Models;

/// <summary>
/// Represents a topic subscription configuration
/// </summary>
public class TopicSubscriptionConfig
{
    /// <summary>
    /// Name of the subscription
    /// </summary>
    public string SubscriptionName { get; set; } = string.Empty;

    /// <summary>
    /// Name of the topic to subscribe to
    /// </summary>
    public string TopicName { get; set; } = string.Empty;

    /// <summary>
    /// Message filters for this subscription
    /// </summary>
    public List<MessageFilterConfig> Filters { get; set; } = new();

    /// <summary>
    /// Maximum number of concurrent messages to process
    /// </summary>
    public int MaxConcurrentMessages { get; set; } = 10;

    /// <summary>
    /// Message acknowledgment deadline in seconds
    /// </summary>
    public int AckDeadlineSeconds { get; set; } = 600;

    /// <summary>
    /// Maximum number of delivery attempts
    /// </summary>
    public int MaxDeliveryAttempts { get; set; } = 5;

    /// <summary>
    /// Dead letter topic for failed messages
    /// </summary>
    public string? DeadLetterTopic { get; set; }

    /// <summary>
    /// Enable message ordering
    /// </summary>
    public bool EnableMessageOrdering { get; set; } = false;

    /// <summary>
    /// Retry policy configuration
    /// </summary>
    public RetryPolicyConfig? RetryPolicy { get; set; }

    /// <summary>
    /// Custom properties for platform-specific configuration
    /// </summary>
    public Dictionary<string, object> Properties { get; set; } = new();
}

/// <summary>
/// Represents an active topic subscription
/// </summary>
public class TopicSubscription
{
    /// <summary>
    /// Subscription name
    /// </summary>
    public string SubscriptionName { get; set; } = string.Empty;

    /// <summary>
    /// Topic name
    /// </summary>
    public string TopicName { get; set; } = string.Empty;

    /// <summary>
    /// Subscription status
    /// </summary>
    public SubscriptionStatus Status { get; set; } = SubscriptionStatus.Unknown;

    /// <summary>
    /// Creation timestamp
    /// </summary>
    public DateTime CreatedAt { get; set; }

    /// <summary>
    /// Last update timestamp
    /// </summary>
    public DateTime UpdatedAt { get; set; }

    /// <summary>
    /// Subscription configuration
    /// </summary>
    public TopicSubscriptionConfig Configuration { get; set; } = new();

    /// <summary>
    /// Platform-specific subscription details
    /// </summary>
    public Dictionary<string, object> PlatformDetails { get; set; } = new();
}

/// <summary>
/// Subscription status enumeration
/// </summary>
public enum SubscriptionStatus
{
    Unknown,
    Active,
    Inactive,
    Error,
    Creating,
    Deleting,
    Updating
}

/// <summary>
/// Subscription health information
/// </summary>
public class SubscriptionHealth
{
    /// <summary>
    /// Subscription name
    /// </summary>
    public string SubscriptionName { get; set; } = string.Empty;

    /// <summary>
    /// Health status
    /// </summary>
    public bool IsHealthy { get; set; }

    /// <summary>
    /// Health check timestamp
    /// </summary>
    public DateTime CheckedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// Error message if unhealthy
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// Number of pending messages
    /// </summary>
    public long PendingMessages { get; set; }

    /// <summary>
    /// Number of unacknowledged messages
    /// </summary>
    public long UnacknowledgedMessages { get; set; }

    /// <summary>
    /// Messages per second rate
    /// </summary>
    public double MessagesPerSecond { get; set; }

    /// <summary>
    /// Average processing time in milliseconds
    /// </summary>
    public double AverageProcessingTimeMs { get; set; }

    /// <summary>
    /// Additional health metrics
    /// </summary>
    public Dictionary<string, object> Metrics { get; set; } = new();
}

/// <summary>
/// Message filter configuration
/// </summary>
public class MessageFilterConfig
{
    /// <summary>
    /// Filter name/identifier
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Filter type (e.g., "PropertyFilter", "ContentFilter", "RegexFilter")
    /// </summary>
    public string FilterType { get; set; } = string.Empty;

    /// <summary>
    /// Filter configuration parameters
    /// </summary>
    public Dictionary<string, object> Parameters { get; set; } = new();

    /// <summary>
    /// Whether this filter is enabled
    /// </summary>
    public bool Enabled { get; set; } = true;
}

/// <summary>
/// Retry policy configuration
/// </summary>
public class RetryPolicyConfig
{
    /// <summary>
    /// Maximum number of retry attempts
    /// </summary>
    public int MaxRetryAttempts { get; set; } = 3;

    /// <summary>
    /// Initial retry delay in seconds
    /// </summary>
    public int InitialRetryDelaySeconds { get; set; } = 30;

    /// <summary>
    /// Maximum retry delay in seconds
    /// </summary>
    public int MaxRetryDelaySeconds { get; set; } = 600;

    /// <summary>
    /// Retry delay multiplier for exponential backoff
    /// </summary>
    public double RetryDelayMultiplier { get; set; } = 2.0;

    /// <summary>
    /// Whether to use exponential backoff
    /// </summary>
    public bool UseExponentialBackoff { get; set; } = true;
}
