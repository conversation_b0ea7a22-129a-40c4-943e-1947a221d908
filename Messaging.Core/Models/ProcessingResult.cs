namespace Messaging.Core.Models;

/// <summary>
/// Result of a message processing operation containing processed entities and processing statistics.
/// </summary>
/// <typeparam name="TEntity">The type of entities created from processed messages</typeparam>
/// <remarks>
/// This class provides comprehensive information about message processing results including:
/// - Successfully processed entities ready for database import
/// - Processing statistics for monitoring and reporting
/// - Error and warning information for troubleshooting
/// - Success indicators for automated processing decisions
/// </remarks>
public class ProcessingResult<TEntity> where TEntity : class
{
    /// <summary>
    /// Gets or sets the collection of entities successfully created from processed messages.
    /// These entities are ready for database import operations.
    /// </summary>
    public IEnumerable<TEntity> Entities { get; set; } = Enumerable.Empty<TEntity>();

    /// <summary>
    /// Gets or sets the total number of messages that were processed.
    /// Includes both valid and invalid messages.
    /// </summary>
    public int MessagesProcessed { get; set; }

    /// <summary>
    /// Gets or sets the number of messages that were successfully processed and converted to entities.
    /// </summary>
    public int MessagesValid { get; set; }

    /// <summary>
    /// Gets or sets the number of messages that failed processing due to validation or parsing errors.
    /// </summary>
    public int MessagesInvalid { get; set; }

    /// <summary>
    /// Gets or sets the collection of error messages encountered during processing.
    /// Contains detailed error information for troubleshooting failed messages.
    /// </summary>
    public List<string> Errors { get; set; } = new();

    /// <summary>
    /// Gets or sets the collection of warning messages generated during processing.
    /// Contains non-fatal issues that didn't prevent processing but may need attention.
    /// </summary>
    public List<string> Warnings { get; set; } = new();

    /// <summary>
    /// Gets a value indicating whether the processing operation was completely successful.
    /// Returns true only if no messages were invalid and no errors occurred.
    /// </summary>
    public bool IsSuccess => MessagesInvalid == 0 && Errors.Count == 0;
}

/// <summary>
/// Result of a database import operation containing import statistics and error information.
/// </summary>
/// <remarks>
/// This class provides comprehensive information about database import results including:
/// - Import statistics for monitoring and reporting
/// - Error and warning information for troubleshooting
/// - Success indicators for automated processing decisions
/// - Detailed breakdown of insert, update, and failure counts
/// </remarks>
public class ImportResult
{
    /// <summary>
    /// Gets or sets the total number of messages that were processed for import.
    /// This represents the input count before database operations.
    /// </summary>
    public int MessagesProcessed { get; set; }

    /// <summary>
    /// Gets or sets the number of new records that were successfully inserted into the database.
    /// </summary>
    public int RecordsInserted { get; set; }

    /// <summary>
    /// Gets or sets the number of existing records that were successfully updated in the database.
    /// </summary>
    public int RecordsUpdated { get; set; }

    /// <summary>
    /// Gets or sets the number of records that failed to be imported due to database errors or validation issues.
    /// </summary>
    public int RecordsFailed { get; set; }

    /// <summary>
    /// Gets or sets the collection of error messages encountered during the import operation.
    /// Contains detailed error information for troubleshooting failed imports.
    /// </summary>
    public List<string> Errors { get; set; } = new();

    /// <summary>
    /// Gets or sets the collection of warning messages generated during the import operation.
    /// Contains non-fatal issues that didn't prevent import but may need attention.
    /// </summary>
    public List<string> Warnings { get; set; } = new();

    /// <summary>
    /// Gets a value indicating whether the import operation was completely successful.
    /// Returns true only if no records failed and no errors occurred.
    /// </summary>
    public bool IsSuccess => RecordsFailed == 0 && Errors.Count == 0;
}

/// <summary>
/// Result of a message acknowledgment operation in a messaging system.
/// </summary>
/// <remarks>
/// This class provides information about message acknowledgment operations including:
/// - Success status of the acknowledgment
/// - Error details if the operation failed
/// - The specific action that was performed on the message
/// - Message identification for tracking and logging
/// </remarks>
public class AcknowledgmentResult
{
    /// <summary>
    /// Gets or sets the unique identifier of the message that was acknowledged.
    /// </summary>
    public string MessageId { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets a value indicating whether the acknowledgment operation was successful.
    /// </summary>
    public bool IsSuccess { get; set; }

    /// <summary>
    /// Gets or sets the error message if the acknowledgment operation failed.
    /// Contains detailed error information for troubleshooting.
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// Gets or sets the acknowledgment action that was performed on the message.
    /// </summary>
    public AcknowledgmentAction Action { get; set; }
}

/// <summary>
/// Actions that can be taken on a message
/// </summary>
public enum AcknowledgmentAction
{
    Complete,
    Abandon,
    DeadLetter,
    Defer
}

/// <summary>
/// Health check result for messaging services
/// </summary>
public class HealthCheckResult
{
    public bool IsHealthy { get; set; }
    public string? ErrorMessage { get; set; }
    public Dictionary<string, object> Details { get; set; } = new();
    public DateTime CheckTime { get; set; } = DateTime.UtcNow;
}
