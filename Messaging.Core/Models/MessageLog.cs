using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Messaging.Core.Models;

[Table("message_logs")]
public class MessageLog
{
    [Key]
    [Column("id")]
    public int Id { get; set; }

    [Column("batch_id")]
    [MaxLength(100)]
    public string BatchId { get; set; } = string.Empty;

    [Column("message_id")]
    [MaxLength(500)]
    public string MessageId { get; set; } = string.Empty;

    [Column("message_type")]
    [MaxLength(100)]
    public string? MessageType { get; set; }

    [Column("source_system")]
    [MaxLength(50)]
    public string SourceSystem { get; set; } = string.Empty; // "AzureServiceBus", "GooglePubSub"

    [Column("processor_type")]
    [MaxLength(100)]
    public string ProcessorType { get; set; } = string.Empty;

    [Column("queue_name")]
    [MaxLength(200)]
    public string? QueueName { get; set; }

    [Column("topic_name")]
    [MaxLength(200)]
    public string? TopicName { get; set; }

    [Column("subscription_name")]
    [MaxLength(200)]
    public string? SubscriptionName { get; set; }

    [Column("message_size")]
    public long? MessageSize { get; set; }

    [Column("messages_processed")]
    public int MessagesProcessed { get; set; }

    [Column("messages_inserted")]
    public int MessagesInserted { get; set; }

    [Column("messages_updated")]
    public int MessagesUpdated { get; set; }

    [Column("messages_failed")]
    public int MessagesFailed { get; set; }

    [Column("start_time")]
    public DateTime StartTime { get; set; }

    [Column("end_time")]
    public DateTime? EndTime { get; set; }

    [Column("status")]
    [MaxLength(50)]
    public string Status { get; set; } = "Processing";

    [Column("error_message")]
    public string? ErrorMessage { get; set; }

    [Column("retry_count")]
    public int RetryCount { get; set; } = 0;

    [Column("created_at")]
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    public TimeSpan? Duration => EndTime.HasValue ? EndTime.Value - StartTime : null;
}

/// <summary>
/// Represents a message received from a messaging system
/// </summary>
public class MessageData
{
    public string MessageId { get; set; } = string.Empty;
    public string? MessageType { get; set; }
    public string Body { get; set; } = string.Empty;
    public Dictionary<string, object> Properties { get; set; } = new();
    public DateTime EnqueuedTime { get; set; }
    public DateTime? ScheduledEnqueueTime { get; set; }
    public int DeliveryCount { get; set; }
    public string? CorrelationId { get; set; }
    public string? SessionId { get; set; }
    public TimeSpan? TimeToLive { get; set; }

    /// <summary>
    /// Topic name where the message originated from
    /// </summary>
    public string? TopicName { get; set; }

    /// <summary>
    /// Subscription name that received the message
    /// </summary>
    public string? SubscriptionName { get; set; }

    /// <summary>
    /// Message routing key or subject for topic-based routing
    /// </summary>
    public string? RoutingKey { get; set; }

    /// <summary>
    /// Message labels/tags for filtering and routing
    /// </summary>
    public Dictionary<string, string> Labels { get; set; } = new();

    /// <summary>
    /// Message priority for routing decisions
    /// </summary>
    public int Priority { get; set; } = 0;

    /// <summary>
    /// Content type of the message body
    /// </summary>
    public string? ContentType { get; set; }

    // Platform-specific data
    public object? PlatformMessage { get; set; }
}
