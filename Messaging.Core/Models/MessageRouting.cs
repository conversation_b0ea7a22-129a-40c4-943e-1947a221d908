namespace Messaging.Core.Models;

/// <summary>
/// Represents a message routing rule
/// </summary>
public class MessageRoutingRule
{
    /// <summary>
    /// Unique identifier for the routing rule
    /// </summary>
    public string Id { get; set; } = Guid.NewGuid().ToString();

    /// <summary>
    /// Human-readable name for the rule
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Description of what this rule does
    /// </summary>
    public string Description { get; set; } = string.Empty;

    /// <summary>
    /// Priority of this rule (higher numbers = higher priority)
    /// </summary>
    public int Priority { get; set; } = 0;

    /// <summary>
    /// Whether this rule is enabled
    /// </summary>
    public bool Enabled { get; set; } = true;

    /// <summary>
    /// Conditions that must be met for this rule to apply
    /// </summary>
    public List<RoutingCondition> Conditions { get; set; } = new();

    /// <summary>
    /// Action to take when conditions are met
    /// </summary>
    public RoutingAction Action { get; set; } = new();

    /// <summary>
    /// Creation timestamp
    /// </summary>
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// Last update timestamp
    /// </summary>
    public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// Number of times this rule has been matched
    /// </summary>
    public long MatchCount { get; set; } = 0;

    /// <summary>
    /// Last time this rule was matched
    /// </summary>
    public DateTime? LastMatchedAt { get; set; }
}

/// <summary>
/// Represents a condition for message routing
/// </summary>
public class RoutingCondition
{
    /// <summary>
    /// Field to evaluate (e.g., "MessageType", "TopicName", "Properties.CustomerId")
    /// </summary>
    public string Field { get; set; } = string.Empty;

    /// <summary>
    /// Operator to use for comparison
    /// </summary>
    public RoutingOperator Operator { get; set; } = RoutingOperator.Equals;

    /// <summary>
    /// Value to compare against
    /// </summary>
    public string Value { get; set; } = string.Empty;

    /// <summary>
    /// Whether this condition should be case-sensitive
    /// </summary>
    public bool CaseSensitive { get; set; } = false;

    /// <summary>
    /// Whether this condition is negated (NOT condition)
    /// </summary>
    public bool Negate { get; set; } = false;
}

/// <summary>
/// Routing operators for conditions
/// </summary>
public enum RoutingOperator
{
    Equals,
    NotEquals,
    Contains,
    NotContains,
    StartsWith,
    EndsWith,
    Regex,
    GreaterThan,
    LessThan,
    GreaterThanOrEqual,
    LessThanOrEqual,
    In,
    NotIn,
    Exists,
    NotExists
}

/// <summary>
/// Action to take when routing conditions are met
/// </summary>
public class RoutingAction
{
    /// <summary>
    /// Type of action to take
    /// </summary>
    public RoutingActionType ActionType { get; set; } = RoutingActionType.RouteToProcessor;

    /// <summary>
    /// Target processor type for RouteToProcessor action
    /// </summary>
    public string? ProcessorType { get; set; }

    /// <summary>
    /// Target topic for RouteToTopic action
    /// </summary>
    public string? TargetTopic { get; set; }

    /// <summary>
    /// Target subscription for RouteToSubscription action
    /// </summary>
    public string? TargetSubscription { get; set; }

    /// <summary>
    /// Whether to stop processing other rules after this action
    /// </summary>
    public bool StopProcessing { get; set; } = true;

    /// <summary>
    /// Additional properties for the action
    /// </summary>
    public Dictionary<string, object> Properties { get; set; } = new();
}

/// <summary>
/// Types of routing actions
/// </summary>
public enum RoutingActionType
{
    /// <summary>
    /// Route message to a specific processor
    /// </summary>
    RouteToProcessor,

    /// <summary>
    /// Route message to a different topic
    /// </summary>
    RouteToTopic,

    /// <summary>
    /// Route message to a specific subscription
    /// </summary>
    RouteToSubscription,

    /// <summary>
    /// Drop/ignore the message
    /// </summary>
    Drop,

    /// <summary>
    /// Send message to dead letter queue
    /// </summary>
    DeadLetter,

    /// <summary>
    /// Transform message and continue processing
    /// </summary>
    Transform,

    /// <summary>
    /// Log message and continue processing
    /// </summary>
    Log
}

/// <summary>
/// Result of message routing evaluation
/// </summary>
public class RoutingResult
{
    /// <summary>
    /// Whether routing was successful
    /// </summary>
    public bool Success { get; set; } = true;

    /// <summary>
    /// Target processor type (if routed to processor)
    /// </summary>
    public string? ProcessorType { get; set; }

    /// <summary>
    /// Target topic (if routed to topic)
    /// </summary>
    public string? TargetTopic { get; set; }

    /// <summary>
    /// Target subscription (if routed to subscription)
    /// </summary>
    public string? TargetSubscription { get; set; }

    /// <summary>
    /// Action that was taken
    /// </summary>
    public RoutingActionType ActionTaken { get; set; }

    /// <summary>
    /// Rule that was matched (if any)
    /// </summary>
    public MessageRoutingRule? MatchedRule { get; set; }

    /// <summary>
    /// Whether to continue processing the message
    /// </summary>
    public bool ContinueProcessing { get; set; } = true;

    /// <summary>
    /// Error message if routing failed
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// Additional routing metadata
    /// </summary>
    public Dictionary<string, object> Metadata { get; set; } = new();
}

/// <summary>
/// Configuration for topic-based message routing
/// </summary>
public class TopicRoutingConfig
{
    /// <summary>
    /// Default processor type for unmatched messages
    /// </summary>
    public string? DefaultProcessorType { get; set; }

    /// <summary>
    /// Whether to enable routing rule evaluation
    /// </summary>
    public bool EnableRouting { get; set; } = true;

    /// <summary>
    /// Whether to log routing decisions
    /// </summary>
    public bool LogRoutingDecisions { get; set; } = false;

    /// <summary>
    /// Maximum number of routing rules to evaluate
    /// </summary>
    public int MaxRulesEvaluated { get; set; } = 100;

    /// <summary>
    /// Routing rules
    /// </summary>
    public List<MessageRoutingRule> Rules { get; set; } = new();
}
