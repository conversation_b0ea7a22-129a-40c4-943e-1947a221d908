namespace Messaging.Core.Services;

public interface ISecretManagerService
{
    /// <summary>
    /// Retrieves a secret value from GCP Secret Manager
    /// </summary>
    /// <param name="secretName">Name of the secret</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>The secret value</returns>
    Task<string> GetSecretAsync(string secretName, CancellationToken cancellationToken = default);

    /// <summary>
    /// Checks if the service is available (i.e., running in GCP environment)
    /// </summary>
    /// <returns>True if service is available, false otherwise</returns>
    bool IsAvailable();
}
