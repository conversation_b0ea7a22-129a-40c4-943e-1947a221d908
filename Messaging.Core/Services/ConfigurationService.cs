using Microsoft.Extensions.Logging;
using Messaging.Core.Configuration;

namespace Messaging.Core.Services;

public class ConfigurationService : IConfigurationService
{
    private readonly ILogger<ConfigurationService> _logger;
    private readonly MessagingConfiguration _baseConfig;
    private readonly ISecretManagerService _secretManager;

    public ConfigurationService(
        ILogger<ConfigurationService> logger,
        MessagingConfiguration baseConfig,
        ISecretManagerService secretManager)
    {
        _logger = logger;
        _baseConfig = baseConfig;
        _secretManager = secretManager;
    }

    public async Task<MessagingConfiguration> GetConfigurationAsync(CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Loading messaging configuration for environment: {Environment}", _baseConfig.Environment);

        var config = new MessagingConfiguration
        {
            Environment = _baseConfig.Environment,
            GcpProjectId = _baseConfig.GcpProjectId,
            Processing = _baseConfig.Processing
        };

        // Load database configuration
        config.Database = await LoadDatabaseConfigurationAsync(cancellationToken);

        // Load Azure Service Bus configuration
        config.AzureServiceBus = await LoadAzureServiceBusConfigurationAsync(cancellationToken);

        // Load Google Pub/Sub configuration
        config.GooglePubSub = await LoadGooglePubSubConfigurationAsync(cancellationToken);

        _logger.LogInformation("Messaging configuration loaded successfully");
        return config;
    }

    public async Task<string> GetDatabaseConnectionStringAsync(CancellationToken cancellationToken = default)
    {
        var dbConfig = await LoadDatabaseConfigurationAsync(cancellationToken);
        
        if (dbConfig.UseConnectionString)
        {
            return dbConfig.ConnectionString;
        }

        // Build connection string from individual components
        return $"Host={dbConfig.Host};Port={dbConfig.Port};Database={dbConfig.Database};Username={dbConfig.Username};Password={dbConfig.Password};";
    }

    public async Task<AzureServiceBusConfiguration> GetAzureServiceBusConfigurationAsync(CancellationToken cancellationToken = default)
    {
        return await LoadAzureServiceBusConfigurationAsync(cancellationToken);
    }

    public async Task<GooglePubSubConfiguration> GetGooglePubSubConfigurationAsync(CancellationToken cancellationToken = default)
    {
        return await LoadGooglePubSubConfigurationAsync(cancellationToken);
    }

    private async Task<DatabaseConfiguration> LoadDatabaseConfigurationAsync(CancellationToken cancellationToken)
    {
        var dbConfig = new DatabaseConfiguration
        {
            Host = _baseConfig.Database.Host,
            Port = _baseConfig.Database.Port,
            Database = _baseConfig.Database.Database,
            Username = _baseConfig.Database.Username
        };

        if (_baseConfig.IsCloudEnvironment && _secretManager.IsAvailable())
        {
            _logger.LogDebug("Loading database secrets from GCP Secret Manager");
            
            try
            {
                // Load database password from Secret Manager
                dbConfig.Password = await _secretManager.GetSecretAsync("messaging-database-password", cancellationToken);
                
                // Optionally load full connection string from Secret Manager
                try
                {
                    dbConfig.ConnectionString = await _secretManager.GetSecretAsync("messaging-database-connection-string", cancellationToken);
                }
                catch (Exception ex)
                {
                    _logger.LogDebug(ex, "Database connection string not found in Secret Manager, using individual components");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to load database secrets from Secret Manager");
                throw;
            }
        }
        else
        {
            _logger.LogDebug("Loading database configuration from local settings");
            dbConfig.Password = _baseConfig.Database.Password;
            dbConfig.ConnectionString = _baseConfig.Database.ConnectionString;
        }

        return dbConfig;
    }

    private async Task<AzureServiceBusConfiguration> LoadAzureServiceBusConfigurationAsync(CancellationToken cancellationToken)
    {
        var asbConfig = new AzureServiceBusConfiguration
        {
            QueueName = _baseConfig.AzureServiceBus.QueueName,
            TopicName = _baseConfig.AzureServiceBus.TopicName,
            SubscriptionName = _baseConfig.AzureServiceBus.SubscriptionName,
            MaxConcurrentMessages = _baseConfig.AzureServiceBus.MaxConcurrentMessages,
            MessageLockDurationMinutes = _baseConfig.AzureServiceBus.MessageLockDurationMinutes,
            MaxRetryAttempts = _baseConfig.AzureServiceBus.MaxRetryAttempts
        };

        if (_baseConfig.IsCloudEnvironment && _secretManager.IsAvailable())
        {
            _logger.LogDebug("Loading Azure Service Bus secrets from GCP Secret Manager");
            
            try
            {
                asbConfig.ConnectionString = await _secretManager.GetSecretAsync("azure-servicebus-connection-string", cancellationToken);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to load Azure Service Bus secrets from Secret Manager");
                throw;
            }
        }
        else
        {
            _logger.LogDebug("Loading Azure Service Bus configuration from local settings");
            asbConfig.ConnectionString = _baseConfig.AzureServiceBus.ConnectionString;
        }

        return asbConfig;
    }

    private async Task<GooglePubSubConfiguration> LoadGooglePubSubConfigurationAsync(CancellationToken cancellationToken)
    {
        var pubsubConfig = new GooglePubSubConfiguration
        {
            ProjectId = _baseConfig.GooglePubSub.ProjectId,
            SubscriptionName = _baseConfig.GooglePubSub.SubscriptionName,
            TopicName = _baseConfig.GooglePubSub.TopicName,
            MaxMessages = _baseConfig.GooglePubSub.MaxMessages,
            AckDeadlineSeconds = _baseConfig.GooglePubSub.AckDeadlineSeconds,
            MaxRetryAttempts = _baseConfig.GooglePubSub.MaxRetryAttempts,
            EnableMessageOrdering = _baseConfig.GooglePubSub.EnableMessageOrdering,
            Protocol = _baseConfig.GooglePubSub.Protocol,
            EnableMultipleTopics = _baseConfig.GooglePubSub.EnableMultipleTopics,
            TopicSubscriptions = _baseConfig.GooglePubSub.TopicSubscriptions,
            Routing = _baseConfig.GooglePubSub.Routing
        };

        // For Google Pub/Sub, we typically use service account authentication
        // No additional secrets needed if running in GCP with proper IAM roles
        if (_baseConfig.IsCloudEnvironment && _secretManager.IsAvailable())
        {
            _logger.LogDebug("Loading Google Pub/Sub configuration for cloud environment");
            
            // Optionally load service account key from Secret Manager if needed
            try
            {
                var serviceAccountKey = await _secretManager.GetSecretAsync("google-pubsub-service-account-key", cancellationToken);
                // Set environment variable for Google client libraries
                Environment.SetEnvironmentVariable("GOOGLE_APPLICATION_CREDENTIALS_JSON", serviceAccountKey);
            }
            catch (Exception ex)
            {
                _logger.LogDebug(ex, "Google Pub/Sub service account key not found in Secret Manager, using default authentication");
            }
        }
        else
        {
            _logger.LogDebug("Loading Google Pub/Sub configuration from local settings");
        }

        return pubsubConfig;
    }
}
