using Google.Cloud.SecretManager.V1;
using Microsoft.Extensions.Logging;
using Messaging.Core.Configuration;

namespace Messaging.Core.Services;

public class SecretManagerService : ISecretManagerService
{
    private readonly ILogger<SecretManagerService> _logger;
    private readonly MessagingConfiguration _config;
    private readonly SecretManagerServiceClient? _client;

    public SecretManagerService(ILogger<SecretManagerService> logger, MessagingConfiguration config)
    {
        _logger = logger;
        _config = config;

        if (IsAvailable())
        {
            try
            {
                _logger.LogDebug("Attempting to initialize Secret Manager client for project: {ProjectId}", _config.GcpProjectId);
                _client = SecretManagerServiceClient.Create();
                _logger.LogInformation("Secret Manager client initialized successfully");
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to initialize Secret Manager client - continuing without Secret Manager");
                // Don't rethrow - continue without Secret Manager
            }
        }
        else
        {
            _logger.LogDebug("Secret Manager not available - IsCloudEnvironment: {IsCloud}, GcpProjectId: '{ProjectId}'",
                _config.IsCloudEnvironment, _config.GcpProjectId);
        }
    }

    public async Task<string> GetSecretAsync(string secretName, CancellationToken cancellationToken = default)
    {
        if (_client == null)
        {
            throw new InvalidOperationException("Secret Manager client is not available");
        }

        try
        {
            _logger.LogDebug("Retrieving secret {SecretName} from GCP Secret Manager", secretName);

            var secretVersionName = new SecretVersionName(_config.GcpProjectId, secretName, "latest");
            var response = await _client.AccessSecretVersionAsync(secretVersionName, cancellationToken);

            var secretValue = response.Payload.Data.ToStringUtf8();
            
            _logger.LogDebug("Successfully retrieved secret {SecretName}", secretName);
            return secretValue;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to retrieve secret {SecretName} from GCP Secret Manager", secretName);
            throw;
        }
    }

    public bool IsAvailable()
    {
        // Check if we're running in a cloud environment and have a project ID
        var isCloudEnv = _config.IsCloudEnvironment;
        var hasProjectId = !string.IsNullOrEmpty(_config.GcpProjectId);

        _logger.LogDebug("Secret Manager availability check - IsCloudEnvironment: {IsCloud}, HasProjectId: {HasProject}",
            isCloudEnv, hasProjectId);

        return isCloudEnv && hasProjectId;
    }
}
