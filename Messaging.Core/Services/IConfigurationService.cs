using Messaging.Core.Configuration;

namespace Messaging.Core.Services;

public interface IConfigurationService
{
    /// <summary>
    /// Gets the messaging configuration, loading secrets from appropriate sources
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Fully populated messaging configuration</returns>
    Task<MessagingConfiguration> GetConfigurationAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets the database connection string, loading from secrets if needed
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Database connection string</returns>
    Task<string> GetDatabaseConnectionStringAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets the Azure Service Bus configuration, loading secrets if needed
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Azure Service Bus configuration</returns>
    Task<AzureServiceBusConfiguration> GetAzureServiceBusConfigurationAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets the Google Pub/Sub configuration, loading secrets if needed
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Google Pub/Sub configuration</returns>
    Task<GooglePubSubConfiguration> GetGooglePubSubConfigurationAsync(CancellationToken cancellationToken = default);
}
