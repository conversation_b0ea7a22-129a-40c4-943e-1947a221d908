using Microsoft.Extensions.Logging;
using Messaging.Core.Abstractions;
using Messaging.Core.Models;
using System.Text.RegularExpressions;

namespace Messaging.Core.Services;

/// <summary>
/// Default implementation of message routing based on configurable rules
/// </summary>
public class MessageRouter : IMessageRouter
{
    private readonly ILogger<MessageRouter> _logger;
    private readonly List<MessageRoutingRule> _rules = new();
    private readonly object _rulesLock = new();

    public MessageRouter(ILogger<MessageRouter> logger)
    {
        _logger = logger;
    }

    public string? RouteMessage(MessageData message)
    {
        lock (_rulesLock)
        {
            var enabledRules = _rules
                .Where(r => r.Enabled)
                .OrderByDescending(r => r.Priority)
                .ToList();

            foreach (var rule in enabledRules)
            {
                if (EvaluateRule(rule, message))
                {
                    _logger.LogDebug("Message {MessageId} matched routing rule {RuleId}: {RuleName}", 
                        message.MessageId, rule.Id, rule.Name);

                    // Update rule statistics
                    rule.MatchCount++;
                    rule.LastMatchedAt = DateTime.UtcNow;

                    // Execute the action
                    var result = ExecuteAction(rule.Action, message);
                    
                    if (rule.Action.StopProcessing)
                    {
                        return result;
                    }
                }
            }

            _logger.LogDebug("Message {MessageId} did not match any routing rules", message.MessageId);
            return null;
        }
    }

    public IEnumerable<MessageRoutingRule> GetRoutingRules()
    {
        lock (_rulesLock)
        {
            return _rules.ToList();
        }
    }

    public void AddRoutingRule(MessageRoutingRule rule)
    {
        lock (_rulesLock)
        {
            _rules.Add(rule);
            _logger.LogInformation("Added routing rule {RuleId}: {RuleName}", rule.Id, rule.Name);
        }
    }

    public bool RemoveRoutingRule(string ruleId)
    {
        lock (_rulesLock)
        {
            var rule = _rules.FirstOrDefault(r => r.Id == ruleId);
            if (rule != null)
            {
                _rules.Remove(rule);
                _logger.LogInformation("Removed routing rule {RuleId}: {RuleName}", rule.Id, rule.Name);
                return true;
            }
            return false;
        }
    }

    public bool UpdateRoutingRule(string ruleId, MessageRoutingRule updatedRule)
    {
        lock (_rulesLock)
        {
            var index = _rules.FindIndex(r => r.Id == ruleId);
            if (index >= 0)
            {
                updatedRule.Id = ruleId; // Preserve the original ID
                updatedRule.UpdatedAt = DateTime.UtcNow;
                _rules[index] = updatedRule;
                _logger.LogInformation("Updated routing rule {RuleId}: {RuleName}", ruleId, updatedRule.Name);
                return true;
            }
            return false;
        }
    }

    private bool EvaluateRule(MessageRoutingRule rule, MessageData message)
    {
        if (!rule.Conditions.Any())
        {
            return true; // No conditions means always match
        }

        // All conditions must be true (AND logic)
        return rule.Conditions.All(condition => EvaluateCondition(condition, message));
    }

    private bool EvaluateCondition(RoutingCondition condition, MessageData message)
    {
        try
        {
            var fieldValue = GetFieldValue(condition.Field, message);
            var result = CompareValues(fieldValue, condition.Value, condition.Operator, condition.CaseSensitive);
            
            return condition.Negate ? !result : result;
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error evaluating routing condition for field {Field}", condition.Field);
            return false;
        }
    }

    private string? GetFieldValue(string field, MessageData message)
    {
        return field.ToLowerInvariant() switch
        {
            "messageid" => message.MessageId,
            "messagetype" => message.MessageType,
            "body" => message.Body,
            "topicname" => message.TopicName,
            "subscriptionname" => message.SubscriptionName,
            "routingkey" => message.RoutingKey,
            "correlationid" => message.CorrelationId,
            "sessionid" => message.SessionId,
            "contenttype" => message.ContentType,
            "priority" => message.Priority.ToString(),
            _ when field.StartsWith("properties.", StringComparison.OrdinalIgnoreCase) => 
                GetPropertyValue(field.Substring(11), message.Properties),
            _ when field.StartsWith("labels.", StringComparison.OrdinalIgnoreCase) => 
                GetLabelValue(field.Substring(7), message.Labels),
            _ => null
        };
    }

    private string? GetPropertyValue(string propertyName, Dictionary<string, object> properties)
    {
        return properties.TryGetValue(propertyName, out var value) ? value?.ToString() : null;
    }

    private string? GetLabelValue(string labelName, Dictionary<string, string> labels)
    {
        return labels.TryGetValue(labelName, out var value) ? value : null;
    }

    private bool CompareValues(string? fieldValue, string conditionValue, RoutingOperator op, bool caseSensitive)
    {
        var comparison = caseSensitive ? StringComparison.Ordinal : StringComparison.OrdinalIgnoreCase;

        return op switch
        {
            RoutingOperator.Equals => string.Equals(fieldValue, conditionValue, comparison),
            RoutingOperator.NotEquals => !string.Equals(fieldValue, conditionValue, comparison),
            RoutingOperator.Contains => fieldValue?.Contains(conditionValue, comparison) == true,
            RoutingOperator.NotContains => fieldValue?.Contains(conditionValue, comparison) != true,
            RoutingOperator.StartsWith => fieldValue?.StartsWith(conditionValue, comparison) == true,
            RoutingOperator.EndsWith => fieldValue?.EndsWith(conditionValue, comparison) == true,
            RoutingOperator.Regex => fieldValue != null && Regex.IsMatch(fieldValue, conditionValue),
            RoutingOperator.Exists => !string.IsNullOrEmpty(fieldValue),
            RoutingOperator.NotExists => string.IsNullOrEmpty(fieldValue),
            RoutingOperator.In => conditionValue.Split(',').Any(v => string.Equals(fieldValue, v.Trim(), comparison)),
            RoutingOperator.NotIn => !conditionValue.Split(',').Any(v => string.Equals(fieldValue, v.Trim(), comparison)),
            RoutingOperator.GreaterThan => CompareNumeric(fieldValue, conditionValue, (a, b) => a > b),
            RoutingOperator.LessThan => CompareNumeric(fieldValue, conditionValue, (a, b) => a < b),
            RoutingOperator.GreaterThanOrEqual => CompareNumeric(fieldValue, conditionValue, (a, b) => a >= b),
            RoutingOperator.LessThanOrEqual => CompareNumeric(fieldValue, conditionValue, (a, b) => a <= b),
            _ => false
        };
    }

    private bool CompareNumeric(string? fieldValue, string conditionValue, Func<double, double, bool> comparison)
    {
        if (double.TryParse(fieldValue, out var fieldNum) && double.TryParse(conditionValue, out var conditionNum))
        {
            return comparison(fieldNum, conditionNum);
        }
        return false;
    }

    private string? ExecuteAction(RoutingAction action, MessageData message)
    {
        return action.ActionType switch
        {
            RoutingActionType.RouteToProcessor => action.ProcessorType,
            RoutingActionType.RouteToTopic => action.TargetTopic,
            RoutingActionType.RouteToSubscription => action.TargetSubscription,
            RoutingActionType.Drop => null,
            RoutingActionType.DeadLetter => "DeadLetter",
            RoutingActionType.Transform => HandleTransform(action, message),
            RoutingActionType.Log => HandleLog(action, message),
            _ => null
        };
    }

    private string? HandleTransform(RoutingAction action, MessageData message)
    {
        // Transform logic would be implemented here
        _logger.LogInformation("Transform action executed for message {MessageId}", message.MessageId);
        return action.ProcessorType;
    }

    private string? HandleLog(RoutingAction action, MessageData message)
    {
        _logger.LogInformation("Log action executed for message {MessageId}: {Message}", 
            message.MessageId, action.Properties.GetValueOrDefault("LogMessage", "Message logged"));
        return action.ProcessorType;
    }
}
