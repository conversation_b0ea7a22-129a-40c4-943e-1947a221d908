using Messaging.Core.Models;

namespace Messaging.Core.Abstractions;

/// <summary>
/// Generic service interface for importing processed message entities into the database.
/// Provides database operations for entity persistence and message processing tracking functionality.
/// </summary>
/// <typeparam name="TEntity">The entity type to be imported into the database</typeparam>
/// <remarks>
/// This service handles the database persistence layer of the message processing workflow, including:
/// - Bulk import operations with upsert capabilities (insert new, update existing)
/// - Message processing logging and audit trail management
/// - Batch processing statistics and error tracking
/// - Transaction management for data consistency
/// - Monitoring and reporting capabilities
///
/// Implementations should provide efficient bulk operations and proper error handling
/// to ensure data integrity during large message processing operations.
/// </remarks>
public interface IMessageImportService<TEntity> where TEntity : class
{
    /// <summary>
    /// Imports entities into the database
    /// </summary>
    /// <param name="entities">Entities to import</param>
    /// <param name="batchId">Unique identifier for this import batch</param>
    /// <param name="processorType">Type of processor used</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Import result with statistics</returns>
    Task<ImportResult> ImportEntitiesAsync(IEnumerable<TEntity> entities, string batchId, string processorType, CancellationToken cancellationToken = default);

    /// <summary>
    /// Creates a message log entry
    /// </summary>
    /// <param name="messageLog">Message log to create</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Created message log with ID</returns>
    Task<MessageLog> CreateMessageLogAsync(MessageLog messageLog, CancellationToken cancellationToken = default);

    /// <summary>
    /// Updates an existing message log entry
    /// </summary>
    /// <param name="messageLog">Message log to update</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Updated message log</returns>
    Task<MessageLog> UpdateMessageLogAsync(MessageLog messageLog, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets message logs for a specific batch
    /// </summary>
    /// <param name="batchId">Batch ID to search for</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Message logs for the batch</returns>
    Task<IEnumerable<MessageLog>> GetMessageLogsByBatchAsync(string batchId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets recent message logs for monitoring
    /// </summary>
    /// <param name="hours">Number of hours to look back</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Recent message logs</returns>
    Task<IEnumerable<MessageLog>> GetRecentMessageLogsAsync(int hours = 24, CancellationToken cancellationToken = default);
}
