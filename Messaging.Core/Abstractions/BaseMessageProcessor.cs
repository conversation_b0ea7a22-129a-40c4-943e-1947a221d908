using Microsoft.Extensions.Logging;
using Messaging.Core.Models;
using System.Text.Json;

namespace Messaging.Core.Abstractions;

/// <summary>
/// Abstract base class providing common functionality for message processors across different messaging platforms.
/// Implements standard message processing patterns, validation logic, and utility methods for data parsing.
/// </summary>
/// <typeparam name="TEntity">The entity type that this processor creates from message data</typeparam>
/// <remarks>
/// This base class provides a foundation for implementing message processors with:
///
/// **Common Functionality:**
/// - Message validation with basic checks (MessageId, Body presence)
/// - Processing statistics tracking and monitoring
/// - Logging infrastructure with structured logging support
/// - Standard error handling patterns
///
/// **Utility Methods:**
/// - JSON message parsing with error handling
/// - Type-safe property extraction from message data
/// - String, integer, and date parsing with null handling
/// - Message property access with type conversion
///
/// **Statistics Tracking:**
/// - Thread-safe processing statistics management
/// - Counters for processed, succeeded, and failed messages
/// - Timing information for monitoring and alerting
/// - Resettable statistics for operational management
///
/// **Extensibility Points:**
/// - Abstract methods for processor-specific logic
/// - Virtual methods for customizable validation
/// - Protected helper methods for common parsing scenarios
/// - Structured logging support throughout processing
///
/// **Implementation Guidelines:**
/// - Override ProcessMessagesAsync for platform-specific message processing
/// - Override ValidateMessageContentAsync for custom validation rules
/// - Use provided parsing helpers for consistent data handling
/// - Leverage the Logger for structured logging throughout processing
/// - Update statistics using UpdateProcessingStats for monitoring
///
/// Derived classes should focus on platform-specific message processing logic
/// while leveraging the common infrastructure provided by this base class.
/// </remarks>
public abstract class BaseMessageProcessor<TEntity> : IMessageProcessor<TEntity> where TEntity : class
{
    protected readonly ILogger Logger;
    private readonly Dictionary<string, object> _processingStats = new();
    private readonly object _statsLock = new();

    protected BaseMessageProcessor(ILogger logger)
    {
        Logger = logger;
        ResetProcessingStats();
    }

    public abstract string ProcessorType { get; }
    public abstract string[] SupportedMessageTypes { get; }
    public abstract string PlatformName { get; }

    public virtual bool CanProcess(MessageData message)
    {
        if (SupportedMessageTypes.Length == 0)
            return true;

        return SupportedMessageTypes.Any(type => 
            string.Equals(type, message.MessageType, StringComparison.OrdinalIgnoreCase) ||
            type == "*");
    }

    public abstract Task<ProcessingResult<TEntity>> ProcessMessagesAsync(IEnumerable<MessageData> messages, string batchId, CancellationToken cancellationToken = default);

    public virtual async Task<bool> ValidateMessageAsync(MessageData message, CancellationToken cancellationToken = default)
    {
        try
        {
            Logger.LogDebug("Validating message: {MessageId}", message.MessageId);

            if (string.IsNullOrEmpty(message.MessageId))
            {
                Logger.LogWarning("Message has empty MessageId");
                return false;
            }

            if (string.IsNullOrEmpty(message.Body))
            {
                Logger.LogWarning("Message {MessageId} has empty body", message.MessageId);
                return false;
            }

            return await ValidateMessageContentAsync(message, cancellationToken);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error validating message: {MessageId}", message.MessageId);
            return false;
        }
    }

    /// <summary>
    /// Override this method to provide specific message content validation
    /// </summary>
    protected virtual Task<bool> ValidateMessageContentAsync(MessageData message, CancellationToken cancellationToken)
    {
        return Task.FromResult(true);
    }

    public Dictionary<string, object> GetProcessingStats()
    {
        lock (_statsLock)
        {
            return new Dictionary<string, object>(_processingStats);
        }
    }

    public void ResetProcessingStats()
    {
        lock (_statsLock)
        {
            _processingStats.Clear();
            _processingStats["MessagesProcessed"] = 0;
            _processingStats["MessagesSucceeded"] = 0;
            _processingStats["MessagesFailed"] = 0;
            _processingStats["LastProcessedTime"] = DateTime.UtcNow;
            _processingStats["ProcessorStartTime"] = DateTime.UtcNow;
        }
    }

    /// <summary>
    /// Updates processing statistics
    /// </summary>
    protected void UpdateProcessingStats(int processed, int succeeded, int failed)
    {
        lock (_statsLock)
        {
            _processingStats["MessagesProcessed"] = (int)_processingStats["MessagesProcessed"] + processed;
            _processingStats["MessagesSucceeded"] = (int)_processingStats["MessagesSucceeded"] + succeeded;
            _processingStats["MessagesFailed"] = (int)_processingStats["MessagesFailed"] + failed;
            _processingStats["LastProcessedTime"] = DateTime.UtcNow;
        }
    }

    /// <summary>
    /// Helper method for parsing JSON message bodies
    /// </summary>
    protected static T? ParseJsonMessage<T>(string messageBody) where T : class
    {
        try
        {
            return JsonSerializer.Deserialize<T>(messageBody, new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            });
        }
        catch
        {
            return null;
        }
    }

    /// <summary>
    /// Helper method for parsing strings
    /// </summary>
    protected static string? ParseString(object? value)
    {
        var stringValue = value?.ToString()?.Trim();
        return string.IsNullOrEmpty(stringValue) ? null : stringValue;
    }

    /// <summary>
    /// Helper method for parsing integers
    /// </summary>
    protected static int? ParseInt(object? value)
    {
        if (value == null) return null;
        
        if (value is int intValue) return intValue;
        
        var stringValue = value.ToString()?.Trim();
        return string.IsNullOrEmpty(stringValue) ? null : int.TryParse(stringValue, out var result) ? result : null;
    }

    /// <summary>
    /// Helper method for parsing dates
    /// </summary>
    protected static DateTime? ParseDateTime(object? value)
    {
        if (value == null) return null;
        
        if (value is DateTime dateValue) return dateValue;
        
        var stringValue = value.ToString()?.Trim();
        return string.IsNullOrEmpty(stringValue) ? null : DateTime.TryParse(stringValue, out var result) ? result : null;
    }

    /// <summary>
    /// Helper method for parsing DateOnly
    /// </summary>
    protected static DateOnly? ParseDateOnly(object? value)
    {
        var dateTime = ParseDateTime(value);
        return dateTime.HasValue ? DateOnly.FromDateTime(dateTime.Value) : null;
    }

    /// <summary>
    /// Helper method for extracting message properties
    /// </summary>
    protected static T? GetMessageProperty<T>(MessageData message, string propertyName)
    {
        if (message.Properties.TryGetValue(propertyName, out var value))
        {
            if (value is T typedValue)
                return typedValue;
                
            try
            {
                return (T)Convert.ChangeType(value, typeof(T));
            }
            catch
            {
                return default;
            }
        }
        
        return default;
    }
}
