using Messaging.Core.Models;

namespace Messaging.Core.Abstractions;

/// <summary>
/// Generic interface for processing messages from different messaging platforms
/// </summary>
/// <typeparam name="TEntity">The entity type that this processor creates</typeparam>
public interface IMessageProcessor<TEntity> where TEntity : class
{
    /// <summary>
    /// Gets the processor type identifier
    /// </summary>
    string ProcessorType { get; }

    /// <summary>
    /// Gets the supported message types this processor can handle
    /// </summary>
    string[] SupportedMessageTypes { get; }

    /// <summary>
    /// Gets the messaging platform this processor is designed for
    /// </summary>
    string PlatformName { get; }

    /// <summary>
    /// Processes a batch of messages and returns parsed entities
    /// </summary>
    /// <param name="messages">Messages to process</param>
    /// <param name="batchId">Unique identifier for this processing batch</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Processing result with parsed entities</returns>
    Task<ProcessingResult<TEntity>> ProcessMessagesAsync(IEnumerable<MessageData> messages, string batchId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Validates a single message
    /// </summary>
    /// <param name="message">Message to validate</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>True if message is valid, false otherwise</returns>
    Task<bool> ValidateMessageAsync(MessageData message, CancellationToken cancellationToken = default);

    /// <summary>
    /// Determines if this processor can handle the given message
    /// </summary>
    /// <param name="message">Message to check</param>
    /// <returns>True if this processor can handle the message, false otherwise</returns>
    bool CanProcess(MessageData message);

    /// <summary>
    /// Gets processing statistics for monitoring
    /// </summary>
    /// <returns>Processing statistics</returns>
    Dictionary<string, object> GetProcessingStats();

    /// <summary>
    /// Resets processing statistics
    /// </summary>
    void ResetProcessingStats();
}
