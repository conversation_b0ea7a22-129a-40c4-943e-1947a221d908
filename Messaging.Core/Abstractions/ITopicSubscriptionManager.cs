using Messaging.Core.Models;

namespace Messaging.Core.Abstractions;

/// <summary>
/// Interface for managing topic subscriptions across different messaging platforms
/// </summary>
public interface ITopicSubscriptionManager
{
    /// <summary>
    /// Gets the messaging platform name
    /// </summary>
    string PlatformName { get; }

    /// <summary>
    /// Gets all active topic subscriptions
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Collection of active subscriptions</returns>
    Task<IEnumerable<TopicSubscription>> GetActiveSubscriptionsAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Creates a new topic subscription
    /// </summary>
    /// <param name="subscription">Subscription configuration</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Created subscription details</returns>
    Task<TopicSubscription> CreateSubscriptionAsync(TopicSubscriptionConfig subscription, CancellationToken cancellationToken = default);

    /// <summary>
    /// Updates an existing topic subscription
    /// </summary>
    /// <param name="subscriptionName">Name of the subscription to update</param>
    /// <param name="config">Updated configuration</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Updated subscription details</returns>
    Task<TopicSubscription> UpdateSubscriptionAsync(string subscriptionName, TopicSubscriptionConfig config, CancellationToken cancellationToken = default);

    /// <summary>
    /// Deletes a topic subscription
    /// </summary>
    /// <param name="subscriptionName">Name of the subscription to delete</param>
    /// <param name="cancellationToken">Cancellation token</param>
    Task DeleteSubscriptionAsync(string subscriptionName, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets subscription health and metrics
    /// </summary>
    /// <param name="subscriptionName">Name of the subscription</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Subscription health information</returns>
    Task<SubscriptionHealth> GetSubscriptionHealthAsync(string subscriptionName, CancellationToken cancellationToken = default);

    /// <summary>
    /// Tests if a subscription can receive messages
    /// </summary>
    /// <param name="subscriptionName">Name of the subscription to test</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Test result</returns>
    Task<bool> TestSubscriptionAsync(string subscriptionName, CancellationToken cancellationToken = default);
}

/// <summary>
/// Interface for routing messages based on topics and filters
/// </summary>
public interface IMessageRouter
{
    /// <summary>
    /// Determines which processor should handle a message
    /// </summary>
    /// <param name="message">Message to route</param>
    /// <returns>Processor type that should handle the message</returns>
    string? RouteMessage(MessageData message);

    /// <summary>
    /// Gets all available routing rules
    /// </summary>
    /// <returns>Collection of routing rules</returns>
    IEnumerable<MessageRoutingRule> GetRoutingRules();

    /// <summary>
    /// Adds a new routing rule
    /// </summary>
    /// <param name="rule">Routing rule to add</param>
    void AddRoutingRule(MessageRoutingRule rule);

    /// <summary>
    /// Removes a routing rule
    /// </summary>
    /// <param name="ruleId">ID of the rule to remove</param>
    /// <returns>True if rule was removed, false if not found</returns>
    bool RemoveRoutingRule(string ruleId);

    /// <summary>
    /// Updates an existing routing rule
    /// </summary>
    /// <param name="ruleId">ID of the rule to update</param>
    /// <param name="rule">Updated rule configuration</param>
    /// <returns>True if rule was updated, false if not found</returns>
    bool UpdateRoutingRule(string ruleId, MessageRoutingRule rule);
}

/// <summary>
/// Interface for filtering messages based on content and properties
/// </summary>
public interface IMessageFilter
{
    /// <summary>
    /// Filter name/identifier
    /// </summary>
    string Name { get; }

    /// <summary>
    /// Determines if a message matches this filter
    /// </summary>
    /// <param name="message">Message to evaluate</param>
    /// <returns>True if message matches filter criteria</returns>
    bool Matches(MessageData message);

    /// <summary>
    /// Gets the filter configuration as a dictionary
    /// </summary>
    /// <returns>Filter configuration</returns>
    Dictionary<string, object> GetConfiguration();
}
