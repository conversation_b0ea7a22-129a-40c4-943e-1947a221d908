using Messaging.Core.Models;

namespace Messaging.Core.Abstractions;

/// <summary>
/// Generic interface for messaging operations across different platforms
/// </summary>
public interface IMessagingService
{
    /// <summary>
    /// Gets the messaging platform name (e.g., "AzureServiceBus", "GooglePubSub")
    /// </summary>
    string PlatformName { get; }

    /// <summary>
    /// Receives messages from the messaging system
    /// </summary>
    /// <param name="maxMessages">Maximum number of messages to receive</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Collection of received messages</returns>
    Task<IEnumerable<MessageData>> ReceiveMessagesAsync(int maxMessages, CancellationToken cancellationToken = default);

    /// <summary>
    /// Completes (acknowledges) a message, removing it from the queue/subscription
    /// </summary>
    /// <param name="messageData">Message to complete</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Acknowledgment result</returns>
    Task<AcknowledgmentResult> CompleteMessageAsync(MessageData messageData, CancellationToken cancellationToken = default);

    /// <summary>
    /// Abandons a message, making it available for redelivery
    /// </summary>
    /// <param name="messageData">Message to abandon</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Acknowledgment result</returns>
    Task<AcknowledgmentResult> AbandonMessageAsync(MessageData messageData, CancellationToken cancellationToken = default);

    /// <summary>
    /// Sends a message to the dead letter queue
    /// </summary>
    /// <param name="messageData">Message to dead letter</param>
    /// <param name="reason">Reason for dead lettering</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Acknowledgment result</returns>
    Task<AcknowledgmentResult> DeadLetterMessageAsync(MessageData messageData, string reason, CancellationToken cancellationToken = default);

    /// <summary>
    /// Checks the health of the messaging service
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Health check result</returns>
    Task<HealthCheckResult> CheckHealthAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets connection information for logging/monitoring
    /// </summary>
    /// <returns>Connection details</returns>
    Dictionary<string, object> GetConnectionInfo();

    /// <summary>
    /// Starts the messaging service (if needed)
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    Task StartAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Stops the messaging service (if needed)
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    Task StopAsync(CancellationToken cancellationToken = default);
}
