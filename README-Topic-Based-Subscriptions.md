# Topic-Based Subscriptions and Message Routing

This document describes the advanced topic-based subscription and message routing capabilities added to the messaging solutions.

## 🎯 Overview

The messaging architecture now supports:

- **Multiple Topic Subscriptions** - Subscribe to multiple topics simultaneously
- **Message Routing** - Route messages to different processors based on content/properties
- **Message Filtering** - Filter messages at the subscription level
- **Dynamic Subscription Management** - Create, update, and delete subscriptions programmatically
- **Health Monitoring** - Monitor subscription health and metrics

## 🏗️ Architecture Components

### **Core Abstractions**

```csharp
// Topic subscription management
ITopicSubscriptionManager - Manage topic subscriptions
IMessageRouter - Route messages based on rules
IMessageFilter - Filter messages based on criteria

// Enhanced message data
MessageData.TopicName - Source topic name
MessageData.SubscriptionName - Source subscription name
MessageData.RoutingKey - Message routing key
MessageData.Labels - Message labels for filtering
```

### **Configuration Models**

```csharp
TopicSubscriptionConfig - Subscription configuration
MessageRoutingRule - Routing rule definition
MessageFilterConfig - Message filter configuration
SubscriptionHealth - Health monitoring data
```

## 📋 Configuration Examples

### **Azure Service Bus - Multiple Topics**

```json
{
  "AzureServiceBus": {
    "EnableMultipleTopics": true,
    "TopicSubscriptions": [
      {
        "SubscriptionName": "orders-subscription",
        "TopicName": "orders-topic",
        "MaxConcurrentMessages": 5,
        "AckDeadlineSeconds": 300,
        "MaxDeliveryAttempts": 3,
        "DeadLetterTopic": "orders-deadletter",
        "Filters": [
          {
            "Name": "OrderTypeFilter",
            "FilterType": "sql",
            "Parameters": {
              "Expression": "orderType = 'Premium'"
            },
            "Enabled": true
          }
        ]
      }
    ],
    "Routing": {
      "EnableRouting": true,
      "DefaultProcessorType": "GenericProcessor",
      "Rules": [
        {
          "Name": "OrdersToOrderProcessor",
          "Priority": 100,
          "Conditions": [
            {
              "Field": "TopicName",
              "Operator": "Equals",
              "Value": "orders-topic"
            }
          ],
          "Action": {
            "ActionType": "RouteToProcessor",
            "ProcessorType": "OrderProcessor"
          }
        }
      ]
    }
  }
}
```

### **Google Pub/Sub - Multiple Topics**

```json
{
  "GooglePubSub": {
    "EnableMultipleTopics": true,
    "TopicSubscriptions": [
      {
        "SubscriptionName": "user-events-subscription",
        "TopicName": "user-events-topic",
        "MaxConcurrentMessages": 10,
        "AckDeadlineSeconds": 300,
        "EnableMessageOrdering": true,
        "RetryPolicy": {
          "MaxRetryAttempts": 3,
          "InitialRetryDelaySeconds": 30,
          "MaxRetryDelaySeconds": 600,
          "UseExponentialBackoff": true
        }
      }
    ],
    "Routing": {
      "EnableRouting": true,
      "Rules": [
        {
          "Name": "HighPriorityMessages",
          "Priority": 200,
          "Conditions": [
            {
              "Field": "Labels.priority",
              "Operator": "Equals",
              "Value": "high"
            }
          ],
          "Action": {
            "ActionType": "RouteToProcessor",
            "ProcessorType": "PriorityProcessor"
          }
        }
      ]
    }
  }
}
```

## 🔧 Usage Examples

### **1. Multiple Topic Processing**

```csharp
// Configure multiple topics in appsettings.json
// The service will automatically receive from all configured subscriptions

public class MultiTopicProcessor : BaseMessageProcessor<object>
{
    protected override async Task<ProcessingResult> ProcessMessageAsync(
        MessageData message, 
        CancellationToken cancellationToken)
    {
        // Access topic and subscription information
        var topicName = message.TopicName;
        var subscriptionName = message.SubscriptionName;
        
        // Route based on topic
        return topicName switch
        {
            "orders-topic" => await ProcessOrderMessage(message),
            "notifications-topic" => await ProcessNotificationMessage(message),
            _ => ProcessingResult.Success()
        };
    }
}
```

### **2. Message Routing**

```csharp
// Messages are automatically routed based on configured rules
// Access the routed processor type from message properties

if (message.Properties.TryGetValue("RoutedProcessor", out var processor))
{
    // Handle based on routed processor type
    switch (processor.ToString())
    {
        case "OrderProcessor":
            await ProcessAsOrder(message);
            break;
        case "PriorityProcessor":
            await ProcessWithPriority(message);
            break;
    }
}
```

### **3. Dynamic Subscription Management**

```csharp
public class SubscriptionManager
{
    private readonly ITopicSubscriptionManager _subscriptionManager;

    public async Task CreateOrderSubscriptionAsync()
    {
        var config = new TopicSubscriptionConfig
        {
            SubscriptionName = "new-orders-subscription",
            TopicName = "orders-topic",
            MaxConcurrentMessages = 10,
            AckDeadlineSeconds = 300,
            Filters = new List<MessageFilterConfig>
            {
                new()
                {
                    Name = "RegionFilter",
                    FilterType = "sql",
                    Parameters = { ["Expression"] = "region = 'US'" },
                    Enabled = true
                }
            }
        };

        var subscription = await _subscriptionManager.CreateSubscriptionAsync(config);
        Console.WriteLine($"Created subscription: {subscription.SubscriptionName}");
    }

    public async Task MonitorSubscriptionHealthAsync(string subscriptionName)
    {
        var health = await _subscriptionManager.GetSubscriptionHealthAsync(subscriptionName);
        
        if (!health.IsHealthy)
        {
            Console.WriteLine($"Subscription {subscriptionName} is unhealthy: {health.ErrorMessage}");
        }
        else
        {
            Console.WriteLine($"Subscription {subscriptionName} - Pending: {health.PendingMessages}");
        }
    }
}
```

## 🎛️ Routing Operators

The message router supports various operators for flexible message routing:

| Operator | Description | Example |
|----------|-------------|---------|
| `Equals` | Exact match | `Field: "MessageType", Value: "Order"` |
| `Contains` | Contains substring | `Field: "Body", Value: "urgent"` |
| `StartsWith` | Starts with prefix | `Field: "RoutingKey", Value: "order."` |
| `EndsWith` | Ends with suffix | `Field: "MessageType", Value: ".created"` |
| `Regex` | Regular expression | `Field: "Body", Value: "\\d{4}-\\d{2}-\\d{2}"` |
| `In` | Value in list | `Field: "Priority", Value: "high,critical"` |
| `GreaterThan` | Numeric comparison | `Field: "Priority", Value: "5"` |
| `Exists` | Field exists | `Field: "CustomerId"` |

## 📊 Health Monitoring

### **Subscription Health Metrics**

```csharp
public class HealthMonitor
{
    public async Task CheckAllSubscriptionsAsync()
    {
        var subscriptions = await _subscriptionManager.GetActiveSubscriptionsAsync();
        
        foreach (var subscription in subscriptions)
        {
            var health = await _subscriptionManager.GetSubscriptionHealthAsync(
                subscription.SubscriptionName);
            
            LogHealthMetrics(subscription.SubscriptionName, health);
        }
    }
    
    private void LogHealthMetrics(string subscriptionName, SubscriptionHealth health)
    {
        Console.WriteLine($"""
            Subscription: {subscriptionName}
            Healthy: {health.IsHealthy}
            Pending Messages: {health.PendingMessages}
            Unacknowledged: {health.UnacknowledgedMessages}
            Messages/sec: {health.MessagesPerSecond:F2}
            Avg Processing Time: {health.AverageProcessingTimeMs:F2}ms
            """);
    }
}
```

## 🚀 Deployment Considerations

### **Performance**

- **Concurrent Processing**: Configure `MaxConcurrentMessages` per subscription
- **Message Batching**: Adjust `MaxMessages` for optimal throughput
- **Resource Allocation**: Monitor CPU/memory usage with multiple topics

### **Reliability**

- **Dead Letter Queues**: Configure for failed message handling
- **Retry Policies**: Set appropriate retry delays and max attempts
- **Health Monitoring**: Implement subscription health checks

### **Security**

- **Access Control**: Ensure proper IAM permissions for all topics
- **Message Filtering**: Use filters to prevent unauthorized message processing
- **Audit Logging**: Enable routing decision logging for compliance

## 🔍 Troubleshooting

### **Common Issues**

1. **No Messages Received**
   - Check subscription exists and is active
   - Verify topic permissions
   - Review message filters

2. **Routing Not Working**
   - Enable routing decision logging
   - Check routing rule conditions
   - Verify field names and values

3. **Performance Issues**
   - Monitor concurrent message limits
   - Check acknowledgment deadlines
   - Review processing times

### **Debugging Tools**

```csharp
// Enable detailed logging
"Logging": {
  "LogLevel": {
    "Messaging.Core.Services.MessageRouter": "Debug",
    "Messaging.AzureServiceBus": "Debug",
    "Messaging.GooglePubSub": "Debug"
  }
}

// Test subscription connectivity
await _subscriptionManager.TestSubscriptionAsync("subscription-name");

// Monitor routing decisions
config.Routing.LogRoutingDecisions = true;
```

## 📈 Best Practices

1. **Topic Design**: Use clear, hierarchical topic naming conventions
2. **Subscription Naming**: Include purpose and environment in names
3. **Message Filtering**: Filter at subscription level for efficiency
4. **Routing Rules**: Order rules by priority, most specific first
5. **Health Monitoring**: Implement automated health checks
6. **Error Handling**: Configure dead letter topics for all subscriptions
7. **Testing**: Test routing rules with sample messages before deployment

The enhanced topic-based subscription system provides powerful capabilities for building scalable, flexible messaging architectures! 🎉
