# Complete Multi-Platform Data Processing Architecture

This repository contains two complete data processing solutions that demonstrate the same architectural principles: **separating generic logic from processing logic** to enable **multiple Cloud Run jobs with different schedules to reuse core functionality**.

## 🎯 **Architecture Overview**

Both solutions follow identical design patterns:

### **1. SFTP to PostgreSQL Importer**
- **Generic SFTP logic** separated from **file processing logic**
- Multiple file processors can reuse SFTP operations
- Independent Cloud Run jobs per data source

### **2. Messaging to Database Importer**
- **Generic messaging logic** separated from **platform processing logic**
- Multiple messaging platforms can reuse core operations
- Independent Cloud Run jobs per messaging platform

## 📁 **Project Structure**

```
# SFTP Solution
Sftp.Core/                             # Reusable SFTP + config logic
Sftp.BrightStar/                       # BrightStar file processor
Sftp/                                  # Original monolithic (deprecated)

# Messaging Solution
Messaging.Core/                        # Reusable messaging + config logic
Messaging.AzureServiceBus/             # Azure Service Bus processor
Messaging.GooglePubSub/                # Google Pub/Sub processor

# Shared Infrastructure
Scripts/                               # Deployment scripts for both solutions
```

## 🔄 **Reusable Core Components**

### **SFTP Solution Core**
- `ISftpService` - Generic SFTP operations
- `IFileProcessor<T>` - File processing interface
- `BaseFileProcessor<T>` - Common parsing utilities
- Configuration + Secret management

### **Messaging Solution Core**
- `IMessagingService` - Generic messaging operations
- `IMessageProcessor<T>` - Message processing interface
- `BaseMessageProcessor<T>` - Common parsing utilities
- Configuration + Secret management

## 🎯 **Pluggable Processors**

### **File Processors (SFTP)**
```csharp
// BrightStar file processor
public class BrightStarFileProcessor : BaseFileProcessor<BrightStarCenter>
{
    public override string ProcessorType => "BrightStar_Tadpoles_Center";
    public override string[] SupportedFilePatterns => new[] { "BrightStar_Tadpoles_Center_*.txt" };
    // BrightStar-specific parsing logic
}
```

### **Message Processors (Messaging)**
```csharp
// Azure Service Bus processor
public class AzureMessageProcessor : BaseMessageProcessor<AzureMessageEntity>
{
    public override string ProcessorType => "AzureServiceBus_Generic";
    public override string PlatformName => "AzureServiceBus";
    // Azure-specific message parsing logic
}
```

## 🚀 **Independent Cloud Run Jobs**

### **SFTP Jobs**
```bash
# Deploy BrightStar file processor
./Scripts/build-and-deploy.sh --project-id your-project

# Creates:
# - sftp-brightstar-importer (Cloud Run service)
# - brightstar-daily-import (Cloud Scheduler job)
```

### **Messaging Jobs**
```bash
# Deploy both messaging processors
./Scripts/deploy-messaging.sh --project-id your-project

# Creates:
# - messaging-azure-servicebus (Cloud Run service)
# - messaging-google-pubsub (Cloud Run service)
# - Scheduler jobs for both platforms
```

## 📊 **Benefits Achieved**

### **🔄 Reusable Core Logic**
- SFTP operations shared across file processors
- Messaging operations shared across platforms
- Configuration and secret management centralized
- Database patterns reusable

### **🎯 Pluggable Processors**
- Easy to add new file sources (CSV, XML, JSON)
- Easy to add new messaging platforms (AWS SQS, RabbitMQ)
- Consistent interfaces across all processors
- Independent development and testing

### **🚀 Independent Deployments**
- Each data source gets its own Cloud Run job
- Different schedules per processor
- Independent scaling and resource allocation
- Isolated failures and monitoring

### **🔧 Simplified Maintenance**
- Core logic changes benefit all processors
- Processor-specific changes are isolated
- Clear separation of concerns
- Easy debugging and troubleshooting

## 🛠 **Adding New Processors**

### **Adding a New File Source**
```bash
# 1. Create new project
dotnet new console -n Sftp.NewSource
dotnet add reference ../Sftp.Core/

# 2. Implement processor
public class NewSourceFileProcessor : BaseFileProcessor<NewSourceEntity>
{
    // Implement file-specific parsing logic
}

# 3. Deploy independently
docker build -t sftp-newsource-importer .
gcloud run deploy sftp-newsource-importer
```

### **Adding a New Messaging Platform**
```bash
# 1. Create new project
dotnet new console -n Messaging.AwsSqs
dotnet add reference ../Messaging.Core/

# 2. Implement services
public class AwsSqsService : IMessagingService { /* ... */ }
public class AwsSqsMessageProcessor : BaseMessageProcessor<AwsSqsEntity> { /* ... */ }

# 3. Deploy independently
docker build -t messaging-aws-sqs .
gcloud run deploy messaging-aws-sqs
```

## 📈 **Usage Examples**

### **SFTP Processing**
```bash
# Process local file
cd Sftp.BrightStar
dotnet run /path/to/BrightStar_file.txt

# Process remote files
dotnet run  # Processes all matching files from SFTP
```

### **Message Processing**
```bash
# Process messages once
cd Messaging.AzureServiceBus
dotnet run --once

# Continuous processing
dotnet run
```

## 🔐 **Configuration Management**

Both solutions use the same configuration pattern:

### **Local Development**
- Secrets in `appsettings.json`
- Direct database connections
- Local service configurations

### **Cloud Deployment**
- Secrets in GCP Secret Manager
- Service account authentication
- Production configurations

## 📋 **Deployment Checklist**

### **Setup Secrets**
```bash
# SFTP solution
./Scripts/setup-secrets.sh --project-id your-project

# Messaging solution
./Scripts/setup-messaging-secrets.sh --project-id your-project
```

### **Deploy Services**
```bash
# SFTP solution
./Scripts/build-and-deploy.sh --project-id your-project

# Messaging solution
./Scripts/deploy-messaging.sh --project-id your-project
```

### **Configure Infrastructure**
1. Set up PostgreSQL database
2. Configure SFTP servers (for SFTP solution)
3. Set up Azure Service Bus + Google Pub/Sub (for messaging solution)
4. Configure IAM permissions
5. Test services manually

## 🎉 **Perfect Architecture Match**

Both solutions demonstrate the **exact same architectural principles**:

✅ **Generic logic separated from processing logic**  
✅ **Multiple Cloud Run jobs can reuse core functionality**  
✅ **Different schedules and scaling per processor**  
✅ **Easy to add new data sources/platforms**  
✅ **Local development + cloud deployment ready**  

This architecture provides maximum flexibility, maintainability, and scalability for data processing workloads across different sources and platforms! 🚀

## 📚 **Documentation**

- [SFTP Solution Details](README-Refactored.md)
- [Messaging Solution Details](README-Messaging.md)
- [Original SFTP Implementation](README.md)
