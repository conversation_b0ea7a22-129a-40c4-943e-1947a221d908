# Messaging to Database Importer - Multi-Platform Architecture

A .NET Core 9 solution that processes messages from Azure Service Bus and Google Pub/Sub, storing them in PostgreSQL with full separation of concerns and reusable core logic.

## Architecture Overview

The solution follows the same design principles as the SFTP solution, with generic messaging logic separated from platform-specific processing:

```
MessagingToDatabase.Core/                 # Shared library with generic services
├── Abstractions/                         # Messaging interfaces and base classes
├── Configuration/                        # Configuration models
├── Models/                              # Shared data models (MessageLog, ProcessingResult)
├── Services/                            # Generic services (Configuration, Secrets)

MessagingToDatabase.AzureServiceBus/      # Azure Service Bus implementation
├── Data/                                # Azure message database context
├── Models/                              # Azure-specific models
├── Services/                            # Azure message processor and services
├── Program.cs                           # Azure Service Bus job entry point
├── Dockerfile                           # Azure container configuration

MessagingToDatabase.GooglePubSub/         # Google Pub/Sub implementation
├── Data/                                # Google message database context
├── Models/                              # Google-specific models
├── Services/                            # Google message processor and services
├── Program.cs                           # Google Pub/Sub job entry point
├── Dockerfile                           # Google container configuration

Scripts/                                 # Deployment and setup scripts
```

## Key Benefits

### 🔄 **Reusable Core Logic**
- Generic messaging operations across platforms
- Configuration management with secret loading
- Database import patterns
- Logging and error handling

### 🎯 **Pluggable Message Processors**
- Each messaging platform gets its own processor implementation
- Consistent interface across all processors
- Easy to add new messaging platforms (AWS SQS, RabbitMQ, etc.)

### 🚀 **Independent Deployments**
- Each messaging platform can be deployed as a separate Cloud Run job
- Different schedules for different platforms
- Independent scaling and resource allocation

### 🔧 **Simplified Maintenance**
- Core logic changes benefit all processors
- Platform-specific changes are isolated
- Clear separation of concerns

## Core Abstractions

### IMessagingService

<augment_code_snippet path="MessagingToDatabase.Core/Abstractions/IMessagingService.cs" mode="EXCERPT">
```csharp
public interface IMessagingService
{
    string PlatformName { get; }
    Task<IEnumerable<MessageData>> ReceiveMessagesAsync(int maxMessages, CancellationToken cancellationToken = default);
    Task<AcknowledgmentResult> CompleteMessageAsync(MessageData messageData, CancellationToken cancellationToken = default);
    Task<HealthCheckResult> CheckHealthAsync(CancellationToken cancellationToken = default);
}
```
</augment_code_snippet>

### IMessageProcessor<TEntity>

<augment_code_snippet path="MessagingToDatabase.Core/Abstractions/IMessageProcessor.cs" mode="EXCERPT">
```csharp
public interface IMessageProcessor<TEntity> where TEntity : class
{
    string ProcessorType { get; }
    string[] SupportedMessageTypes { get; }
    Task<ProcessingResult<TEntity>> ProcessMessagesAsync(IEnumerable<MessageData> messages, string batchId, CancellationToken cancellationToken = default);
}
```
</augment_code_snippet>

### BaseMessageProcessor<TEntity>

Provides common functionality for all message processors:
- Message validation logic
- Helper methods for parsing JSON data
- Consistent error handling
- Processing statistics tracking

## Platform Implementations

### Azure Service Bus Implementation

<augment_code_snippet path="MessagingToDatabase.AzureServiceBus/Services/AzureMessageProcessor.cs" mode="EXCERPT">
```csharp
public class AzureMessageProcessor : BaseMessageProcessor<AzureMessageEntity>
{
    public override string ProcessorType => "AzureServiceBus_Generic";
    public override string[] SupportedMessageTypes => new[] { "*" };
    public override string PlatformName => "AzureServiceBus";
    
    // Azure-specific message parsing logic
}
```
</augment_code_snippet>

**Features:**
- Queue and Topic/Subscription support
- Message acknowledgment and abandonment
- Dead letter queue handling
- Connection string authentication

### Google Pub/Sub Implementation

<augment_code_snippet path="MessagingToDatabase.GooglePubSub/Services/GoogleMessageProcessor.cs" mode="EXCERPT">
```csharp
public class GoogleMessageProcessor : BaseMessageProcessor<GoogleMessageEntity>
{
    public override string ProcessorType => "GooglePubSub_Generic";
    public override string[] SupportedMessageTypes => new[] { "*" };
    public override string PlatformName => "GooglePubSub";
    
    // Google Pub/Sub-specific message parsing logic
}
```
</augment_code_snippet>

**Features:**
- Pull-based message consumption
- Message ordering support
- Automatic dead lettering
- Service account authentication

## Independent Deployment

Each processor runs as its own Cloud Run job:

```bash
# Deploy both messaging services
./Scripts/deploy-messaging.sh --project-id your-project

# Creates:
# - messaging-azure-servicebus (Cloud Run service)
# - messaging-google-pubsub (Cloud Run service)
# - Scheduler jobs for automated processing
```

## Adding New Messaging Platforms

To add a new messaging platform (e.g., "AWS SQS"):

1. **Create new project:**
   ```bash
   dotnet new console -n MessagingToDatabase.AwsSqs
   dotnet add reference ../MessagingToDatabase.Core/
   ```

2. **Implement messaging service:**
   ```csharp
   public class AwsSqsService : IMessagingService
   {
       public string PlatformName => "AwsSqs";
       // Implement AWS SQS-specific operations
   }
   ```

3. **Implement message processor:**
   ```csharp
   public class AwsSqsMessageProcessor : BaseMessageProcessor<AwsSqsMessageEntity>
   {
       public override string ProcessorType => "AwsSqs_Generic";
       // Implement AWS SQS-specific parsing logic
   }
   ```

4. **Deploy independently:**
   ```bash
   docker build -t gcr.io/project/messaging-aws-sqs .
   gcloud run deploy messaging-aws-sqs --image=...
   ```

## Configuration Management

### Local Development (appsettings.json)
```json
{
  "MessagingConfiguration": {
    "Environment": "Development",
    "AzureServiceBus": {
      "ConnectionString": "Endpoint=sb://...",
      "QueueName": "test-queue"
    },
    "GooglePubSub": {
      "ProjectId": "local-project",
      "SubscriptionName": "test-subscription"
    }
  }
}
```

### Cloud Deployment (GCP Secret Manager)
```json
{
  "MessagingConfiguration": {
    "Environment": "Production",
    "GcpProjectId": "your-project-id"
    // Connection strings loaded from Secret Manager
  }
}
```

## Deployment

### 1. Setup Secrets
```bash
./Scripts/setup-messaging-secrets.sh --project-id your-project-id
```

### 2. Deploy Services
```bash
./Scripts/deploy-messaging.sh --project-id your-project-id --region us-central1
```

### 3. Configure Schedules
The deployment script automatically creates Cloud Scheduler jobs:
- `azure-servicebus-processor`: Every 15 minutes
- `google-pubsub-processor`: Every 10 minutes

## Usage Examples

### Process Messages Once
```bash
# Azure Service Bus processor
cd MessagingToDatabase.AzureServiceBus
dotnet run --once

# Google Pub/Sub processor
cd MessagingToDatabase.GooglePubSub
dotnet run --once
```

### Continuous Processing
```bash
# Runs continuously, polling for messages
dotnet run
```

### Docker Deployment
```bash
# Build Azure Service Bus image
docker build -f MessagingToDatabase.AzureServiceBus/Dockerfile -t messaging-azure .

# Build Google Pub/Sub image
docker build -f MessagingToDatabase.GooglePubSub/Dockerfile -t messaging-google .
```

## Monitoring and Logging

Each processor logs with its specific context:
- **Azure logs**: Prefixed with "Azure Service Bus"
- **Google logs**: Prefixed with "Google Pub/Sub"
- **Import logs**: Stored in database with `source_system` field
- **Cloud Logging**: Structured logs for each service

### Database Tracking

The `message_logs` table tracks all processing:
- `source_system`: "AzureServiceBus" or "GooglePubSub"
- `processor_type`: Identifies which processor handled the messages
- `batch_id`: Unique identifier for each processing run
- Detailed statistics and error information

### Message Storage

Platform-specific tables store processed messages:
- `azure_messages`: Azure Service Bus messages with business data
- `google_messages`: Google Pub/Sub messages with business data

## Benefits of This Architecture

### For Development Teams
- **Parallel Development**: Teams can work on different messaging platforms independently
- **Focused Testing**: Test only the specific processor being modified
- **Clear Ownership**: Each team owns their processor implementation

### For Operations
- **Independent Scaling**: Scale each platform based on its needs
- **Flexible Scheduling**: Different schedules for different platforms
- **Isolated Failures**: One processor failure doesn't affect others
- **Resource Optimization**: Allocate resources based on platform requirements

### For Maintenance
- **Core Updates**: Bug fixes and improvements benefit all processors
- **Isolated Changes**: Platform-specific changes don't affect others
- **Easy Debugging**: Clear separation makes issues easier to trace
- **Version Management**: Deploy core library updates independently

## Message Processing Flow

1. **Receive**: Pull messages from messaging platform
2. **Process**: Parse and validate message content
3. **Store**: Save to PostgreSQL with business data extraction
4. **Acknowledge**: Complete or abandon messages based on success
5. **Log**: Record processing statistics and errors

## Future Enhancements

- **Dead Letter Processing**: Dedicated processors for failed messages
- **Message Transformation**: ETL pipelines for complex data processing
- **Real-time Analytics**: Stream processing with Apache Kafka
- **Multi-tenant Support**: Separate processing per customer/tenant
- **Message Replay**: Reprocess historical messages
- **Circuit Breakers**: Fault tolerance patterns for external dependencies

This messaging architecture provides the same benefits as the SFTP solution: **generic logic separated from platform-specific processing**, enabling **multiple Cloud Run jobs with different schedules to reuse core functionality**! 🎯
