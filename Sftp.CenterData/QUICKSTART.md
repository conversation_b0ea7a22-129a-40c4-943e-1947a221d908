# CenterData SFTP Processor - Quick Start Guide

## 🚀 Get Started in 5 Minutes

This guide will help you get the CenterData SFTP Processor up and running quickly for development and testing.

## Prerequisites

- ✅ .NET 9 SDK installed
- ✅ PostgreSQL server running
- ✅ Basic knowledge of command line

## Quick Setup

### 1. Run the Setup Script
```bash
# Make the setup script executable and run it
chmod +x setup.sh
./setup.sh
```

This script will:
- ✅ Check prerequisites
- ✅ Create necessary directories
- ✅ Build the project
- ✅ Generate sample configuration files
- ✅ Create sample data for testing
- ✅ Set up Docker configuration

### 2. Configure Database
```bash
# Create the database (update password in the script first)
psql -U postgres -f setup-database.sql

# Or manually create:
createdb worldplanning
psql -d worldplanning -c "CREATE USER centerdata_user WITH PASSWORD 'your_password';"
psql -d worldplanning -c "GRANT ALL PRIVILEGES ON DATABASE worldplanning TO centerdata_user;"
```

### 3. Update Configuration
Edit `appsettings.Development.json`:
```json
{
  "AppConfiguration": {
    "Database": {
      "Password": "your_actual_password"
    },
    "Sftp": {
      "Host": "your-sftp-server.com",
      "Username": "your_username",
      "Password": "your_password"
    }
  }
}
```

### 4. Test with Sample Data
```bash
# Process the sample file
./run-sample.sh

# Or manually:
dotnet run "./sample-data/sample_center_data.txt"
```

### 5. Process Remote Files (Optional)
```bash
# After configuring SFTP settings
./run-remote.sh

# Or manually:
dotnet run
```

## Expected Output

### Successful Processing
```
🚀 Starting CenterData import process for file: ./sample-data/sample_center_data.txt
✅ File headers validated successfully
✅ Successfully processed 2 records from CenterData file
✅ CenterData import completed. Total: 2 inserted, 0 updated, 0 deleted, 0 failed
```

### Database Verification
```sql
-- Check imported data
SELECT center_id, center_name, timezone_name FROM center;

-- Check import logs
SELECT * FROM import_logs ORDER BY start_time DESC LIMIT 5;
```

## File Format Example

Your data files should look like this:
```
School_ID|School_Name|Status|Center_Live_Date|Regions|State|...
12345|Sample Center|Active|2024-01-15|Region North|1.0|...
67890|Another Center|Active|2024-02-01|Region South|2.0|...
```

## Common Commands

### Development
```bash
# Build project
dotnet build

# Run with specific file
dotnet run "/path/to/your/file.txt"

# Run with debug logging
ASPNETCORE_ENVIRONMENT=Development dotnet run

# Clean and rebuild
dotnet clean && dotnet build
```

### Docker
```bash
# Build Docker image
docker build -t centerdata-processor .

# Run in container
docker run -e ASPNETCORE_ENVIRONMENT=Production centerdata-processor
```

### Database Operations
```bash
# Connect to database
psql -h localhost -U centerdata_user -d worldplanning

# View recent imports
psql -d worldplanning -c "SELECT * FROM import_logs ORDER BY start_time DESC LIMIT 10;"

# Count records
psql -d worldplanning -c "SELECT COUNT(*) FROM center;"
```

## Troubleshooting

### Build Issues
```bash
# Clear NuGet cache
dotnet nuget locals all --clear

# Restore packages
dotnet restore

# Rebuild
dotnet clean && dotnet build
```

### Database Connection Issues
```bash
# Test connection
psql -h localhost -U centerdata_user -d worldplanning -c "SELECT 1;"

# Check if database exists
psql -U postgres -l | grep worldplanning
```

### File Processing Issues
- ✅ Verify file encoding is UTF-8
- ✅ Check that file has exactly 35 columns
- ✅ Ensure pipe (|) delimiter is used
- ✅ Validate header row matches expected format

### SFTP Connection Issues
- ✅ Test SFTP connection manually: `sftp username@hostname`
- ✅ Verify credentials in configuration
- ✅ Check firewall and network connectivity
- ✅ Ensure SFTP server allows your IP address

## Key Features Demonstrated

### ✅ Data Import
- Parses pipe-delimited files
- Maps source fields to database columns
- Handles data type conversions

### ✅ Upsert Operations
- Inserts new records
- Updates existing records based on `center_id`
- Maintains audit trail

### ✅ Deletion Logic
- Removes records not present in source data
- Prevents orphaned records
- Logs deletion operations

### ✅ Error Handling
- Continues processing on individual record failures
- Comprehensive error logging
- Transaction rollback on batch failures

### ✅ Monitoring
- Import statistics tracking
- Processing time metrics
- Detailed audit logs

## Next Steps

1. **Production Setup**: Configure GCP Secret Manager for cloud deployment
2. **Scheduling**: Set up automated processing with cron jobs or Cloud Scheduler
3. **Monitoring**: Implement alerting for failed imports
4. **Scaling**: Configure batch sizes for optimal performance
5. **Security**: Implement SSH key authentication for SFTP

## Getting Help

- 📖 **Full Documentation**: See `README.md`
- 🐛 **Issues**: Check application logs and database import_logs table
- 🔧 **Configuration**: Verify all settings in appsettings files
- 🌐 **Connectivity**: Test database and SFTP connections independently

---

**Happy Processing! 🎉**

For detailed documentation and advanced configuration options, please refer to the complete `README.md` file.
