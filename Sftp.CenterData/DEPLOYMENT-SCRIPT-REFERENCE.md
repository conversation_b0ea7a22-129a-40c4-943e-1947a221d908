# Generic Deployment Script Reference

## 📋 Overview

The `deploy-azure-devops.sh` script is a **generic, reusable deployment script** for deploying any SFTP processor to Google Cloud Run. It's designed to be copied and used across multiple SFTP processing projects without modification.

## 🔧 Required Environment Variables

The script requires these environment variables to be set:

| Variable | Description | Example |
|----------|-------------|---------|
| `CLOUD_RUN_JOB_NAME` | Name of the Cloud Run job | `centerdata-sftp-processor` |
| `GCP_PROJECT_ID` | GCP project ID | `mapp-dev-457512` |
| `GCP_REGION` | GCP region for deployment | `us-central1` |
| `GCP_SERVICE_ACCOUNT` | Service account email | `<EMAIL>` |

## 🔐 Optional Environment Variables

These have sensible defaults but can be overridden:

| Variable | Default | Description |
|----------|---------|-------------|
| `SECRET_DB_NAME` | `${CLOUD_RUN_JOB_NAME}-db-connection` | Database connection secret name |
| `SECRET_SFTP_NAME` | `${CLOUD_RUN_JOB_NAME}-sftp-password` | SFTP password secret name |
| `BUILD_ID` | Current timestamp | Build identifier for Docker tags |
| `DOCKER_IMAGE_NAME` | `gcr.io/${GCP_PROJECT_ID}/${CLOUD_RUN_JOB_NAME}` | Full Docker image name |
| `SKIP_TEST` | `false` | Skip deployment testing |

## 🚀 Usage Examples

### From Azure DevOps Pipeline
```yaml
- script: |
    export GCP_PROJECT_ID=$(GCP_PROJECT_ID)
    export GCP_REGION=$(GCP_REGION)
    export GCP_SERVICE_ACCOUNT=$(GCP_SERVICE_ACCOUNT)
    export CLOUD_RUN_JOB_NAME=$(CLOUD_RUN_JOB_NAME)
    export SECRET_DB_NAME=$(SECRET_DB_NAME)
    export SECRET_SFTP_NAME=$(SECRET_SFTP_NAME)
    export BUILD_ID=$(Build.BuildId)
    
    ./deploy-azure-devops.sh
  displayName: 'Deploy Cloud Run Job'
```

### Manual Execution
```bash
# Set required variables
export CLOUD_RUN_JOB_NAME="centerdata-sftp-processor"
export SECRET_DB_NAME="centerdata-db-connection"
export SECRET_SFTP_NAME="centerdata-sftp-password"

# Run deployment
./deploy-azure-devops.sh
```

### Different Processor Example
```bash
# Employee processor
export CLOUD_RUN_JOB_NAME="employee-sftp-processor"
export SECRET_DB_NAME="employee-db-connection"
export SECRET_SFTP_NAME="employee-sftp-password"

./deploy-azure-devops.sh
```

## 📦 What the Script Does

### 1. **Validation**
- ✅ Checks required environment variables
- ✅ Validates GCP authentication
- ✅ Verifies project access

### 2. **GCP Setup**
- ✅ Sets GCP project and region
- ✅ Enables required APIs
- ✅ Configures Docker for GCR

### 3. **Docker Operations**
- ✅ Builds Docker image with build ID tag
- ✅ Tags image as `latest`
- ✅ Pushes both tags to Google Container Registry

### 4. **Cloud Run Deployment**
- ✅ Checks if job exists (update vs create)
- ✅ Deploys with proper configuration:
  - Service account
  - Environment variables
  - Secret Manager integration
  - Resource limits
  - Retry policies
  - Labels

### 5. **Testing & Validation**
- ✅ Executes test run of the deployed job
- ✅ Monitors execution status
- ✅ Reports success/failure

### 6. **Output & Reporting**
- ✅ Deployment summary
- ✅ Management commands
- ✅ Cloud Console links

## 🔄 Reusability Features

### **No Hardcoded Values**
- All job-specific values come from environment variables
- No need to modify the script for different processors

### **Smart Defaults**
- Secret names automatically derived from job name
- Docker image names follow consistent pattern
- Build IDs generated automatically

### **Flexible Configuration**
- Override any default with environment variables
- Support for different secret naming conventions
- Configurable testing behavior

## 📋 File Structure for Multiple Processors

```
├── Sftp.CenterData/
│   ├── deploy-azure-devops.sh          # Copy of generic script
│   ├── azure-pipelines.yml             # CenterData-specific pipeline
│   └── Dockerfile
├── Sftp.Employee/
│   ├── deploy-azure-devops.sh          # Copy of generic script
│   ├── azure-pipelines.yml             # Employee-specific pipeline
│   └── Dockerfile
├── Sftp.Payroll/
│   ├── deploy-azure-devops.sh          # Copy of generic script
│   ├── azure-pipelines.yml             # Payroll-specific pipeline
│   └── Dockerfile
└── azure-pipelines-template.yml        # Template for new processors
```

## 🔧 Customization Points

### **Secret Names**
```bash
# Default pattern
SECRET_DB_NAME="${CLOUD_RUN_JOB_NAME}-db-connection"
SECRET_SFTP_NAME="${CLOUD_RUN_JOB_NAME}-sftp-password"

# Custom names
export SECRET_DB_NAME="custom-database-secret"
export SECRET_SFTP_NAME="custom-sftp-secret"
```

### **Docker Image Names**
```bash
# Default pattern
DOCKER_IMAGE_NAME="gcr.io/${GCP_PROJECT_ID}/${CLOUD_RUN_JOB_NAME}"

# Custom registry
export DOCKER_IMAGE_NAME="us-central1-docker.pkg.dev/project/repo/image"
```

### **Resource Configuration**
The script uses these Cloud Run settings:
- **Memory**: 1Gi
- **CPU**: 1
- **Timeout**: 3600 seconds (1 hour)
- **Max Retries**: 3
- **Parallelism**: 1

To customize, modify the `gcloud run jobs create` command in the script.

## 🎯 Benefits

### **✅ Consistency**
- Same deployment process for all SFTP processors
- Consistent naming conventions
- Standardized configuration

### **✅ Maintainability**
- Single script to maintain
- Changes benefit all processors
- Reduced code duplication

### **✅ Scalability**
- Easy to add new processors
- Template-based approach
- Minimal setup for new jobs

### **✅ Reliability**
- Tested deployment process
- Built-in validation
- Comprehensive error handling

## 🔍 Troubleshooting

### **Missing Environment Variables**
```bash
❌ Error: Required environment variables not set
Required: GCP_PROJECT_ID, GCP_REGION, GCP_SERVICE_ACCOUNT, CLOUD_RUN_JOB_NAME
```
**Solution**: Set all required environment variables before running the script.

### **Secret Not Found**
```bash
❌ Error: Secret 'your-secret-name' not found
```
**Solution**: Create the secret in Secret Manager or update the secret name.

### **Permission Denied**
```bash
❌ Error: Permission denied for service account
```
**Solution**: Verify service account has required roles (run.admin, storage.admin, secretmanager.secretAccessor).

## 📞 Support

For script issues:
1. Check environment variables are set correctly
2. Verify GCP authentication and permissions
3. Ensure secrets exist in Secret Manager
4. Review Cloud Run job logs for deployment issues
