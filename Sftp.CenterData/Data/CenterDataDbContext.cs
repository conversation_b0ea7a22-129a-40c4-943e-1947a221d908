using Microsoft.EntityFrameworkCore;
using Sftp.CenterData.Models;
using Sftp.Core.Models;

namespace Sftp.CenterData.Data;

/// <summary>
/// Entity Framework DbContext for managing CenterData childcare center data and import operations.
/// Provides data access for CenterData center entities and shared import logging functionality.
/// </summary>
/// <remarks>
/// This context manages two primary entities:
/// - Center: Stores comprehensive childcare center information including locations, contacts, and emergency data
/// - ImportLog: Tracks import operations, batch information, and processing statistics
///
/// The context includes optimized database indexes for common query patterns including:
/// - CenterData identification and lookup by school ID
/// - Status-based filtering for operational centers
/// - Geographic queries by state and region
/// - Import batch tracking and audit trails
/// - Time-based queries for reporting and monitoring
///
/// **Database Optimization:**
/// - Unique constraints to prevent duplicate center records
/// - Performance indexes on frequently queried fields
/// - Proper column naming conventions for PostgreSQL
/// - Efficient batch processing support
///
/// This context is specifically designed for CenterData Tadpoles Center data
/// and provides the foundation for center management and reporting operations.
/// </remarks>
public class CenterDataDbContext : DbContext
{
    /// <summary>
    /// Initializes a new instance of the CenterDataDbContext with the specified options.
    /// </summary>
    /// <param name="options">The options to be used by the DbContext. Contains connection string and provider configuration.</param>
    public CenterDataDbContext(DbContextOptions<CenterDataDbContext> options) : base(options)
    {
    }

    /// <summary>
    /// Gets or sets the DbSet for childcare center entities.
    /// Represents comprehensive center information including locations, contacts, staff, and emergency procedures.
    /// </summary>
    /// <value>
    /// A DbSet containing Center records that store:
    /// - Basic center identification and operational information
    /// - Complete address and contact details
    /// - Staff and management contact information
    /// - Primary and secondary evacuation location details
    /// - Emergency contact numbers and procedures
    /// - Import tracking and audit information
    /// </value>
    public DbSet<Center> Centers { get; set; }

    /// <summary>
    /// Gets or sets the DbSet for import operation logs.
    /// Tracks processing operations, batch statistics, and error information for all import processes.
    /// </summary>
    /// <value>
    /// A DbSet containing ImportLog records that store:
    /// - Import batch identification and tracking
    /// - File processing statistics and metrics
    /// - Processing duration and status information
    /// - Error messages and troubleshooting data
    /// - Processor type identification for multi-processor environments
    /// </value>
    public DbSet<ImportLog> ImportLogs { get; set; }

    /// <summary>
    /// Configures the database model and relationships when the model is being created.
    /// Sets up indexes, constraints, and database-specific configurations for optimal query performance.
    /// </summary>
    /// <param name="modelBuilder">The builder being used to construct the model for this context.</param>
    /// <remarks>
    /// This method configures:
    ///
    /// For Center:
    /// - Performance indexes on school ID, status, and state for efficient querying
    /// - Import batch tracking indexes for data lineage
    /// - Time-based indexes for reporting and audit queries
    /// - Unique constraint on SchoolId to prevent duplicate center records
    ///
    /// For ImportLog:
    /// - Batch processing indexes for tracking and monitoring
    /// - Status and timing indexes for operational dashboards
    /// - Processor type indexes for multi-processor environments
    ///
    /// All indexes use explicit database names following the naming convention:
    /// - ix_{table_name}_{column_name} for regular indexes
    /// - uk_{table_name}_{column_name} for unique constraints
    /// </remarks>
    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);

        // Configure DateTime properties to use UTC
        foreach (var entityType in modelBuilder.Model.GetEntityTypes())
        {
            foreach (var property in entityType.GetProperties())
            {
                if (property.ClrType == typeof(DateTime))
                {
                    property.SetValueConverter(new Microsoft.EntityFrameworkCore.Storage.ValueConversion.ValueConverter<DateTime, DateTime>(
                        v => v.Kind == DateTimeKind.Unspecified ? DateTime.SpecifyKind(v, DateTimeKind.Utc) : v.ToUniversalTime(),
                        v => DateTime.SpecifyKind(v, DateTimeKind.Utc)));
                }
                else if (property.ClrType == typeof(DateTime?))
                {
                    property.SetValueConverter(new Microsoft.EntityFrameworkCore.Storage.ValueConversion.ValueConverter<DateTime?, DateTime?>(
                        v => v.HasValue ? (v.Value.Kind == DateTimeKind.Unspecified ? DateTime.SpecifyKind(v.Value, DateTimeKind.Utc) : v.Value.ToUniversalTime()) : v,
                        v => v.HasValue ? DateTime.SpecifyKind(v.Value, DateTimeKind.Utc) : v));
                }
            }
        }

        // Configure Center
        modelBuilder.Entity<Center>(entity =>
        {
            entity.HasIndex(e => e.CenterId)
                .HasDatabaseName("ix_center_center_id");

            entity.HasIndex(e => e.CenterName)
                .HasDatabaseName("ix_center_center_name");

            entity.HasIndex(e => e.State)
                .HasDatabaseName("ix_center_state");

            entity.HasIndex(e => e.ImportBatchId)
                .HasDatabaseName("ix_center_import_batch_id");

            entity.HasIndex(e => e.Regions)
                .HasDatabaseName("ix_center_regions");

            // Unique constraint on CenterId to prevent duplicates
            entity.HasIndex(e => e.CenterId)
                .IsUnique()
                .HasDatabaseName("uk_center_center_id");
        });

        // Configure ImportLog
        modelBuilder.Entity<ImportLog>(entity =>
        {
            entity.HasIndex(e => e.BatchId)
                .HasDatabaseName("ix_import_logs_batch_id");

            entity.HasIndex(e => e.Status)
                .HasDatabaseName("ix_import_logs_status");

            entity.HasIndex(e => e.StartTime)
                .HasDatabaseName("ix_import_logs_start_time");

            entity.HasIndex(e => e.CreatedAt)
                .HasDatabaseName("ix_import_logs_created_at");

            entity.HasIndex(e => e.ProcessorType)
                .HasDatabaseName("ix_import_logs_processor_type");
        });
    }
}
