#!/bin/bash

# Test Docker Build Script for Sftp.CenterData
# This script tests the Docker build process before running the full deployment

set -e

echo "🧪 Testing Docker build for Sftp.CenterData"
echo "============================================"

# Configuration
PROJECT_ID="mapp-dev-457512"
REGION="us-central1"
JOB_NAME="centerdata-sftp-processor"
REPOSITORY_NAME="sftp-processors"
IMAGE_NAME="${REGION}-docker.pkg.dev/${PROJECT_ID}/${REPOSITORY_NAME}/${JOB_NAME}"
TEST_TAG="test-$(date +%Y%m%d-%H%M%S)"

echo "📋 Build Configuration:"
echo "  Project: ${PROJECT_ID}"
echo "  Image: ${IMAGE_NAME}:${TEST_TAG}"
echo "  Build Context: $(pwd)/.."
echo "  Dockerfile: $(pwd)/Dockerfile"
echo ""

# Check prerequisites
echo "🔍 Checking prerequisites..."

# Check if we're in the right directory
if [[ ! -f "Dockerfile" ]]; then
    echo "❌ Error: Dockerfile not found in current directory"
    echo "   Make sure you're running this from the Sftp.CenterData directory"
    exit 1
fi

# Check if Sftp.Core exists
if [[ ! -d "../Sftp.Core" ]]; then
    echo "❌ Error: Sftp.Core project not found"
    echo "   Expected to find ../Sftp.Core directory"
    exit 1
fi

# Check if project files exist
if [[ ! -f "Sftp.CenterData.csproj" ]]; then
    echo "❌ Error: Sftp.CenterData.csproj not found"
    exit 1
fi

if [[ ! -f "../Sftp.Core/Sftp.Core.csproj" ]]; then
    echo "❌ Error: ../Sftp.Core/Sftp.Core.csproj not found"
    exit 1
fi

echo "✅ All prerequisites found"
echo ""

# Test Docker build
echo "🐳 Testing Docker build..."
echo "Command: docker build -f Dockerfile -t ${IMAGE_NAME}:${TEST_TAG} .."

if docker build -f Dockerfile -t ${IMAGE_NAME}:${TEST_TAG} ..; then
    echo "✅ Docker build successful!"
    echo ""
    
    # Show image details
    echo "📊 Image details:"
    docker images ${IMAGE_NAME}:${TEST_TAG}
    echo ""
    
    # Test container run (dry run)
    echo "🧪 Testing container startup..."
    if docker run --rm ${IMAGE_NAME}:${TEST_TAG} --help >/dev/null 2>&1; then
        echo "✅ Container starts successfully"
    else
        echo "⚠️  Container startup test failed (this might be expected if --help is not implemented)"
    fi
    
    # Cleanup test image
    echo ""
    echo "🧹 Cleaning up test image..."
    docker rmi ${IMAGE_NAME}:${TEST_TAG}
    
    echo ""
    echo "🎉 Docker build test completed successfully!"
    echo ""
    echo "📋 Next steps:"
    echo "1. Run the full deployment: ./deploy-cloud-run.sh"
    echo "2. Or test locally: docker run --rm -it ${IMAGE_NAME}:latest"
    
else
    echo "❌ Docker build failed!"
    echo ""
    echo "🔧 Troubleshooting tips:"
    echo "1. Check that all project files are present"
    echo "2. Verify .NET 9.0 SDK is available in the build environment"
    echo "3. Check for any compilation errors in the projects"
    echo "4. Review the Dockerfile for any syntax issues"
    exit 1
fi
