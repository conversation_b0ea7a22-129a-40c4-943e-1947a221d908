-- =====================================================
-- CenterData SFTP Processor - Database Test Script
-- =====================================================
-- 
-- This script tests the database setup and validates
-- that all components are working correctly.
--
-- Prerequisites:
-- - Database setup completed (run 00_SetupDatabase.sql first)
-- - Connected to worldplanning database
--
-- Usage:
--   psql -d worldplanning -f Scripts/99_TestDatabase.sql
--
-- =====================================================

-- Set client encoding and timezone
SET client_encoding = 'UTF8';
SET timezone = 'UTC';

\echo ''
\echo '🧪 CenterData SFTP Processor - Database Tests'
\echo '============================================='
\echo ''

-- =====================================================
-- Test 1: Verify tables exist
-- =====================================================

\echo '📋 Test 1: Verifying tables exist...'

SELECT 
    table_name,
    table_type,
    CASE 
        WHEN table_name IN ('center', 'import_logs') THEN '✅ Required table found'
        ELSE '⚠️  Unexpected table'
    END as status
FROM information_schema.tables 
WHERE table_schema = 'public' 
AND table_type = 'BASE TABLE'
ORDER BY table_name;

-- =====================================================
-- Test 2: Verify indexes exist
-- =====================================================

\echo ''
\echo '📋 Test 2: Verifying indexes exist...'

SELECT 
    tablename,
    indexname,
    '✅ Index found' as status
FROM pg_indexes 
WHERE schemaname = 'public' 
AND tablename IN ('center', 'import_logs')
ORDER BY tablename, indexname;

-- =====================================================
-- Test 3: Verify constraints exist
-- =====================================================

\echo ''
\echo '📋 Test 3: Verifying constraints exist...'

SELECT 
    tc.table_name,
    tc.constraint_name,
    tc.constraint_type,
    '✅ Constraint found' as status
FROM information_schema.table_constraints tc
WHERE tc.table_schema = 'public'
AND tc.table_name IN ('center', 'import_logs')
ORDER BY tc.table_name, tc.constraint_type, tc.constraint_name;

-- =====================================================
-- Test 4: Verify views exist
-- =====================================================

\echo ''
\echo '📋 Test 4: Verifying views exist...'

SELECT 
    table_name as view_name,
    '✅ View found' as status
FROM information_schema.views 
WHERE table_schema = 'public'
ORDER BY table_name;

-- =====================================================
-- Test 5: Test data insertion
-- =====================================================

\echo ''
\echo '📋 Test 5: Testing data insertion...'

-- Begin test transaction
BEGIN;

-- Insert test data into center
INSERT INTO center (
    center_id, center_name, timezone_name, center_live_date, regions, state,
    center_email, center_director_name, center_director_email,
    dvp_name, dvp_email, regional_manager_name, regional_manager_email,
    is_using_assessment, is_using_planning, import_batch_id
) VALUES (
    99999, 
    'Test Center for Database Validation', 
    'America/New_York', 
    CURRENT_TIMESTAMP, 
    'Test Region', 
    99.0,
    '<EMAIL>', 
    'Test Director', 
    '<EMAIL>',
    'Test VP', 
    '<EMAIL>', 
    'Test Manager', 
    '<EMAIL>',
    true, 
    true, 
    'TEST-BATCH-001'
);

-- Insert test data into import_logs
INSERT INTO import_logs (
    batch_id, file_name, file_path, processor_type,
    start_time, end_time, file_size,
    records_processed, records_inserted, records_updated, records_deleted, records_failed,
    status
) VALUES (
    'TEST-BATCH-001',
    'test_validation_file.txt',
    './test/test_validation_file.txt',
    'CenterData_Tadpoles_Center',
    CURRENT_TIMESTAMP - INTERVAL '2 minutes',
    CURRENT_TIMESTAMP - INTERVAL '1 minute',
    512,
    1, 1, 0, 0, 0,
    'Completed'
);

-- Verify insertions
SELECT 
    'center' as table_name,
    COUNT(*) as test_records_inserted,
    '✅ Insert successful' as status
FROM center 
WHERE import_batch_id = 'TEST-BATCH-001';

SELECT 
    'import_logs' as table_name,
    COUNT(*) as test_records_inserted,
    '✅ Insert successful' as status
FROM import_logs 
WHERE batch_id = 'TEST-BATCH-001';

-- =====================================================
-- Test 6: Test update operations
-- =====================================================

\echo ''
\echo '📋 Test 6: Testing update operations...'

-- Update test record
UPDATE center 
SET center_name = 'Updated Test Center for Database Validation',
    is_using_assessment = false
WHERE center_id = 99999;

-- Verify update and automatic timestamp update
SELECT 
    center_id,
    center_name,
    is_using_assessment,
    CASE 
        WHEN updated_at > created_at THEN '✅ Automatic timestamp update working'
        ELSE '❌ Automatic timestamp update failed'
    END as timestamp_test
FROM center 
WHERE center_id = 99999;

-- =====================================================
-- Test 7: Test monitoring views
-- =====================================================

\echo ''
\echo '📋 Test 7: Testing monitoring views...'

-- Test recent imports view
SELECT 
    batch_id,
    file_name,
    processor_type,
    duration_seconds,
    records_processed,
    result_summary,
    '✅ View query successful' as status
FROM v_recent_imports 
WHERE batch_id = 'TEST-BATCH-001';

-- =====================================================
-- Test 8: Test constraints
-- =====================================================

\echo ''
\echo '📋 Test 8: Testing constraints...'

-- Test positive center_id constraint
DO $$
BEGIN
    INSERT INTO center (center_id, center_name) VALUES (-1, 'Invalid Center');
    RAISE EXCEPTION 'Constraint test failed - negative center_id was allowed';
EXCEPTION 
    WHEN check_violation THEN
        RAISE NOTICE '✅ Positive center_id constraint working correctly';
    WHEN OTHERS THEN
        RAISE NOTICE '❌ Unexpected error in constraint test: %', SQLERRM;
END $$;

-- Test status constraint on import_logs
DO $$
BEGIN
    INSERT INTO import_logs (batch_id, status) VALUES ('TEST-INVALID', 'InvalidStatus');
    RAISE EXCEPTION 'Constraint test failed - invalid status was allowed';
EXCEPTION 
    WHEN check_violation THEN
        RAISE NOTICE '✅ Status constraint working correctly';
    WHEN OTHERS THEN
        RAISE NOTICE '❌ Unexpected error in status constraint test: %', SQLERRM;
END $$;

-- =====================================================
-- Test 9: Test permissions
-- =====================================================

\echo ''
\echo '📋 Test 9: Testing permissions...'

-- Check table privileges for centerdata_user
SELECT 
    table_name,
    privilege_type,
    '✅ Permission granted' as status
FROM information_schema.table_privileges 
WHERE grantee = 'centerdata_user'
AND table_name IN ('center', 'import_logs')
ORDER BY table_name, privilege_type;

-- =====================================================
-- Cleanup test data
-- =====================================================

\echo ''
\echo '📋 Cleaning up test data...'

-- Delete test records
DELETE FROM center WHERE center_id = 99999;
DELETE FROM import_logs WHERE batch_id = 'TEST-BATCH-001';

-- Rollback test transaction
ROLLBACK;

\echo '✅ Test data cleaned up'

-- =====================================================
-- Test Summary
-- =====================================================

\echo ''
\echo '📊 Test Summary'
\echo '==============='
\echo ''

-- Count tables
SELECT 
    'Tables' as component,
    COUNT(*) as count,
    '✅ All required tables present' as status
FROM information_schema.tables 
WHERE table_schema = 'public' 
AND table_name IN ('center', 'import_logs');

-- Count indexes
SELECT 
    'Indexes' as component,
    COUNT(*) as count,
    '✅ Performance indexes created' as status
FROM pg_indexes 
WHERE schemaname = 'public' 
AND tablename IN ('center', 'import_logs');

-- Count constraints
SELECT 
    'Constraints' as component,
    COUNT(*) as count,
    '✅ Data integrity constraints active' as status
FROM information_schema.table_constraints 
WHERE table_schema = 'public'
AND table_name IN ('center', 'import_logs');

-- Count views
SELECT 
    'Views' as component,
    COUNT(*) as count,
    '✅ Monitoring views available' as status
FROM information_schema.views 
WHERE table_schema = 'public';

-- Count permissions
SELECT 
    'Permissions' as component,
    COUNT(DISTINCT table_name) as tables_with_permissions,
    '✅ Application user has required access' as status
FROM information_schema.table_privileges 
WHERE grantee = 'centerdata_user'
AND table_name IN ('center', 'import_logs');

-- =====================================================
-- Final validation
-- =====================================================

\echo ''
\echo '🎯 Final Validation'
\echo '=================='
\echo ''

-- Check if database is ready for application use
WITH validation_summary AS (
    SELECT 
        (SELECT COUNT(*) FROM information_schema.tables 
         WHERE table_schema = 'public' AND table_name IN ('center', 'import_logs')) as tables_count,
        (SELECT COUNT(*) FROM pg_indexes 
         WHERE schemaname = 'public' AND tablename IN ('center', 'import_logs')) as indexes_count,
        (SELECT COUNT(DISTINCT table_name) FROM information_schema.table_privileges 
         WHERE grantee = 'centerdata_user' AND table_name IN ('center', 'import_logs')) as permissions_count
)
SELECT 
    CASE 
        WHEN tables_count = 2 AND indexes_count >= 10 AND permissions_count = 2 THEN 
            '🎉 Database is ready for CenterData SFTP Processor!'
        ELSE 
            '❌ Database setup incomplete - please review test results above'
    END as final_status,
    tables_count,
    indexes_count,
    permissions_count
FROM validation_summary;

\echo ''
\echo '✅ Database testing completed!'
\echo ''
\echo 'Next steps:'
\echo '1. Update application configuration with database connection details'
\echo '2. Test application connectivity: dotnet run --test-db'
\echo '3. Process sample data: dotnet run "./sample-data/sample_center_data.txt"'
\echo '4. Monitor operations: SELECT * FROM v_recent_imports;'
\echo ''
