-- =====================================================
-- CenterData SFTP Processor - Database Schema Script
-- =====================================================
-- 
-- This script creates the center table and related database objects
-- for the CenterData SFTP Processor application.
--
-- Prerequisites:
-- - PostgreSQL 12+ database server
-- - Database and user already created
-- - Appropriate permissions granted
--
-- Usage:
--   psql -d worldplanning -f Scripts/01_CreateCenterTable.sql
--
-- =====================================================

-- Set client encoding and timezone
SET client_encoding = 'UTF8';
SET timezone = 'UTC';

-- Begin transaction
BEGIN;

-- =====================================================
-- Drop existing objects (if they exist)
-- =====================================================

-- Drop indexes first (if they exist)
DROP INDEX IF EXISTS ix_center_center_id;
DROP INDEX IF EXISTS ix_center_center_name;
DROP INDEX IF EXISTS ix_center_state;
DROP INDEX IF EXISTS ix_center_regions;
DROP INDEX IF EXISTS ix_center_import_batch_id;
DROP INDEX IF EXISTS uk_center_center_id;

-- Drop table (if it exists)
DROP TABLE IF EXISTS center CASCADE;

-- =====================================================
-- Create center table
-- =====================================================

CREATE TABLE center (
    -- Primary key
    id                              SERIAL PRIMARY KEY,
    
    -- Core identification fields
    center_id                       BIGINT NOT NULL,
    center_name                     TEXT,
    
    -- Operational information
    timezone_name                   TEXT,
    center_live_date                TIMESTAMP,
    regions                         TEXT,
    state                           DOUBLE PRECISION,
    
    -- Audit fields
    edited_by                       TEXT,
    created_by                      DOUBLE PRECISION,
    edited_date                     TEXT,
    created_date                    DOUBLE PRECISION,
    
    -- Business logic fields
    center_type_enum                BIGINT,
    center_type_enum_placeholder    BIGINT,
    
    -- Contact information
    center_email                    TEXT,
    center_director_name            TEXT,
    center_director_email           TEXT,
    dvp_name                        TEXT,
    dvp_email                       TEXT,
    regional_manager_name           TEXT,
    regional_manager_email          TEXT,
    
    -- Feature flags
    is_using_assessment             BOOLEAN NOT NULL DEFAULT FALSE,
    is_using_planning               BOOLEAN NOT NULL DEFAULT FALSE,
    
    -- Import tracking
    import_batch_id                 TEXT,
    
    -- Timestamps (automatically managed)
    created_at                      TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at                      TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- =====================================================
-- Add table comments
-- =====================================================

COMMENT ON TABLE center IS 'Stores childcare center data imported from CenterData Tadpoles Center files';

-- Column comments
COMMENT ON COLUMN center.id IS 'Primary key - auto-incrementing identifier';
COMMENT ON COLUMN center.center_id IS 'Unique center identifier from source system (School_ID)';
COMMENT ON COLUMN center.center_name IS 'Official name of the childcare center (School_Name)';
COMMENT ON COLUMN center.timezone_name IS 'Timezone in which the center operates';
COMMENT ON COLUMN center.center_live_date IS 'Date when the center became operational';
COMMENT ON COLUMN center.regions IS 'Geographic regions or territories served';
COMMENT ON COLUMN center.state IS 'State identifier as double precision';
COMMENT ON COLUMN center.edited_by IS 'User who last edited this record';
COMMENT ON COLUMN center.created_by IS 'User who created this record';
COMMENT ON COLUMN center.edited_date IS 'Date when record was last edited';
COMMENT ON COLUMN center.created_date IS 'Date when record was created';
COMMENT ON COLUMN center.center_type_enum IS 'Center type enumeration value';
COMMENT ON COLUMN center.center_type_enum_placeholder IS 'Additional center type categorization';
COMMENT ON COLUMN center.center_email IS 'Primary email address for the center';
COMMENT ON COLUMN center.center_director_name IS 'Full name of the center director';
COMMENT ON COLUMN center.center_director_email IS 'Email address of the center director';
COMMENT ON COLUMN center.dvp_name IS 'Divisional Vice President name';
COMMENT ON COLUMN center.dvp_email IS 'Divisional Vice President email';
COMMENT ON COLUMN center.regional_manager_name IS 'Regional manager name';
COMMENT ON COLUMN center.regional_manager_email IS 'Regional manager email';
COMMENT ON COLUMN center.is_using_assessment IS 'Whether center uses assessment functionality';
COMMENT ON COLUMN center.is_using_planning IS 'Whether center uses planning functionality';
COMMENT ON COLUMN center.import_batch_id IS 'Batch identifier for import tracking';
COMMENT ON COLUMN center.created_at IS 'Timestamp when record was created';
COMMENT ON COLUMN center.updated_at IS 'Timestamp when record was last updated';

-- =====================================================
-- Create indexes for performance
-- =====================================================

-- Primary business key index (most important)
CREATE UNIQUE INDEX uk_center_center_id 
    ON center (center_id);

-- Performance indexes for common queries
CREATE INDEX ix_center_center_name 
    ON center (center_name);

CREATE INDEX ix_center_state 
    ON center (state);

CREATE INDEX ix_center_regions 
    ON center (regions);

-- Import tracking index
CREATE INDEX ix_center_import_batch_id 
    ON center (import_batch_id);

-- Timestamp indexes for audit queries
CREATE INDEX ix_center_created_at 
    ON center (created_at);

CREATE INDEX ix_center_updated_at 
    ON center (updated_at);

-- Composite index for common filtering
CREATE INDEX ix_center_state_regions 
    ON center (state, regions);

-- =====================================================
-- Add constraints
-- =====================================================

-- Check constraints for data integrity
ALTER TABLE center 
    ADD CONSTRAINT chk_center_center_id_positive 
    CHECK (center_id > 0);

-- Email format constraints (basic validation)
ALTER TABLE center 
    ADD CONSTRAINT chk_center_center_email_format 
    CHECK (center_email IS NULL OR center_email ~* '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$');

ALTER TABLE center 
    ADD CONSTRAINT chk_center_director_email_format 
    CHECK (center_director_email IS NULL OR center_director_email ~* '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$');

ALTER TABLE center 
    ADD CONSTRAINT chk_center_dvp_email_format 
    CHECK (dvp_email IS NULL OR dvp_email ~* '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$');

ALTER TABLE center 
    ADD CONSTRAINT chk_center_manager_email_format 
    CHECK (regional_manager_email IS NULL OR regional_manager_email ~* '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$');

-- =====================================================
-- Create trigger for automatic updated_at timestamp
-- =====================================================

-- Function to update the updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Trigger to automatically update updated_at on row changes
CREATE TRIGGER tr_center_updated_at
    BEFORE UPDATE ON center
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- =====================================================
-- Grant permissions
-- =====================================================

-- Grant permissions to the application user
GRANT SELECT, INSERT, UPDATE, DELETE ON center TO centerdata_user;
GRANT USAGE, SELECT ON SEQUENCE center_id_seq TO centerdata_user;

-- =====================================================
-- Create sample data (optional - for testing)
-- =====================================================

-- Uncomment the following section to insert sample data for testing

/*
INSERT INTO center (
    center_id, center_name, timezone_name, center_live_date, regions, state,
    center_email, center_director_name, center_director_email,
    dvp_name, dvp_email, regional_manager_name, regional_manager_email,
    is_using_assessment, is_using_planning, import_batch_id
) VALUES 
(
    12345, 
    'Sample Childcare Center', 
    'America/New_York', 
    '2024-01-15 00:00:00', 
    'Region North', 
    1.0,
    '<EMAIL>', 
    'Jane Director', 
    '<EMAIL>',
    'Sarah VP', 
    '<EMAIL>', 
    'John Manager', 
    '<EMAIL>',
    false, 
    false, 
    'SAMPLE-BATCH-001'
),
(
    67890, 
    'Another Test Center', 
    'America/Chicago', 
    '2024-02-01 00:00:00', 
    'Region South', 
    2.0,
    '<EMAIL>', 
    'Bob Director', 
    '<EMAIL>',
    'Mike VP', 
    '<EMAIL>', 
    'Alice Manager', 
    '<EMAIL>',
    true, 
    true, 
    'SAMPLE-BATCH-001'
);
*/

-- =====================================================
-- Verify table creation
-- =====================================================

-- Display table information
SELECT 
    'center table created successfully' as status,
    COUNT(*) as initial_record_count
FROM center;

-- Display index information
SELECT 
    schemaname,
    tablename,
    indexname,
    indexdef
FROM pg_indexes 
WHERE tablename = 'center'
ORDER BY indexname;

-- Commit transaction
COMMIT;

-- =====================================================
-- Success message
-- =====================================================

\echo ''
\echo '✅ Center table creation completed successfully!'
\echo ''
\echo 'Table created: center'
\echo 'Indexes created: 8 performance and constraint indexes'
\echo 'Constraints added: Email validation and business rules'
\echo 'Triggers created: Automatic updated_at timestamp'
\echo 'Permissions granted: centerdata_user access'
\echo ''
\echo 'Next steps:'
\echo '1. Verify table structure: \\d center'
\echo '2. Test application connectivity'
\echo '3. Run sample data import'
\echo ''
