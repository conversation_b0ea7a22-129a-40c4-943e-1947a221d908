-- =====================================================
-- CenterData SFTP Processor - Complete Database Setup
-- =====================================================
-- 
-- This master script sets up the complete database schema
-- for the CenterData SFTP Processor application.
--
-- What this script does:
-- 1. Creates database and user (if needed)
-- 2. Sets up proper permissions
-- 3. Creates all required tables
-- 4. Creates indexes and constraints
-- 5. Sets up monitoring views
-- 6. Validates the setup
--
-- Prerequisites:
-- - PostgreSQL 12+ database server
-- - Superuser access (postgres user)
--
-- Usage:
--   # Run as postgres superuser
--   psql -U postgres -f Scripts/00_SetupDatabase.sql
--
--   # Or run individual scripts:
--   psql -U postgres -f Scripts/00_SetupDatabase.sql
--   psql -d worldplanning -f Scripts/01_CreateCenterTable.sql
--   psql -d worldplanning -f Scripts/02_CreateImportLogsTable.sql
--
-- =====================================================

-- Set client encoding and timezone
SET client_encoding = 'UTF8';
SET timezone = 'UTC';

-- Display setup information
\echo ''
\echo '🚀 CenterData SFTP Processor - Database Setup'
\echo '=============================================='
\echo ''
\echo 'Setting up complete database schema...'
\echo ''

-- =====================================================
-- Step 1: Create database and user
-- =====================================================

\echo '📋 Step 1: Creating database and user...'

-- Create database (ignore error if exists)
SELECT 'Creating database worldplanning...' as step;
CREATE DATABASE worldplanning;

-- Create user (ignore error if exists)
SELECT 'Creating user centerdata_user...' as step;
DO $$
BEGIN
    CREATE USER mappDbUser WITH PASSWORD 'gu3st123';
    EXCEPTION WHEN duplicate_object THEN
    RAISE NOTICE 'User mappDbUser already exists, skipping...';
END
$$;

-- Grant database privileges
GRANT ALL PRIVILEGES ON DATABASE worldplanning TO mappDbUser;

\echo '✅ Database and user setup completed'

-- =====================================================
-- Step 2: Connect to the application database
-- =====================================================

\echo ''
\echo '📋 Step 2: Connecting to worldplanning...'

-- Connect to the application database
\c worldplanning;

-- Grant schema privileges
GRANT ALL ON SCHEMA public TO centerdata_user;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO centerdata_user;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO centerdata_user;

-- Grant future privileges
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO centerdata_user;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON SEQUENCES TO centerdata_user;

\echo '✅ Connected to worldplanning and permissions granted'

-- =====================================================
-- Step 3: Create shared functions
-- =====================================================

\echo ''
\echo '📋 Step 3: Creating shared database functions...'

-- Function to update the updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

COMMENT ON FUNCTION update_updated_at_column() IS 'Automatically updates the updated_at column when a row is modified';

\echo '✅ Shared functions created'

-- =====================================================
-- Step 4: Create center table
-- =====================================================

\echo ''
\echo '📋 Step 4: Creating center table...'

-- Begin transaction for center table
BEGIN;

-- Drop existing objects (if they exist)
DROP INDEX IF EXISTS ix_center_center_id;
DROP INDEX IF EXISTS ix_center_center_name;
DROP INDEX IF EXISTS ix_center_state;
DROP INDEX IF EXISTS ix_center_regions;
DROP INDEX IF EXISTS ix_center_import_batch_id;
DROP INDEX IF EXISTS ix_center_created_at;
DROP INDEX IF EXISTS ix_center_updated_at;
DROP INDEX IF EXISTS ix_center_state_regions;
DROP INDEX IF EXISTS uk_center_center_id;
DROP TABLE IF EXISTS center CASCADE;

-- Create center table
CREATE TABLE center (
    -- Primary key
    id                              SERIAL PRIMARY KEY,
    
    -- Core identification fields
    center_id                       BIGINT NOT NULL,
    center_name                     TEXT,
    
    -- Operational information
    timezone_name                   TEXT,
    center_live_date                TIMESTAMP,
    regions                         TEXT,
    state                           DOUBLE PRECISION,
    
    -- Audit fields
    edited_by                       TEXT,
    created_by                      DOUBLE PRECISION,
    edited_date                     TEXT,
    created_date                    DOUBLE PRECISION,
    
    -- Business logic fields
    center_type_enum                BIGINT,
    center_type_enum_placeholder    BIGINT,
    
    -- Contact information
    center_email                    TEXT,
    center_director_name            TEXT,
    center_director_email           TEXT,
    dvp_name                        TEXT,
    dvp_email                       TEXT,
    regional_manager_name           TEXT,
    regional_manager_email          TEXT,
    
    -- Feature flags
    is_using_assessment             BOOLEAN NOT NULL DEFAULT FALSE,
    is_using_planning               BOOLEAN NOT NULL DEFAULT FALSE,
    
    -- Import tracking
    import_batch_id                 TEXT,
    
    -- Timestamps (automatically managed)
    created_at                      TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at                      TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Add table and column comments
COMMENT ON TABLE center IS 'Stores childcare center data imported from CenterData Tadpoles Center files';
COMMENT ON COLUMN center.center_id IS 'Unique center identifier from source system (School_ID)';
COMMENT ON COLUMN center.center_name IS 'Official name of the childcare center (School_Name)';

-- Create indexes
CREATE UNIQUE INDEX uk_center_center_id ON center (center_id);
CREATE INDEX ix_center_center_name ON center (center_name);
CREATE INDEX ix_center_state ON center (state);
CREATE INDEX ix_center_regions ON center (regions);
CREATE INDEX ix_center_import_batch_id ON center (import_batch_id);
CREATE INDEX ix_center_created_at ON center (created_at);
CREATE INDEX ix_center_updated_at ON center (updated_at);
CREATE INDEX ix_center_state_regions ON center (state, regions);

-- Add constraints
ALTER TABLE center ADD CONSTRAINT chk_center_center_id_positive CHECK (center_id > 0);

-- Create trigger for automatic updated_at timestamp
CREATE TRIGGER tr_center_updated_at
    BEFORE UPDATE ON center
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Grant permissions
GRANT SELECT, INSERT, UPDATE, DELETE ON center TO centerdata_user;
GRANT USAGE, SELECT ON SEQUENCE center_id_seq TO centerdata_user;

COMMIT;

\echo '✅ center table created with indexes and constraints'

-- =====================================================
-- Step 5: Create import_logs table
-- =====================================================

\echo ''
\echo '📋 Step 5: Creating import_logs table...'

-- Begin transaction for import_logs table
BEGIN;

-- Drop existing objects (if they exist)
DROP INDEX IF EXISTS ix_import_logs_batch_id;
DROP INDEX IF EXISTS ix_import_logs_start_time;
DROP INDEX IF EXISTS ix_import_logs_status;
DROP INDEX IF EXISTS ix_import_logs_processor_type;
DROP INDEX IF EXISTS ix_import_logs_file_name;
DROP INDEX IF EXISTS ix_import_logs_status_start_time;
DROP INDEX IF EXISTS ix_import_logs_processor_status;
DROP VIEW IF EXISTS v_recent_imports;
DROP VIEW IF EXISTS v_import_stats_by_processor;
DROP VIEW IF EXISTS v_daily_import_summary;
DROP TABLE IF EXISTS import_logs CASCADE;

-- Create import_logs table
CREATE TABLE import_logs (
    -- Primary key
    id                      SERIAL PRIMARY KEY,
    
    -- Import identification
    batch_id                VARCHAR(100) NOT NULL,
    file_name               VARCHAR(500),
    file_path               VARCHAR(1000),
    processor_type          VARCHAR(100),
    
    -- Timing information
    start_time              TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    end_time                TIMESTAMP,
    
    -- File information
    file_size               BIGINT,
    
    -- Processing statistics
    records_processed       INTEGER DEFAULT 0,
    records_inserted        INTEGER DEFAULT 0,
    records_updated         INTEGER DEFAULT 0,
    records_deleted         INTEGER DEFAULT 0,
    records_failed          INTEGER DEFAULT 0,
    
    -- Status and error tracking
    status                  VARCHAR(50) NOT NULL DEFAULT 'Processing',
    error_message           TEXT,
    
    -- Timestamps
    created_at              TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at              TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Add table comments
COMMENT ON TABLE import_logs IS 'Tracks import operations and statistics for the CenterData SFTP Processor';

-- Create indexes
CREATE INDEX ix_import_logs_batch_id ON import_logs (batch_id);
CREATE INDEX ix_import_logs_start_time ON import_logs (start_time DESC);
CREATE INDEX ix_import_logs_status ON import_logs (status);
CREATE INDEX ix_import_logs_processor_type ON import_logs (processor_type);
CREATE INDEX ix_import_logs_file_name ON import_logs (file_name);
CREATE INDEX ix_import_logs_status_start_time ON import_logs (status, start_time DESC);
CREATE INDEX ix_import_logs_processor_status ON import_logs (processor_type, status);

-- Add constraints
ALTER TABLE import_logs ADD CONSTRAINT chk_import_logs_status_valid CHECK (status IN ('Processing', 'Completed', 'Failed'));
ALTER TABLE import_logs ADD CONSTRAINT chk_import_logs_records_non_negative CHECK (
    records_processed >= 0 AND records_inserted >= 0 AND records_updated >= 0 AND 
    records_deleted >= 0 AND records_failed >= 0
);

-- Create trigger
CREATE TRIGGER tr_import_logs_updated_at
    BEFORE UPDATE ON import_logs
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Grant permissions
GRANT SELECT, INSERT, UPDATE, DELETE ON import_logs TO centerdata_user;
GRANT USAGE, SELECT ON SEQUENCE import_logs_id_seq TO centerdata_user;

COMMIT;

\echo '✅ import_logs table created with indexes and constraints'

-- =====================================================
-- Step 6: Create monitoring views
-- =====================================================

\echo ''
\echo '📋 Step 6: Creating monitoring views...'

-- View for recent import summary
CREATE OR REPLACE VIEW v_recent_imports AS
SELECT 
    id, batch_id, file_name, processor_type, start_time, end_time,
    CASE WHEN end_time IS NOT NULL THEN EXTRACT(EPOCH FROM (end_time - start_time))::INTEGER ELSE NULL END as duration_seconds,
    records_processed, records_inserted, records_updated, records_deleted, records_failed, status,
    CASE 
        WHEN records_failed = 0 AND status = 'Completed' THEN 'Success'
        WHEN records_failed > 0 AND status = 'Completed' THEN 'Partial Success'
        WHEN status = 'Failed' THEN 'Failed'
        ELSE 'In Progress'
    END as result_summary
FROM import_logs
ORDER BY start_time DESC;

-- Grant view permissions
GRANT SELECT ON v_recent_imports TO centerdata_user;

\echo '✅ Monitoring views created'

-- =====================================================
-- Step 7: Validate setup
-- =====================================================

\echo ''
\echo '📋 Step 7: Validating database setup...'

-- Check tables exist
SELECT 
    'Tables created successfully' as validation,
    COUNT(*) as table_count
FROM information_schema.tables 
WHERE table_schema = 'public' 
AND table_name IN ('center', 'import_logs');

-- Check indexes exist
SELECT 
    'Indexes created successfully' as validation,
    COUNT(*) as index_count
FROM pg_indexes 
WHERE schemaname = 'public' 
AND tablename IN ('center', 'import_logs');

-- Check permissions
SELECT 
    'Permissions granted successfully' as validation,
    COUNT(*) as permission_count
FROM information_schema.table_privileges 
WHERE grantee = 'centerdata_user';

\echo '✅ Database validation completed'

-- =====================================================
-- Success message and next steps
-- =====================================================

\echo ''
\echo '🎉 Database setup completed successfully!'
\echo ''
\echo '📊 Summary:'
\echo '- Database: worldplanning'
\echo '- User: centerdata_user'
\echo '- Tables: center, import_logs'
\echo '- Indexes: 15 performance indexes'
\echo '- Views: 1 monitoring view'
\echo '- Functions: 1 utility function'
\echo '- Triggers: 2 automatic timestamp triggers'
\echo ''
\echo '🔧 Connection Details:'
\echo '- Host: localhost (or your PostgreSQL server)'
\echo '- Port: 5432 (default)'
\echo '- Database: worldplanning'
\echo '- Username: centerdata_user'
\echo '- Password: CenterData2024!'
\echo ''
\echo '📋 Next Steps:'
\echo '1. Update appsettings.json with database connection details'
\echo '2. Test application connectivity'
\echo '3. Run sample data import: dotnet run "./sample-data/sample_center_data.txt"'
\echo '4. Monitor imports: SELECT * FROM v_recent_imports;'
\echo ''
\echo '⚠️  Security Note:'
\echo 'Change the default password in production environments!'
\echo ''
\echo '✅ Ready to process CenterData files! 🚀'
\echo ''
