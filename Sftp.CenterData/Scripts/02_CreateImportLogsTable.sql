-- =====================================================
-- CenterData SFTP Processor - Import Logs Table Script
-- =====================================================
-- 
-- This script creates the import_logs table for tracking
-- import operations and statistics.
--
-- Prerequisites:
-- - PostgreSQL 12+ database server
-- - Database and user already created
-- - Appropriate permissions granted
--
-- Usage:
--   psql -d worldplanning -f Scripts/02_CreateImportLogsTable.sql
--
-- =====================================================

-- Set client encoding and timezone
SET client_encoding = 'UTF8';
SET timezone = 'UTC';

-- Begin transaction
BEGIN;

-- =====================================================
-- Drop existing objects (if they exist)
-- =====================================================

-- Drop indexes first (if they exist)
DROP INDEX IF EXISTS ix_import_logs_batch_id;
DROP INDEX IF EXISTS ix_import_logs_start_time;
DROP INDEX IF EXISTS ix_import_logs_status;
DROP INDEX IF EXISTS ix_import_logs_processor_type;
DROP INDEX IF EXISTS ix_import_logs_file_name;

-- Drop table (if it exists)
DROP TABLE IF EXISTS import_logs CASCADE;

-- =====================================================
-- Create import_logs table
-- =====================================================

CREATE TABLE import_logs (
    -- Primary key
    id                      SERIAL PRIMARY KEY,
    
    -- Import identification
    batch_id                VARCHAR(100) NOT NULL,
    file_name               VARCHAR(500),
    file_path               VARCHAR(1000),
    processor_type          VARCHAR(100),
    
    -- Timing information
    start_time              TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    end_time                TIMESTAMP,
    
    -- File information
    file_size               BIGINT,
    
    -- Processing statistics
    records_processed       INTEGER DEFAULT 0,
    records_inserted        INTEGER DEFAULT 0,
    records_updated         INTEGER DEFAULT 0,
    records_deleted         INTEGER DEFAULT 0,
    records_failed          INTEGER DEFAULT 0,
    
    -- Status and error tracking
    status                  VARCHAR(50) NOT NULL DEFAULT 'Processing',
    error_message           TEXT,
    
    -- Timestamps
    created_at              TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at              TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- =====================================================
-- Add table comments
-- =====================================================

COMMENT ON TABLE import_logs IS 'Tracks import operations and statistics for the CenterData SFTP Processor';

-- Column comments
COMMENT ON COLUMN import_logs.id IS 'Primary key - auto-incrementing identifier';
COMMENT ON COLUMN import_logs.batch_id IS 'Unique identifier for the import batch';
COMMENT ON COLUMN import_logs.file_name IS 'Name of the processed file';
COMMENT ON COLUMN import_logs.file_path IS 'Full path to the processed file';
COMMENT ON COLUMN import_logs.processor_type IS 'Type of processor used (e.g., CenterData_Tadpoles_Center)';
COMMENT ON COLUMN import_logs.start_time IS 'When the import operation started';
COMMENT ON COLUMN import_logs.end_time IS 'When the import operation completed';
COMMENT ON COLUMN import_logs.file_size IS 'Size of the processed file in bytes';
COMMENT ON COLUMN import_logs.records_processed IS 'Total number of records processed';
COMMENT ON COLUMN import_logs.records_inserted IS 'Number of new records inserted';
COMMENT ON COLUMN import_logs.records_updated IS 'Number of existing records updated';
COMMENT ON COLUMN import_logs.records_deleted IS 'Number of records deleted (not in source)';
COMMENT ON COLUMN import_logs.records_failed IS 'Number of records that failed to process';
COMMENT ON COLUMN import_logs.status IS 'Import status: Processing, Completed, Failed';
COMMENT ON COLUMN import_logs.error_message IS 'Error details if import failed';
COMMENT ON COLUMN import_logs.created_at IS 'Timestamp when log record was created';
COMMENT ON COLUMN import_logs.updated_at IS 'Timestamp when log record was last updated';

-- =====================================================
-- Create indexes for performance
-- =====================================================

-- Primary business indexes
CREATE INDEX ix_import_logs_batch_id 
    ON import_logs (batch_id);

CREATE INDEX ix_import_logs_start_time 
    ON import_logs (start_time DESC);

CREATE INDEX ix_import_logs_status 
    ON import_logs (status);

CREATE INDEX ix_import_logs_processor_type 
    ON import_logs (processor_type);

CREATE INDEX ix_import_logs_file_name 
    ON import_logs (file_name);

-- Composite indexes for common queries
CREATE INDEX ix_import_logs_status_start_time 
    ON import_logs (status, start_time DESC);

CREATE INDEX ix_import_logs_processor_status 
    ON import_logs (processor_type, status);

-- =====================================================
-- Add constraints
-- =====================================================

-- Check constraints for data integrity
ALTER TABLE import_logs 
    ADD CONSTRAINT chk_import_logs_status_valid 
    CHECK (status IN ('Processing', 'Completed', 'Failed'));

ALTER TABLE import_logs 
    ADD CONSTRAINT chk_import_logs_records_non_negative 
    CHECK (
        records_processed >= 0 AND 
        records_inserted >= 0 AND 
        records_updated >= 0 AND 
        records_deleted >= 0 AND 
        records_failed >= 0
    );

ALTER TABLE import_logs 
    ADD CONSTRAINT chk_import_logs_file_size_non_negative 
    CHECK (file_size IS NULL OR file_size >= 0);

ALTER TABLE import_logs 
    ADD CONSTRAINT chk_import_logs_end_time_after_start 
    CHECK (end_time IS NULL OR end_time >= start_time);

-- =====================================================
-- Create trigger for automatic updated_at timestamp
-- =====================================================

-- Create trigger to automatically update updated_at on row changes
CREATE TRIGGER tr_import_logs_updated_at
    BEFORE UPDATE ON import_logs
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- =====================================================
-- Grant permissions
-- =====================================================

-- Grant permissions to the application user
GRANT SELECT, INSERT, UPDATE, DELETE ON import_logs TO centerdata_user;
GRANT USAGE, SELECT ON SEQUENCE import_logs_id_seq TO centerdata_user;

-- =====================================================
-- Create useful views for monitoring
-- =====================================================

-- View for recent import summary
CREATE OR REPLACE VIEW v_recent_imports AS
SELECT 
    id,
    batch_id,
    file_name,
    processor_type,
    start_time,
    end_time,
    CASE 
        WHEN end_time IS NOT NULL THEN 
            EXTRACT(EPOCH FROM (end_time - start_time))::INTEGER 
        ELSE NULL 
    END as duration_seconds,
    records_processed,
    records_inserted,
    records_updated,
    records_deleted,
    records_failed,
    status,
    CASE 
        WHEN records_failed = 0 AND status = 'Completed' THEN 'Success'
        WHEN records_failed > 0 AND status = 'Completed' THEN 'Partial Success'
        WHEN status = 'Failed' THEN 'Failed'
        ELSE 'In Progress'
    END as result_summary
FROM import_logs
ORDER BY start_time DESC;

-- View for import statistics by processor type
CREATE OR REPLACE VIEW v_import_stats_by_processor AS
SELECT 
    processor_type,
    COUNT(*) as total_imports,
    COUNT(CASE WHEN status = 'Completed' THEN 1 END) as successful_imports,
    COUNT(CASE WHEN status = 'Failed' THEN 1 END) as failed_imports,
    SUM(records_processed) as total_records_processed,
    SUM(records_inserted) as total_records_inserted,
    SUM(records_updated) as total_records_updated,
    SUM(records_deleted) as total_records_deleted,
    SUM(records_failed) as total_records_failed,
    AVG(records_processed) as avg_records_per_import,
    AVG(
        CASE 
            WHEN end_time IS NOT NULL THEN 
                EXTRACT(EPOCH FROM (end_time - start_time))
            ELSE NULL 
        END
    ) as avg_duration_seconds
FROM import_logs
WHERE processor_type IS NOT NULL
GROUP BY processor_type
ORDER BY total_imports DESC;

-- View for daily import summary
CREATE OR REPLACE VIEW v_daily_import_summary AS
SELECT 
    DATE(start_time) as import_date,
    COUNT(*) as total_imports,
    COUNT(CASE WHEN status = 'Completed' THEN 1 END) as successful_imports,
    COUNT(CASE WHEN status = 'Failed' THEN 1 END) as failed_imports,
    SUM(records_processed) as total_records_processed,
    SUM(records_inserted) as total_records_inserted,
    SUM(records_updated) as total_records_updated,
    SUM(records_deleted) as total_records_deleted,
    SUM(records_failed) as total_records_failed
FROM import_logs
GROUP BY DATE(start_time)
ORDER BY import_date DESC;

-- Grant view permissions
GRANT SELECT ON v_recent_imports TO centerdata_user;
GRANT SELECT ON v_import_stats_by_processor TO centerdata_user;
GRANT SELECT ON v_daily_import_summary TO centerdata_user;

-- =====================================================
-- Add comments for views
-- =====================================================

COMMENT ON VIEW v_recent_imports IS 'Recent import operations with calculated duration and result summary';
COMMENT ON VIEW v_import_stats_by_processor IS 'Import statistics aggregated by processor type';
COMMENT ON VIEW v_daily_import_summary IS 'Daily summary of import operations and statistics';

-- =====================================================
-- Create sample data (optional - for testing)
-- =====================================================

-- Uncomment the following section to insert sample data for testing

/*
INSERT INTO import_logs (
    batch_id, file_name, file_path, processor_type,
    start_time, end_time, file_size,
    records_processed, records_inserted, records_updated, records_deleted, records_failed,
    status
) VALUES 
(
    'SAMPLE-BATCH-001',
    'sample_center_data.txt',
    './sample-data/sample_center_data.txt',
    'CenterData_Tadpoles_Center',
    CURRENT_TIMESTAMP - INTERVAL '5 minutes',
    CURRENT_TIMESTAMP - INTERVAL '4 minutes',
    1024,
    2, 2, 0, 0, 0,
    'Completed'
),
(
    'SAMPLE-BATCH-002',
    'another_center_file.txt',
    '/remote/path/another_center_file.txt',
    'CenterData_Tadpoles_Center',
    CURRENT_TIMESTAMP - INTERVAL '2 minutes',
    CURRENT_TIMESTAMP - INTERVAL '1 minute',
    2048,
    5, 3, 2, 0, 0,
    'Completed'
);
*/

-- =====================================================
-- Verify table creation
-- =====================================================

-- Display table information
SELECT 
    'import_logs table created successfully' as status,
    COUNT(*) as initial_record_count
FROM import_logs;

-- Display index information
SELECT 
    schemaname,
    tablename,
    indexname,
    indexdef
FROM pg_indexes 
WHERE tablename = 'import_logs'
ORDER BY indexname;

-- Display view information
SELECT 
    schemaname,
    viewname,
    definition
FROM pg_views 
WHERE viewname LIKE 'v_%import%'
ORDER BY viewname;

-- Commit transaction
COMMIT;

-- =====================================================
-- Success message
-- =====================================================

\echo ''
\echo '✅ Import logs table creation completed successfully!'
\echo ''
\echo 'Table created: import_logs'
\echo 'Indexes created: 7 performance indexes'
\echo 'Views created: 3 monitoring views'
\echo 'Constraints added: Data validation rules'
\echo 'Triggers created: Automatic updated_at timestamp'
\echo 'Permissions granted: centerdata_user access'
\echo ''
\echo 'Monitoring views available:'
\echo '- v_recent_imports: Recent import operations'
\echo '- v_import_stats_by_processor: Statistics by processor type'
\echo '- v_daily_import_summary: Daily import summary'
\echo ''
\echo 'Next steps:'
\echo '1. Verify table structure: \\d import_logs'
\echo '2. Test monitoring views: SELECT * FROM v_recent_imports;'
\echo '3. Run application import tests'
\echo ''
