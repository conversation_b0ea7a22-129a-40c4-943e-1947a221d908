{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.EntityFrameworkCore": "Warning"}}, "AppConfiguration": {"Environment": "Production", "GcpProjectId": "mapp-dev-457512", "Database": {"ConnectionString": "", "Host": "your-postgres-host", "Port": 5432, "Database": "centerdata_db", "Username": "centerdata_user", "Password": ""}, "Sftp": {"Host": "upload.brighthorizons.com", "Port": 22, "Username": "onsharp-sftp", "Password": "", "PrivateKeyPath": "", "PrivateKeyPassphrase": "", "ConnectionTimeoutSeconds": 30, "OperationTimeoutSeconds": 300}, "Processing": {"TempDirectory": "/tmp", "BatchSize": 1000, "DeleteTempFilesAfterProcessing": true, "FilePattern": "BrightStar_Tadpoles_Center_*.txt", "RemoteDirectory": "/Non-Eligibility/onsharp-sftp/Non-PROD/Tadpoles/PreProduction/", "ProcessCurrentDateOnly": true}}}