#!/bin/bash

# Test script for Center SFTP to PostgreSQL Importer

echo "Testing Center SFTP to PostgreSQL Importer"
echo "=============================================="

# Check if sample file exists
if [ ! -f "../BrightStar_Tadpoles_Center_20250602.txt" ]; then
    echo "Error: Sample file BrightStar_Tadpoles_Center_20250602.txt not found in parent directory"
    echo "Please ensure the sample file is available"
    exit 1
fi

echo "Sample file found: ../BrightStar_Tadpoles_Center_20250602.txt"

# Build the application
echo "Building BrightStar application..."
dotnet build -c Release

if [ $? -ne 0 ]; then
    echo "Build failed!"
    exit 1
fi

echo "Build successful!"

echo ""
echo "BrightStar processor is ready for testing!"
echo ""
echo "To test the application:"
echo "1. Set up PostgreSQL database"
echo "2. Configure appsettings.json with your database connection"
echo "3. Run: dotnet run ../BrightStar_Tadpoles_Center_20250602.txt"
echo ""
echo "For SFTP testing:"
echo "1. Configure SFTP settings in appsettings.json"
echo "2. Run: dotnet run /remote/path/to/BrightStar_file.txt"
echo ""
echo "For cloud deployment:"
echo "1. Build Docker image: docker build -t sftp-brightstar-importer ."
echo "2. Configure GCP Secret Manager: ../Scripts/setup-secrets.sh"
echo "3. Deploy to Cloud Run: ../Scripts/build-and-deploy.sh"

echo ""
echo "Architecture benefits:"
echo "- Core SFTP logic is reusable across different file processors"
echo "- BrightStar-specific logic is isolated in this project"
echo "- Easy to add new data sources by creating similar projects"
echo "- Independent deployment and scaling per data source"

echo ""
echo "BrightStar application structure verified successfully!"
