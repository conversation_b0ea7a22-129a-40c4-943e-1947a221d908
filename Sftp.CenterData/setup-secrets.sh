#!/bin/bash

# =====================================================
# Secret Manager Setup Script for CenterData SFTP Processor
# =====================================================
#
# This script creates the required secrets in Google Cloud Secret Manager
# for the CenterData SFTP processor deployment.
#
# Prerequisites:
# 1. gcloud CLI installed and authenticated
# 2. Secret Manager API enabled
# 3. Appropriate IAM permissions
#
# Usage:
#   ./setup-secrets.sh
#
# =====================================================

set -e  # Exit on any error

# Configuration
PROJECT_ID="mapp-dev-457512"
SERVICE_ACCOUNT="centerdata-processor@${PROJECT_ID}.iam.gserviceaccount.com"

echo "🔐 Setting up Secret Manager secrets for CenterData SFTP Processor"
echo "Project: ${PROJECT_ID}"
echo ""

# Set the project
gcloud config set project ${PROJECT_ID}

# Enable Secret Manager API
echo "🔧 Enabling Secret Manager API..."
gcloud services enable secretmanager.googleapis.com

# Create service account if it doesn't exist
echo "👤 Creating service account..."
gcloud iam service-accounts create centerdata-processor \
    --display-name="CenterData SFTP Processor" \
    --description="Service account for CenterData SFTP processor Cloud Run job" \
    --project=${PROJECT_ID} || echo "Service account already exists"

# Grant necessary permissions to service account
echo "🔑 Granting IAM permissions..."
gcloud projects add-iam-policy-binding ${PROJECT_ID} \
    --member="serviceAccount:${SERVICE_ACCOUNT}" \
    --role="roles/secretmanager.secretAccessor"

gcloud projects add-iam-policy-binding ${PROJECT_ID} \
    --member="serviceAccount:${SERVICE_ACCOUNT}" \
    --role="roles/cloudsql.client"

gcloud projects add-iam-policy-binding ${PROJECT_ID} \
    --member="serviceAccount:${SERVICE_ACCOUNT}" \
    --role="roles/logging.logWriter"

gcloud projects add-iam-policy-binding ${PROJECT_ID} \
    --member="serviceAccount:${SERVICE_ACCOUNT}" \
    --role="roles/monitoring.metricWriter"

# Create secrets
echo "🔐 Creating secrets..."

# Database connection string secret
echo "Creating database connection string secret..."
echo -n "Host=**************;Port=5432;Database=worldplanning;Username=mappdbuser;Password=********" | \
gcloud secrets create postgresdb-connection \
    --data-file=- \
    --project=${PROJECT_ID} || echo "Secret postgresdb-connection already exists"

# SFTP password secret
echo "Creating SFTP password secret..."
echo -n "w43p95624t" | \
gcloud secrets create sftp-password \
    --data-file=- \
    --project=${PROJECT_ID} || echo "Secret sftp-password already exists"

# Grant service account access to secrets
echo "🔑 Granting secret access permissions..."
gcloud secrets add-iam-policy-binding postgresdb-connection \
    --member="serviceAccount:${SERVICE_ACCOUNT}" \
    --role="roles/secretmanager.secretAccessor" \
    --project=${PROJECT_ID}

gcloud secrets add-iam-policy-binding sftp-password \
    --member="serviceAccount:${SERVICE_ACCOUNT}" \
    --role="roles/secretmanager.secretAccessor" \
    --project=${PROJECT_ID}

echo ""
echo "✅ Secret Manager setup completed!"
echo ""
echo "📋 Created secrets:"
echo "  - postgresdb-connection"
echo "  - sftp-password"
echo ""
echo "🔧 Next steps:"
echo "1. Update the database connection string secret with your actual PostgreSQL details:"
echo "   gcloud secrets versions add postgresdb-connection --data-file=- <<< 'Host=YOUR_ACTUAL_HOST;Port=5432;Database=centerdata_db;Username=centerdata_user;Password=YOUR_ACTUAL_PASSWORD'"
echo ""
echo "2. Verify secrets:"
echo "   gcloud secrets list --project=${PROJECT_ID}"
echo ""
echo "3. Test secret access:"
echo "   gcloud secrets versions access latest --secret=postgresdb-connection --project=${PROJECT_ID}"
echo ""
echo "🎯 Ready for deployment!"
