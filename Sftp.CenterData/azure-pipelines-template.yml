# =====================================================
# Azure DevOps Pipeline Template for SFTP Processors
# =====================================================
#
# This is a template for creating Azure DevOps pipelines for different
# SFTP processing jobs. Copy this file and customize the variables
# section for each specific SFTP processor.
#
# Usage:
# 1. Copy this file to your specific processor project
# 2. Rename to azure-pipelines.yml
# 3. Update the variables section below
# 4. Update the trigger paths to match your project structure
#
# =====================================================

trigger:
  branches:
    include:
    - main
    - develop
  paths:
    include:
    - Sftp.YourProcessor/*    # UPDATE: Change to your processor path
    - Sftp.Core/*

variables:
  # GCP Configuration (usually same for all processors)
  GCP_PROJECT_ID: 'mapp-dev-457512'
  GCP_REGION: 'us-central1'
  GCP_SERVICE_ACCOUNT: '<EMAIL>'
  
  # Application Configuration - CUSTOMIZE FOR EACH SFTP PROCESSOR
  CLOUD_RUN_JOB_NAME: 'your-processor-name'           # UPDATE: e.g., 'centerdata-sftp-processor', 'employee-sftp-processor'
  SECRET_DB_NAME: 'your-processor-db-connection'      # UPDATE: e.g., 'centerdata-db-connection', 'employee-db-connection'
  SECRET_SFTP_NAME: 'your-processor-sftp-password'    # UPDATE: e.g., 'centerdata-sftp-password', 'employee-sftp-password'
  
  DOCKER_IMAGE_NAME: 'gcr.io/$(GCP_PROJECT_ID)/$(CLOUD_RUN_JOB_NAME)'
  
  # Build Configuration (usually same for all processors)
  DOTNET_VERSION: '9.0.x'
  BUILD_CONFIGURATION: 'Release'

stages:
- stage: Build
  displayName: 'Build and Test'
  jobs:
  - job: BuildJob
    displayName: 'Build .NET Application'
    pool:
      vmImage: 'ubuntu-latest'
    
    steps:
    - checkout: self
      displayName: 'Checkout Source Code'
    
    - task: UseDotNet@2
      displayName: 'Install .NET SDK'
      inputs:
        packageType: 'sdk'
        version: '$(DOTNET_VERSION)'
    
    - task: DotNetCoreCLI@2
      displayName: 'Restore NuGet Packages'
      inputs:
        command: 'restore'
        projects: 'Sftp.YourProcessor/Sftp.YourProcessor.csproj'  # UPDATE: Change to your processor project
    
    - task: DotNetCoreCLI@2
      displayName: 'Build Application'
      inputs:
        command: 'build'
        projects: 'Sftp.YourProcessor/Sftp.YourProcessor.csproj'  # UPDATE: Change to your processor project
        arguments: '--configuration $(BUILD_CONFIGURATION) --no-restore'
    
    - task: DotNetCoreCLI@2
      displayName: 'Run Unit Tests'
      inputs:
        command: 'test'
        projects: '**/*Tests.csproj'
        arguments: '--configuration $(BUILD_CONFIGURATION) --no-build --collect:"XPlat Code Coverage"'
      continueOnError: true
    
    - task: PublishCodeCoverageResults@1
      displayName: 'Publish Code Coverage'
      inputs:
        codeCoverageTool: 'Cobertura'
        summaryFileLocation: '$(Agent.TempDirectory)/**/coverage.cobertura.xml'
      condition: succeededOrFailed()

- stage: Deploy
  displayName: 'Deploy to GCP'
  dependsOn: Build
  condition: and(succeeded(), eq(variables['Build.SourceBranch'], 'refs/heads/main'))
  jobs:
  - deployment: DeployCloudRun
    displayName: 'Deploy Cloud Run Job'
    pool:
      vmImage: 'ubuntu-latest'
    environment: 'production'
    
    strategy:
      runOnce:
        deploy:
          steps:
          - checkout: self
            displayName: 'Checkout Source Code'
          
          - task: GoogleCloudSdkTool@0
            displayName: 'Install Google Cloud SDK'
            inputs:
              version: 'latest'
          
          - task: DownloadSecureFile@1
            name: gcpServiceAccountKey
            displayName: 'Download GCP Service Account Key'
            inputs:
              secureFile: 'gcp-service-account-key.json'
          
          - script: |
              # Authenticate with GCP using service account
              gcloud auth activate-service-account $(GCP_SERVICE_ACCOUNT) --key-file=$(gcpServiceAccountKey.secureFilePath)
              gcloud config set project $(GCP_PROJECT_ID)
              gcloud config set compute/region $(GCP_REGION)
              
              # Configure Docker for GCR
              gcloud auth configure-docker
            displayName: 'Authenticate with GCP'
          
          - script: |
              # Set environment variables for deployment script
              export GCP_PROJECT_ID=$(GCP_PROJECT_ID)
              export GCP_REGION=$(GCP_REGION)
              export GCP_SERVICE_ACCOUNT=$(GCP_SERVICE_ACCOUNT)
              export CLOUD_RUN_JOB_NAME=$(CLOUD_RUN_JOB_NAME)
              export SECRET_DB_NAME=$(SECRET_DB_NAME)
              export SECRET_SFTP_NAME=$(SECRET_SFTP_NAME)
              export BUILD_ID=$(Build.BuildId)
              export DOCKER_IMAGE_NAME=$(DOCKER_IMAGE_NAME)
              
              # Change to project directory and run deployment script
              cd Sftp.YourProcessor  # UPDATE: Change to your processor directory
              ./deploy-azure-devops.sh
            displayName: 'Deploy Cloud Run Job'
          
          - script: |
              # Output deployment information
              echo "🎉 Deployment Summary:"
              echo "Project: $(GCP_PROJECT_ID)"
              echo "Region: $(GCP_REGION)"
              echo "Job Name: $(CLOUD_RUN_JOB_NAME)"
              echo "Image: $(DOCKER_IMAGE_NAME):$(Build.BuildId)"
              echo "Service Account: $(GCP_SERVICE_ACCOUNT)"
              echo "DB Secret: $(SECRET_DB_NAME)"
              echo "SFTP Secret: $(SECRET_SFTP_NAME)"
              echo ""
              echo "📋 Management Commands:"
              echo "Manual execution: gcloud run jobs execute $(CLOUD_RUN_JOB_NAME) --region=$(GCP_REGION)"
              echo "View logs: gcloud logging read 'resource.type=\"cloud_run_job\"' --limit=50"
              echo "Job details: gcloud run jobs describe $(CLOUD_RUN_JOB_NAME) --region=$(GCP_REGION)"
              echo ""
              echo "🔗 Cloud Console: https://console.cloud.google.com/run/jobs/details/$(GCP_REGION)/$(CLOUD_RUN_JOB_NAME)?project=$(GCP_PROJECT_ID)"
            displayName: 'Deployment Summary'

# =====================================================
# CUSTOMIZATION EXAMPLES
# =====================================================
#
# Example 1: CenterData SFTP Processor
# CLOUD_RUN_JOB_NAME: 'centerdata-sftp-processor'
# SECRET_DB_NAME: 'centerdata-db-connection'
# SECRET_SFTP_NAME: 'centerdata-sftp-password'
# Project path: 'Sftp.CenterData/*'
#
# Example 2: Employee SFTP Processor
# CLOUD_RUN_JOB_NAME: 'employee-sftp-processor'
# SECRET_DB_NAME: 'employee-db-connection'
# SECRET_SFTP_NAME: 'employee-sftp-password'
# Project path: 'Sftp.Employee/*'
#
# Example 3: Payroll SFTP Processor
# CLOUD_RUN_JOB_NAME: 'payroll-sftp-processor'
# SECRET_DB_NAME: 'payroll-db-connection'
# SECRET_SFTP_NAME: 'payroll-sftp-password'
# Project path: 'Sftp.Payroll/*'
#
# =====================================================
