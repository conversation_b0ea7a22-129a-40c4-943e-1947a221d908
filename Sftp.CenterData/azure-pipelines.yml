# =====================================================
# Azure DevOps Pipeline for CenterData SFTP Processor
# Deploys Cloud Run Job to GCP project mapp-dev-457512
# =====================================================

trigger:
  branches:
    include:
    - main
    - develop
  paths:
    include:
    - Sftp.CenterData/*
    - Sftp.Core/*

variables:
  # GCP Configuration
  GCP_PROJECT_ID: 'mapp-dev-457512'
  GCP_REGION: 'us-central1'
  GCP_SERVICE_ACCOUNT: '<EMAIL>'

  # Application Configuration - CUSTOMIZE FOR EACH SFTP PROCESSOR
  CLOUD_RUN_JOB_NAME: 'centerdata-sftp-processor'  # Change this for different processors
  SECRET_DB_NAME: 'postgresdb-connection'        # Change this for different processors
  SECRET_SFTP_NAME: 'sftp-password'      # Change this for different processors

  REPOSITORY_NAME: 'sftp-processors'
  DOCKER_IMAGE_NAME: '$(GCP_REGION)-docker.pkg.dev/$(GCP_PROJECT_ID)/$(REPOSITORY_NAME)/$(CLOUD_RUN_JOB_NAME)'

  # Build Configuration
  DOTNET_VERSION: '9.0.x'
  BUILD_CONFIGURATION: 'Release'

stages:
- stage: Build
  displayName: 'Build and Test'
  jobs:
  - job: BuildJob
    displayName: 'Build .NET Application'
    pool:
      vmImage: 'ubuntu-latest'
    
    steps:
    - checkout: self
      displayName: 'Checkout Source Code'
    
    - task: UseDotNet@2
      displayName: 'Install .NET SDK'
      inputs:
        packageType: 'sdk'
        version: '$(DOTNET_VERSION)'
    
    - task: DotNetCoreCLI@2
      displayName: 'Restore NuGet Packages'
      inputs:
        command: 'restore'
        projects: 'Sftp.CenterData/Sftp.CenterData.csproj'
    
    - task: DotNetCoreCLI@2
      displayName: 'Build Application'
      inputs:
        command: 'build'
        projects: 'Sftp.CenterData/Sftp.CenterData.csproj'
        arguments: '--configuration $(BUILD_CONFIGURATION) --no-restore'
    
    - task: DotNetCoreCLI@2
      displayName: 'Run Unit Tests'
      inputs:
        command: 'test'
        projects: '**/*Tests.csproj'
        arguments: '--configuration $(BUILD_CONFIGURATION) --no-build --collect:"XPlat Code Coverage"'
      continueOnError: true
    
    - task: PublishCodeCoverageResults@1
      displayName: 'Publish Code Coverage'
      inputs:
        codeCoverageTool: 'Cobertura'
        summaryFileLocation: '$(Agent.TempDirectory)/**/coverage.cobertura.xml'
      condition: succeededOrFailed()

- stage: Deploy
  displayName: 'Deploy to GCP'
  dependsOn: Build
  condition: and(succeeded(), eq(variables['Build.SourceBranch'], 'refs/heads/main'))
  jobs:
  - deployment: DeployCloudRun
    displayName: 'Deploy Cloud Run Job'
    pool:
      vmImage: 'ubuntu-latest'
    environment: 'production'
    
    strategy:
      runOnce:
        deploy:
          steps:
          - checkout: self
            displayName: 'Checkout Source Code'
          
          #- task: GoogleCloudSdkTool@1
          #  displayName: 'Install Google Cloud SDK'
          #  inputs:
          #    version: '1.4.1'
          - script: |
              curl https://sdk.cloud.google.com | bash
              source $HOME/google-cloud-sdk/path.bash.inc
              gcloud version
            displayName: 'Install gcloud manually'
          - task: DownloadSecureFile@1
            name: gcpServiceAccountKey
            displayName: 'Download GCP Service Account Key'
            inputs:
              secureFile: 'gcp-service-account-key.json'
          
          - script: |
              # Authenticate with GCP using service account
              gcloud auth activate-service-account $(GCP_SERVICE_ACCOUNT) --key-file=$(gcpServiceAccountKey.secureFilePath)
              gcloud config set project $(GCP_PROJECT_ID)
              gcloud config set compute/region $(GCP_REGION)
              
              # Configure Docker for GCR
              gcloud auth configure-docker
            displayName: 'Authenticate with GCP'
          
          - script: |
              # Set environment variables for deployment script
              export GCP_PROJECT_ID=$(GCP_PROJECT_ID)
              export GCP_REGION=$(GCP_REGION)
              export GCP_SERVICE_ACCOUNT=$(GCP_SERVICE_ACCOUNT)
              export CLOUD_RUN_JOB_NAME=$(CLOUD_RUN_JOB_NAME)
              export SECRET_DB_NAME=$(SECRET_DB_NAME)
              export SECRET_SFTP_NAME=$(SECRET_SFTP_NAME)
              export BUILD_ID=$(Build.BuildId)
              export DOCKER_IMAGE_NAME=$(DOCKER_IMAGE_NAME)

              # Change to project directory and run deployment script
              cd Sftp.CenterData
              ./deploy-azure-devops.sh
            displayName: 'Deploy Cloud Run Job'
          
          - script: |
              # Test the deployment with a manual execution
              echo "Testing Cloud Run Job deployment..."
              EXECUTION_NAME=$(gcloud run jobs execute $(CLOUD_RUN_JOB_NAME) \
                --region=$(GCP_REGION) \
                --format="value(metadata.name)")
              
              echo "Execution started: $EXECUTION_NAME"
              
              # Wait for execution to complete (max 10 minutes)
              timeout=600
              elapsed=0
              while [ $elapsed -lt $timeout ]; do
                status=$(gcloud run jobs executions describe $EXECUTION_NAME \
                  --region=$(GCP_REGION) \
                  --format="value(status.conditions[0].type)")
                
                if [ "$status" = "Completed" ]; then
                  echo "✅ Execution completed successfully"
                  break
                elif [ "$status" = "Failed" ]; then
                  echo "❌ Execution failed"
                  gcloud run jobs executions describe $EXECUTION_NAME --region=$(GCP_REGION)
                  exit 1
                fi
                
                echo "Waiting for execution to complete... ($elapsed/$timeout seconds)"
                sleep 30
                elapsed=$((elapsed + 30))
              done
              
              if [ $elapsed -ge $timeout ]; then
                echo "⚠️ Execution timeout - check Cloud Console for status"
              fi
            displayName: 'Test Deployment'
            continueOnError: true
          
          - script: |
              # Output deployment information
              echo "🎉 Deployment Summary:"
              echo "Project: $(GCP_PROJECT_ID)"
              echo "Region: $(GCP_REGION)"
              echo "Job Name: $(CLOUD_RUN_JOB_NAME)"
              echo "Image: $(DOCKER_IMAGE_NAME):$(Build.BuildId)"
              echo "Service Account: $(GCP_SERVICE_ACCOUNT)"
              echo ""
              echo "📋 Management Commands:"
              echo "Manual execution: gcloud run jobs execute $(CLOUD_RUN_JOB_NAME) --region=$(GCP_REGION)"
              echo "View logs: gcloud logging read 'resource.type=\"cloud_run_job\"' --limit=50"
              echo "Job details: gcloud run jobs describe $(CLOUD_RUN_JOB_NAME) --region=$(GCP_REGION)"
              echo ""
              echo "🔗 Cloud Console: https://console.cloud.google.com/run/jobs/details/$(GCP_REGION)/$(CLOUD_RUN_JOB_NAME)?project=$(GCP_PROJECT_ID)"
            displayName: 'Deployment Summary'
