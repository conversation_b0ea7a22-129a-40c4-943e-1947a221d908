# CenterData SFTP Processor Setup Guide

## Overview

The CenterData SFTP Processor is a .NET 9 application that automatically downloads, processes, and imports childcare center data from SFTP servers into PostgreSQL databases. It supports both local file processing and automated SFTP operations with comprehensive error handling and audit logging.

## Features

- **Automated SFTP Operations**: Download files from remote SFTP servers
- **File Processing**: Parse pipe-delimited CenterData files
- **Database Operations**: Upsert and deletion support with PostgreSQL
- **Audit Logging**: Complete import tracking and error reporting
- **Flexible Deployment**: Local development and cloud-ready
- **Deletion Logic**: Removes records dropped from source data

## Architecture

### Core Services

- **CenterDataFileProcessor**: Parses CenterData Tadpoles Center files
- **CenterImportService**: Database operations with upsert and deletion logic
- **CenterDataOrchestrationService**: Main workflow orchestration
- **SftpService**: Generic SFTP operations (download, list, file existence checks)
- **ConfigurationService**: Configuration management with secret loading
- **SecretManagerService**: GCP Secret Manager integration

### Data Models

- **Center**: Streamlined entity with essential center data (23 fields)
- **ImportLog**: Tracks import operations and statistics
- **ImportResult**: Processing statistics including deletions

## Prerequisites

### System Requirements
- **.NET 9 SDK** or later
- **PostgreSQL 12+** database server
- **SFTP Server** access (for remote file processing)

### Development Tools (Optional)
- Visual Studio 2022 or VS Code
- PostgreSQL client (pgAdmin, DBeaver, etc.)
- Git for version control

## Installation

### 1. Clone the Repository
```bash
git clone <repository-url>
cd Sftp.CenterData
```

### 2. Install Dependencies
```bash
dotnet restore
```

### 3. Build the Application
```bash
dotnet build
```

## Configuration

### 1. Database Setup

#### Create PostgreSQL Database
```sql
CREATE DATABASE worldplanning;
CREATE USER centerdata_user WITH PASSWORD 'your_password';
GRANT ALL PRIVILEGES ON DATABASE worldplanning TO centerdata_user;
```

#### Database Schema
The application will automatically create the required tables:
- `center` - Main center data table
- `import_logs` - Import operation tracking

### 2. Application Configuration

#### Local Development (appsettings.json)
```json
{
  "AppConfiguration": {
    "IsCloudEnvironment": false,
    "Database": {
      "Host": "localhost",
      "Port": 5432,
      "Database": "worldplanning",
      "Username": "centerdata_user",
      "Password": "your_password",
      "ConnectionString": ""
    },
    "Sftp": {
      "Host": "your-sftp-server.com",
      "Port": 22,
      "Username": "sftp_user",
      "Password": "sftp_password",
      "PrivateKeyPath": "",
      "KnownHostsPath": ""
    },
    "Processing": {
      "RemoteDirectory": "/data/center-files",
      "TempDirectory": "./temp",
      "BatchSize": 1000,
      "DeleteTempFilesAfterProcessing": true,
      "ProcessCurrentDateOnly": true
    }
  },
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.AspNetCore": "Warning"
    }
  }
}
```

#### Cloud Environment (GCP Secret Manager)
For cloud deployments, set `IsCloudEnvironment: true` and configure secrets in GCP Secret Manager:
- `database-connection-string`
- `sftp-host`
- `sftp-username`
- `sftp-password`

### 3. Environment Variables (Optional)
```bash
export ASPNETCORE_ENVIRONMENT=Development
export ConnectionStrings__DefaultConnection="Host=localhost;Database=worldplanning;Username=centerdata_user;Password=your_password"
```

## File Format Requirements

### Expected File Structure
- **File Pattern**: `CenterData_Tadpoles_Center_*.txt` or `*CenterData*Tadpoles*Center*.txt`
- **Format**: Pipe-delimited (|) text files
- **Encoding**: UTF-8
- **Header**: Required with exact column names

### Required Columns (35 total)
```
School_ID|School_Name|Status|Center_Live_Date|Regions|State|School_Number|
Center_Primary_Email|Center_Address_Line_1|Center_Address_Line_2|City|
Postal_Code|Center_Phone_Number|Center_Model|Timezone|Operational_Hours|
Center_Director_Name|Center_Director_Primary_Email|Regional_Manager_Name|
Regional_Manager_Email|Divisional_Vice_President_Name|
Divisional_Vice_President_Email|Primary_Evacuation_Location|
Primary_Evacuation_Location_Address Line_1|Primary_Evacuation_Location_Address_Line_2|
Primary_Evacuation_City|Primary_Evacuation_State|Primary_Evacuation_Postal_Code|
Center_Emergency_Cell_Phone|Secondary_Evacuation_Location|
Secondary_Evacuation_Location_Address_Line_1|
Secondary_Evacuation_Location_Address_Line_2|Secondary_Evacuation_City|
Secondary_Evacuation_State|Secondary_Evacuation_Postal_Code
```

### Field Mapping to Database
| Source Field | Database Column | Type | Notes |
|--------------|-----------------|------|-------|
| School_ID | center_id | bigint | Primary identifier |
| School_Name | center_name | text | Center name |
| Timezone | timezone_name | text | Timezone info |
| Center_Live_Date | center_live_date | datetime | Operational date |
| Regions | regions | text | Geographic regions |
| State | state | double precision | State identifier |
| Center_Primary_Email | center_email | text | Contact email |
| Center_Director_Name | center_director_name | text | Director name |
| Center_Director_Primary_Email | center_director_email | text | Director email |
| Divisional_Vice_President_Name | dvp_name | text | DVP name |
| Divisional_Vice_President_Email | dvp_email | text | DVP email |
| Regional_Manager_Name | regional_manager_name | text | Manager name |
| Regional_Manager_Email | regional_manager_email | text | Manager email |

*Note: Only essential fields are imported. Audit and business logic fields are set to defaults.*

## Usage

### 1. Local File Processing
```bash
# Process a specific local file
dotnet run "/path/to/CenterData_Tadpoles_Center_20250602.txt"
```

### 2. Remote File Processing
```bash
# Process a specific remote file
dotnet run "/remote/path/CenterData_Tadpoles_Center_20250602.txt"
```

### 3. Batch Processing (All Matching Files)
```bash
# Process all matching files from configured remote directory
# In Cloud Run: processes only current date file
# In local dev: processes based on ProcessCurrentDateOnly setting
dotnet run
```

### 4. Docker Deployment
```bash
# Build Docker image
docker build -t centerdata-processor .

# Run container
docker run -e ASPNETCORE_ENVIRONMENT=Production \
  -v /local/config:/app/config \
  centerdata-processor
```

## Operations

### Import Process Flow
1. **File Discovery**: Scan SFTP directory or process specified file
2. **Download**: Retrieve file from SFTP server (if remote)
3. **Validation**: Verify file format and header structure
4. **Parsing**: Extract and map data fields
5. **Upsert**: Insert new records, update existing ones
6. **Deletion**: Remove records not present in source
7. **Cleanup**: Delete temporary files and log results

### Database Operations
- **Upsert Logic**: Uses `center_id` for uniqueness
- **Deletion Logic**: Removes records dropped from source
- **Audit Trail**: Tracks all import operations
- **Batch Processing**: Configurable batch sizes for performance

### Smart File Selection
- **Date-Based Filtering**: Automatically selects current date files in Cloud Run
- **Flexible Configuration**: Control date filtering with `ProcessCurrentDateOnly` setting
- **Environment Awareness**: Different behavior for local vs cloud environments
- **Pattern Recognition**: Extracts dates from filenames (YYYYMMDD format)

### Monitoring and Logging
- **Import Logs**: Stored in `import_logs` table
- **Statistics**: Records processed, inserted, updated, deleted, failed
- **Error Tracking**: Detailed error messages and stack traces
- **Performance Metrics**: Processing times and batch statistics

## Troubleshooting

### Common Issues

#### Connection Errors
```bash
# Test database connection
dotnet run --test-db

# Test SFTP connection
dotnet run --test-sftp
```

#### File Format Issues
- Verify file encoding is UTF-8
- Check column count matches expected (35 columns)
- Ensure pipe delimiter is used consistently
- Validate header row matches expected format

#### Permission Issues
- Ensure database user has INSERT, UPDATE, DELETE permissions
- Verify SFTP user has read access to source directory
- Check file system permissions for temp directory

### Log Analysis
```bash
# View recent import logs
SELECT * FROM import_logs ORDER BY start_time DESC LIMIT 10;

# Check for failed imports
SELECT * FROM import_logs WHERE status = 'Failed';

# View processing statistics
SELECT
  processor_type,
  COUNT(*) as total_imports,
  SUM(records_processed) as total_records,
  AVG(records_processed) as avg_records_per_import
FROM import_logs
WHERE status = 'Completed'
GROUP BY processor_type;
```

## Security Considerations

### SFTP Security
- Use SSH key authentication when possible
- Store credentials in secure configuration (GCP Secret Manager for cloud)
- Implement known_hosts verification
- Use secure file transfer protocols only

### Database Security
- Use dedicated database user with minimal required permissions
- Enable SSL/TLS for database connections
- Regularly rotate database passwords
- Monitor database access logs

### Application Security
- Store sensitive configuration in secure vaults
- Use environment-specific configurations
- Implement proper error handling to avoid information disclosure
- Regular security updates for dependencies

## Performance Tuning

### Database Optimization
- Adjust `BatchSize` in configuration (default: 1000)
- Monitor database connection pool usage
- Consider database indexing for large datasets
- Use connection string parameters for performance

### Processing Optimization
- Configure appropriate temp directory with sufficient space
- Adjust logging levels for production environments
- Monitor memory usage during large file processing
- Consider parallel processing for multiple files

## Support

### Getting Help
- Check application logs for detailed error information
- Review database import_logs table for processing history
- Verify configuration settings match environment requirements
- Test connectivity to SFTP and database servers

### Reporting Issues
When reporting issues, include:
- Application version and environment details
- Relevant log entries and error messages
- File format and size information
- Configuration settings (sanitized)
- Steps to reproduce the issue

---

For additional support or questions, please refer to the project documentation or contact the development team.
