{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.EntityFrameworkCore": "Warning", "Microsoft.Hosting.Lifetime": "Information"}}, "AppConfiguration": {"Environment": "Development", "GcpProjectId": "", "Database": {"ConnectionString": "Host=localhost;Port=5432;Database=worldplanning;Username=postgres;Password=********", "Host": "localhost", "Port": 5432, "Database": "worldplanning", "Username": "postgres", "Password": "********"}, "Sftp": {"Host": "upload.brighthorizons.com", "Port": 22, "Username": "onsharp-sftp", "Password": "w43p95624t", "PrivateKeyPath": "", "PrivateKeyPassphrase": "", "ConnectionTimeoutSeconds": 30, "OperationTimeoutSeconds": 300}, "Processing": {"TempDirectory": "./temp", "BatchSize": 1000, "DeleteTempFilesAfterProcessing": false, "FilePattern": "BrightStar_Tadpoles_Center_*.txt", "RemoteDirectory": "/Non-Eligibility/onsharp-sftp/Non-PROD/Tadpoles/PreProduction/", "ProcessCurrentDateOnly": true}}}