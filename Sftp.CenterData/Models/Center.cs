using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Sftp.CenterData.Models;

/// <summary>
/// Entity model representing a childcare center with essential operational information.
/// Contains core data for center management including identification, contacts, and operational details.
/// </summary>
/// <remarks>
/// This entity model captures essential information about childcare centers including:
///
/// **Basic Center Information:**
/// - Unique identifiers and naming information
/// - Operational dates and timezone information
/// - Geographic and regional classification
///
/// **Staff and Management Contacts:**
/// - Center director information
/// - Regional manager details
/// - Divisional vice president contacts
///
/// **Data Management:**
/// - Import batch tracking for data lineage
/// - Created and updated timestamps for audit trails
/// - Support for upsert and deletion operations
/// - Database column mappings for PostgreSQL storage
///
/// **Deletion Logic:**
/// - Supports deletion of records that are dropped from source files
/// - Uses center_id for tracking and deletion operations
///
/// This model is populated from CenterData Tadpoles Center data files and provides
/// essential center operational information for management and reporting.
/// </remarks>
[Table("center")]
public class Center
{
    /// <summary>
    /// Gets or sets the unique database identifier for this center record.
    /// Primary key for the database table.
    /// </summary>
    [Key]
    [Column("id")]
    public int Id { get; set; }

    /// <summary>
    /// Gets or sets the unique center identifier from the CenterData system.
    /// Maps to School_ID from source file. Used for cross-referencing and deletion tracking.
    /// </summary>
    [Column("center_id")]
    public long CenterId { get; set; }

    /// <summary>
    /// Gets or sets the official name of the childcare center.
    /// Maps to School_Name from source file.
    /// </summary>
    [Column("center_name")]
    public string? CenterName { get; set; }

    /// <summary>
    /// Gets or sets the timezone name in which the center operates.
    /// Maps to Timezone from source file.
    /// </summary>
    [Column("timezone_name")]
    public string? TimezoneName { get; set; }

    /// <summary>
    /// Gets or sets the date when the center became operational.
    /// Maps to Center_Live_Date from source file.
    /// </summary>
    [Column("center_live_date")]
    public DateTime? CenterLiveDate { get; set; }

    /// <summary>
    /// Gets or sets the geographic regions or territories this center serves.
    /// Maps to Regions from source file.
    /// </summary>
    [Column("regions")]
    public string? Regions { get; set; }

    /// <summary>
    /// Gets or sets the state where the center is located.
    /// Maps to State from source file.
    /// </summary>
    [Column("state")]
    public double? State { get; set; }

    /// <summary>
    /// Gets or sets the user who last edited this record.
    /// Audit field for tracking data modifications.
    /// </summary>
    [Column("edited_by")]
    public string? EditedBy { get; set; }

    /// <summary>
    /// Gets or sets the user who created this record.
    /// Audit field for tracking data creation.
    /// </summary>
    [Column("created_by")]
    public double? CreatedBy { get; set; }

    /// <summary>
    /// Gets or sets the date when this record was last edited.
    /// Audit field for tracking modification timestamps.
    /// </summary>
    [Column("edited_date")]
    public string? EditedDate { get; set; }

    /// <summary>
    /// Gets or sets the date when this record was created.
    /// Audit field for tracking creation timestamps.
    /// </summary>
    [Column("created_date")]
    public double? CreatedDate { get; set; }

    /// <summary>
    /// Gets or sets the center type enumeration value.
    /// Categorizes the type of center for operational purposes.
    /// </summary>
    [Column("center_type_enum")]
    public long? CenterTypeEnum { get; set; }

    /// <summary>
    /// Gets or sets the center type enumeration placeholder value.
    /// Additional categorization field for center types.
    /// </summary>
    [Column("center_type_enum_placeholder")]
    public long? CenterTypeEnumPlaceholder { get; set; }

    /// <summary>
    /// Gets or sets the primary email address for the center.
    /// Maps to Center_Primary_Email from source file.
    /// </summary>
    [Column("center_email")]
    public string? CenterEmail { get; set; }

    /// <summary>
    /// Gets or sets the full name of the center director.
    /// Maps to Center_Director_Name from source file.
    /// </summary>
    [Column("center_director_name")]
    public string? CenterDirectorName { get; set; }

    /// <summary>
    /// Gets or sets the email address for the center director.
    /// Maps to Center_Director_Primary_Email from source file.
    /// </summary>
    [Column("center_director_email")]
    public string? CenterDirectorEmail { get; set; }

    /// <summary>
    /// Gets or sets the full name of the divisional vice president.
    /// Maps to Divisional_Vice_President_Name from source file.
    /// </summary>
    [Column("dvp_name")]
    public string? DvpName { get; set; }

    /// <summary>
    /// Gets or sets the email address for the divisional vice president.
    /// Maps to Divisional_Vice_President_Email from source file.
    /// </summary>
    [Column("dvp_email")]
    public string? DvpEmail { get; set; }

    /// <summary>
    /// Gets or sets the full name of the regional manager overseeing this center.
    /// Maps to Regional_Manager_Name from source file.
    /// </summary>
    [Column("regional_manager_name")]
    public string? RegionalManagerName { get; set; }

    /// <summary>
    /// Gets or sets the email address for the regional manager.
    /// Maps to Regional_Manager_Email from source file.
    /// </summary>
    [Column("regional_manager_email")]
    public string? RegionalManagerEmail { get; set; }

    /// <summary>
    /// Gets or sets whether the center is using assessment functionality.
    /// No direct mapping from source file - business logic field.
    /// </summary>
    [Column("is_using_assessment")]
    public bool IsUsingAssessment { get; set; }

    /// <summary>
    /// Gets or sets whether the center is using planning functionality.
    /// No direct mapping from source file - business logic field.
    /// </summary>
    [Column("is_using_planning")]
    public bool IsUsingPlanning { get; set; }

    /// <summary>
    /// Gets or sets the import batch identifier for data lineage tracking.
    /// Links this record to the specific import operation that created or updated it.
    /// </summary>
    [Column("import_batch_id")]
    public string? ImportBatchId { get; set; }
}
