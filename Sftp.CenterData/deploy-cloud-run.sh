#!/bin/bash

# =====================================================
# Cloud Run Job Deployment Script for CenterData SFTP Processor
# =====================================================
#
# This script deploys the CenterData SFTP processor as a Cloud Run job
# to GCP project mapp-dev-457512 with Secret Manager integration.
#
# Prerequisites:
# 1. gcloud CLI installed and authenticated
# 2. Docker installed
# 3. Secrets created in Secret Manager
# 4. Appropriate IAM permissions
#
# Usage:
#   ./deploy-cloud-run.sh
#
# =====================================================

set -e  # Exit on any error

# Configuration
PROJECT_ID="mapp-dev-457512"
REGION="us-central1"
JOB_NAME="centerdata-sftp-processor"
IMAGE_NAME="gcr.io/${PROJECT_ID}/${JOB_NAME}"
SERVICE_ACCOUNT="<EMAIL>"

echo "🚀 Starting Cloud Run Job deployment for CenterData SFTP Processor"
echo "Project: ${PROJECT_ID}"
echo "Region: ${REGION}"
echo "Job Name: ${JOB_NAME}"
echo ""

# Set the project
echo "📋 Setting GCP project..."
gcloud config set project ${PROJECT_ID}

# Enable required APIs
echo "🔧 Enabling required APIs..."
gcloud services enable cloudbuild.googleapis.com
gcloud services enable run.googleapis.com
gcloud services enable secretmanager.googleapis.com
gcloud services enable containerregistry.googleapis.com

# Build and push the Docker image
echo "🐳 Building Docker image..."
docker build -t ${IMAGE_NAME} .

echo "📤 Pushing Docker image to Container Registry..."
docker push ${IMAGE_NAME}

# Deploy Cloud Run Job
echo "☁️ Deploying Cloud Run Job..."
gcloud run jobs create ${JOB_NAME} \
    --image=${IMAGE_NAME} \
    --region=${REGION} \
    --service-account=${SERVICE_ACCOUNT} \
    --set-env-vars="ASPNETCORE_ENVIRONMENT=Development" \
    --set-secrets="DATABASE_CONNECTION_STRING=postgresdb-connection:latest" \
    --set-secrets="SFTP_PASSWORD=sftp-password:latest" \
    --memory=1Gi \
    --cpu=1 \
    --max-retries=3 \
    --parallelism=1 \
    --task-count=1 \
    --task-timeout=3600 \
    --labels="app=centerdata-processor,environment=Development,type=sftp-import"

echo ""
echo "✅ Cloud Run Job deployed successfully!"
echo ""
echo "📋 Next steps:"
echo "1. Create secrets in Secret Manager:"
echo "   - centerdata-db-connection"
echo "   - centerdata-sftp-password"
echo ""
echo "2. Test the job:"
echo "   gcloud run jobs execute ${JOB_NAME} --region=${REGION}"
echo ""
echo "3. Set up Cloud Scheduler for daily runs:"
echo "   gcloud scheduler jobs create http centerdata-daily-import \\"
echo "     --schedule='0 6 * * *' \\"
echo "     --uri='https://${REGION}-run.googleapis.com/apis/run.googleapis.com/v1/namespaces/${PROJECT_ID}/jobs/${JOB_NAME}:run' \\"
echo "     --http-method=POST \\"
echo "     --oauth-service-account-email=${SERVICE_ACCOUNT}"
echo ""
echo "🎯 Deployment complete!"
