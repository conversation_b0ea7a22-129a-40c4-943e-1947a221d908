#!/bin/bash

# =====================================================
# Cloud Run Job Deployment Script for CenterData SFTP Processor
# =====================================================
#
# This script deploys the CenterData SFTP processor as a Cloud Run job
# to GCP project mapp-dev-457512 with Secret Manager integration.
#
# Prerequisites:
# 1. gcloud CLI installed and authenticated
# 2. Docker installed
# 3. Secrets created in Secret Manager
# 4. Appropriate IAM permissions
#
# Usage:
#   ./deploy-cloud-run.sh
#
# =====================================================

set -e  # Exit on any error

# Configuration
PROJECT_ID="mapp-dev-457512"
REGION="us-central1"
JOB_NAME="centerdata-sftp-processor"
REPOSITORY_NAME="sftp-processors"
IMAGE_NAME="${REGION}-docker.pkg.dev/${PROJECT_ID}/${REPOSITORY_NAME}/${JOB_NAME}"
SERVICE_ACCOUNT="<EMAIL>"

echo "🚀 Starting Cloud Run Job deployment for CenterData SFTP Processor"
echo "Project: ${PROJECT_ID}"
echo "Region: ${REGION}"
echo "Job Name: ${JOB_NAME}"
echo "Repository: ${REPOSITORY_NAME}"
echo "Image: ${IMAGE_NAME}"
echo ""

# Set the project
echo "📋 Setting GCP project..."
gcloud config set project ${PROJECT_ID}

# Enable required APIs
echo "🔧 Enabling required APIs..."
gcloud services enable cloudbuild.googleapis.com
gcloud services enable run.googleapis.com
gcloud services enable secretmanager.googleapis.com
gcloud services enable artifactregistry.googleapis.com

# Create Artifact Registry repository if it doesn't exist
echo "🏗️ Setting up Artifact Registry repository..."
if ! gcloud artifacts repositories describe ${REPOSITORY_NAME} --location=${REGION} >/dev/null 2>&1; then
    echo "Creating Artifact Registry repository: ${REPOSITORY_NAME}"
    gcloud artifacts repositories create ${REPOSITORY_NAME} \
        --repository-format=docker \
        --location=${REGION} \
        --description="Docker repository for SFTP processors"
else
    echo "Artifact Registry repository ${REPOSITORY_NAME} already exists"
fi

# Configure Docker authentication for Artifact Registry
echo "🔐 Configuring Docker authentication for Artifact Registry..."
gcloud auth configure-docker ${REGION}-docker.pkg.dev

# Build and push the Docker image
echo "🐳 Building Docker image for linux/amd64 platform..."
# Build from solution root to include Sftp.Core dependency
# Explicitly target linux/amd64 for Cloud Run compatibility
docker build --platform linux/amd64 -f Dockerfile -t ${IMAGE_NAME}:latest -t ${IMAGE_NAME}:$(date +%Y%m%d-%H%M%S) ..

echo "📤 Pushing Docker image to Artifact Registry..."
docker push ${IMAGE_NAME}:latest
docker push ${IMAGE_NAME}:$(date +%Y%m%d-%H%M%S)

# Deploy Cloud Run Job
echo "☁️ Deploying Cloud Run Job..."

# Check if job already exists
echo "🔍 Checking if job ${JOB_NAME} exists in region ${REGION}..."
if gcloud run jobs describe ${JOB_NAME} --region=${REGION} >/dev/null 2>&1; then
    echo "📝 Job exists - Deleting and recreating Cloud Run job..."
    gcloud run jobs delete ${JOB_NAME} --region=${REGION} --quiet
    echo "🗑️  Existing job deleted"
fi

echo "🆕 Creating Cloud Run job..."

    # Check if secrets exist before creating job
    echo "🔐 Checking required secrets..."
    SECRETS_EXIST=true

    if ! gcloud secrets describe postgresdb-connection >/dev/null 2>&1; then
        echo "⚠️  Warning: Secret 'postgresdb-connection' does not exist"
        SECRETS_EXIST=false
    fi

    if ! gcloud secrets describe sftp-password >/dev/null 2>&1; then
        echo "⚠️  Warning: Secret 'sftp-password' does not exist"
        SECRETS_EXIST=false
    fi

    if [ "$SECRETS_EXIST" = true ]; then
        echo "✅ All secrets exist - Creating job with secrets..."
        gcloud run jobs create ${JOB_NAME} \
            --image=${IMAGE_NAME}:latest \
            --region=${REGION} \
            --service-account=${SERVICE_ACCOUNT} \
            --set-env-vars="ASPNETCORE_ENVIRONMENT=Development" \
            --set-secrets="DATABASE_CONNECTION_STRING=postgresdb-connection:latest" \
            --set-secrets="SFTP_PASSWORD=sftp-password:latest" \
            --memory=1Gi \
            --cpu=1 \
            --max-retries=3 \
            --parallelism=1 \
            --task-timeout=3600 \
            --labels="app=centerdata-processor,environment=Development,type=sftp-import"
    else
        echo "⚠️  Creating job without secrets (you can add them later)..."
        gcloud run jobs create ${JOB_NAME} \
            --image=${IMAGE_NAME}:latest \
            --region=${REGION} \
            --service-account=${SERVICE_ACCOUNT} \
            --set-env-vars="ASPNETCORE_ENVIRONMENT=Development" \
            --memory=1Gi \
            --cpu=1 \
            --max-retries=3 \
            --parallelism=1 \
            --task-timeout=3600 \
            --labels="app=centerdata-processor,environment=Development,type=sftp-import"
    fi
    echo "✅ Cloud Run job created successfully"
fi

echo ""
echo "✅ Cloud Run Job deployment completed successfully!"
echo ""
echo "📋 Deployment Summary:"
echo "🏗️  Artifact Registry Repository: ${REPOSITORY_NAME}"
echo "🐳 Docker Image: ${IMAGE_NAME}:latest"
echo "☁️  Cloud Run Job: ${JOB_NAME}"
echo "🌍 Region: ${REGION}"
echo ""
echo "📋 Next steps:"
echo "1. Create secrets in Secret Manager:"
echo "   - postgresdb-connection"
echo "   - sftp-password"
echo ""
echo "2. Test the job:"
echo "   gcloud run jobs execute ${JOB_NAME} --region=${REGION}"
echo ""
echo "3. View job logs:"
echo "   gcloud logging read \"resource.type=cloud_run_job AND resource.labels.job_name=${JOB_NAME}\" --limit=50 --format=\"table(timestamp,textPayload)\""
echo ""
echo "4. Set up Cloud Scheduler for daily runs:"
echo "   gcloud scheduler jobs create http centerdata-daily-import \\"
echo "     --schedule='0 6 * * *' \\"
echo "     --uri='https://${REGION}-run.googleapis.com/apis/run.googleapis.com/v1/namespaces/${PROJECT_ID}/jobs/${JOB_NAME}:run' \\"
echo "     --http-method=POST \\"
echo "     --oauth-service-account-email=${SERVICE_ACCOUNT}"
echo ""
echo "5. View Artifact Registry images:"
echo "   gcloud artifacts docker images list ${REGION}-docker.pkg.dev/${PROJECT_ID}/${REPOSITORY_NAME}"
echo ""
echo "🎯 Deployment complete!"
