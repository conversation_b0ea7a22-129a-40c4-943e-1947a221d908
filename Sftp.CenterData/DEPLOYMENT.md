# CenterData SFTP Processor - Cloud Run Deployment Guide

This guide walks you through deploying the CenterData SFTP processor as a Cloud Run job to GCP project `mapp-dev-457512`.

## 🎯 Overview

The deployment includes:
- **Cloud Run Job**: Processes SFTP files and imports to PostgreSQL
- **Secret Manager**: Stores sensitive credentials securely
- **Cloud Scheduler**: Runs daily at 6 AM UTC for current date files
- **IAM**: Proper service account and permissions

## 📋 Prerequisites

1. **gcloud CLI** installed and authenticated
2. **Docker** installed and running
3. **PostgreSQL database** accessible from GCP
4. **SFTP credentials** for `upload.brighthorizons.com`
5. **GCP Project** `mapp-dev-457512` with billing enabled

## 🚀 Deployment Steps

### Step 1: Setup Secrets

```bash
# Make script executable
chmod +x setup-secrets.sh

# Run secret setup
./setup-secrets.sh
```

**Important**: Update the database connection string with your actual PostgreSQL details:

```bash
# Replace with your actual database details
gcloud secrets versions add centerdata-db-connection --data-file=- <<< \
'Host=YOUR_POSTGRES_HOST;Port=5432;Database=centerdata_db;Username=centerdata_user;Password=YOUR_DB_PASSWORD'
```

### Step 2: Deploy Cloud Run Job

```bash
# Make script executable
chmod +x deploy-cloud-run.sh

# Run deployment
./deploy-cloud-run.sh
```

### Step 3: Setup Daily Scheduler

```bash
# Make script executable
chmod +x setup-scheduler.sh

# Setup scheduler
./setup-scheduler.sh
```

## 🔐 Secret Manager Configuration

The deployment uses these secrets:

| Secret Name | Description | Example Value |
|-------------|-------------|---------------|
| `centerdata-db-connection` | PostgreSQL connection string | `Host=********;Port=5432;Database=centerdata_db;Username=centerdata_user;Password=**********` |
| `centerdata-sftp-password` | SFTP server password | `w43p95624t` |

## ⚙️ Configuration

### Production Settings (`appsettings.Production.json`)

```json
{
  "AppConfiguration": {
    "Environment": "Production",
    "GcpProjectId": "mapp-dev-457512",
    "Database": {
      "ConnectionString": "",  // Loaded from Secret Manager
      "Host": "your-postgres-host",
      "Port": 5432,
      "Database": "centerdata_db",
      "Username": "centerdata_user",
      "Password": ""  // Loaded from Secret Manager
    },
    "Sftp": {
      "Host": "upload.brighthorizons.com",
      "Port": 22,
      "Username": "onsharp-sftp",
      "Password": "",  // Loaded from Secret Manager
      "ConnectionTimeoutSeconds": 30,
      "OperationTimeoutSeconds": 300
    },
    "Processing": {
      "TempDirectory": "/tmp",
      "BatchSize": 1000,
      "DeleteTempFilesAfterProcessing": true,
      "FilePattern": "BrightStar_Tadpoles_Center_*.txt",
      "RemoteDirectory": "/Non-Eligibility/onsharp-sftp/Non-PROD/Tadpoles/PreProduction/",
      "ProcessCurrentDateOnly": true
    }
  }
}
```

## 🔧 Management Commands

### Manual Execution
```bash
# Run the job manually
gcloud run jobs execute centerdata-sftp-processor --region=us-central1
```

### View Logs
```bash
# View Cloud Run job logs
gcloud logging read 'resource.type="cloud_run_job"' --limit=50 --format='table(timestamp,severity,textPayload)'

# View scheduler logs
gcloud logging read 'resource.type="cloud_scheduler_job"' --limit=50 --format='table(timestamp,severity,textPayload)'
```

### Monitor Execution
```bash
# List job executions
gcloud run jobs executions list --job=centerdata-sftp-processor --region=us-central1

# Get execution details
gcloud run jobs executions describe EXECUTION_NAME --region=us-central1
```

## 📊 Monitoring & Alerting

### Key Metrics to Monitor
- **Job Success Rate**: Percentage of successful executions
- **Processing Time**: Duration of each execution
- **Records Processed**: Number of center records imported
- **Error Rate**: Failed executions and error messages

### Cloud Console Links
- **Cloud Run Jobs**: https://console.cloud.google.com/run/jobs?project=mapp-dev-457512
- **Cloud Scheduler**: https://console.cloud.google.com/cloudscheduler?project=mapp-dev-457512
- **Secret Manager**: https://console.cloud.google.com/security/secret-manager?project=mapp-dev-457512
- **Logs**: https://console.cloud.google.com/logs?project=mapp-dev-457512

## 🔍 Troubleshooting

### Common Issues

1. **Secret Access Denied**
   ```bash
   # Check service account permissions
   gcloud projects get-iam-policy mapp-dev-457512 --flatten="bindings[].members" --filter="bindings.members:<EMAIL>"
   ```

2. **Database Connection Failed**
   ```bash
   # Test secret value
   gcloud secrets versions access latest --secret=centerdata-db-connection
   ```

3. **SFTP Connection Failed**
   ```bash
   # Test SFTP secret
   gcloud secrets versions access latest --secret=centerdata-sftp-password
   ```

### Debug Commands
```bash
# Check job status
gcloud run jobs describe centerdata-sftp-processor --region=us-central1

# View recent executions
gcloud run jobs executions list --job=centerdata-sftp-processor --region=us-central1 --limit=5

# Get detailed logs for specific execution
gcloud logging read 'resource.type="cloud_run_job" AND resource.labels.job_name="centerdata-sftp-processor"' --limit=100
```

## 🎯 Success Criteria

After deployment, verify:
- ✅ Cloud Run job deploys successfully
- ✅ Secrets are accessible by service account
- ✅ Manual execution processes files correctly
- ✅ Scheduler triggers job daily at 6 AM UTC
- ✅ Data is imported to PostgreSQL database
- ✅ Import logs show successful processing

## 📞 Support

For issues or questions:
1. Check the troubleshooting section above
2. Review Cloud Run job logs
3. Verify secret manager configuration
4. Test database and SFTP connectivity
