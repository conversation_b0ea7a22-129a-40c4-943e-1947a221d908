# Azure DevOps Pipeline Setup Guide - Generic SFTP Processor Deployment

This guide walks you through setting up a reusable Azure DevOps pipeline to deploy any SFTP processor as a Cloud Run job to GCP. The pipeline is designed to be generic and reusable across multiple SFTP processing jobs.

## 🎯 Overview

The **generic pipeline** supports multiple SFTP processors with:
- **Reusable Scripts**: Single `deploy-azure-devops.sh` for all processors
- **Configurable Variables**: Job name, secrets, and paths per processor
- **Template-Based**: Easy to copy and customize for new processors
- **Consistent Deployment**: Same process for all SFTP jobs

### Pipeline Features:
- **Build**: Compile .NET application and run tests
- **Deploy**: Build Docker image and deploy to Cloud Run
- **Test**: Execute the job to verify deployment
- **Monitor**: Provide deployment status and management commands

## 📋 Prerequisites

1. **Azure DevOps Organization** with project access
2. **GCP Service Account Key** for `<EMAIL>`
3. **Secret Manager Secrets** created for each processor in GCP
4. **Repository** with your SFTP processor code
5. **Generic deployment script** (`deploy-azure-devops.sh`) in each processor project

## 🔄 Creating New SFTP Processors

### For Each New SFTP Processor:

1. **Copy the template pipeline**:
   ```bash
   cp azure-pipelines-template.yml your-new-processor/azure-pipelines.yml
   ```

2. **Update the variables** in `azure-pipelines.yml`:
   ```yaml
   variables:
     CLOUD_RUN_JOB_NAME: 'your-processor-name'           # e.g., 'employee-sftp-processor'
     SECRET_DB_NAME: 'your-processor-db-connection'      # e.g., 'employee-db-connection'
     SECRET_SFTP_NAME: 'your-processor-sftp-password'    # e.g., 'employee-sftp-password'
   ```

3. **Copy the deployment script**:
   ```bash
   cp Sftp.CenterData/deploy-azure-devops.sh your-new-processor/
   ```

4. **Create processor-specific secrets** in GCP:
   ```bash
   # Create database connection secret
   gcloud secrets create your-processor-db-connection --data-file=-

   # Create SFTP password secret
   gcloud secrets create your-processor-sftp-password --data-file=-
   ```

5. **Update trigger paths** in the pipeline to match your project structure

### Example Configurations:

#### **CenterData Processor** (Current)
```yaml
CLOUD_RUN_JOB_NAME: 'centerdata-sftp-processor'
SECRET_DB_NAME: 'centerdata-db-connection'
SECRET_SFTP_NAME: 'centerdata-sftp-password'
# Trigger paths: Sftp.CenterData/*
```

#### **Employee Processor** (Example)
```yaml
CLOUD_RUN_JOB_NAME: 'employee-sftp-processor'
SECRET_DB_NAME: 'employee-db-connection'
SECRET_SFTP_NAME: 'employee-sftp-password'
# Trigger paths: Sftp.Employee/*
```

#### **Payroll Processor** (Example)
```yaml
CLOUD_RUN_JOB_NAME: 'payroll-sftp-processor'
SECRET_DB_NAME: 'payroll-db-connection'
SECRET_SFTP_NAME: 'payroll-sftp-password'
# Trigger paths: Sftp.Payroll/*
```

## 🔧 Setup Steps

### Step 1: Create Service Account Key

1. **Download Service Account Key**:
   ```bash
   # Create and download key for the compute service account
   gcloud iam service-accounts keys create gcp-service-account-key.json \
     --iam-account=<EMAIL> \
     --project=mapp-dev-457512
   ```

2. **Grant Required Permissions** (if not already granted):
   ```bash
   # Grant necessary roles to the service account
   gcloud projects add-iam-policy-binding mapp-dev-457512 \
     --member="serviceAccount:<EMAIL>" \
     --role="roles/run.admin"
   
   gcloud projects add-iam-policy-binding mapp-dev-457512 \
     --member="serviceAccount:<EMAIL>" \
     --role="roles/storage.admin"
   
   gcloud projects add-iam-policy-binding mapp-dev-457512 \
     --member="serviceAccount:<EMAIL>" \
     --role="roles/secretmanager.secretAccessor"
   ```

### Step 2: Configure Azure DevOps

#### **2.1 Upload Service Account Key**

1. Go to **Azure DevOps** → **Project** → **Pipelines** → **Library**
2. Click **Secure files** tab
3. Click **+ Secure file**
4. Upload `gcp-service-account-key.json`
5. Set permissions for the pipeline to access this file

#### **2.2 Create Pipeline**

1. Go to **Pipelines** → **New pipeline**
2. Select your repository source (GitHub, Azure Repos, etc.)
3. Choose **Existing Azure Pipelines YAML file**
4. Select `/Sftp.CenterData/azure-pipelines.yml`
5. Click **Continue** and **Save**

#### **2.3 Configure Pipeline Variables** (Optional)

If you need to override default values:

| Variable Name | Value | Description |
|---------------|-------|-------------|
| `GCP_PROJECT_ID` | `mapp-dev-457512` | GCP Project ID |
| `GCP_REGION` | `us-central1` | Deployment region |
| `CLOUD_RUN_JOB_NAME` | `centerdata-sftp-processor` | Job name |

### Step 3: Setup Secrets in GCP (if not done)

Run the secret setup script:
```bash
cd Sftp.CenterData
./setup-secrets.sh
```

Update the database connection string:
```bash
gcloud secrets versions add centerdata-db-connection --data-file=- <<< \
'Host=YOUR_POSTGRES_HOST;Port=5432;Database=centerdata_db;Username=centerdata_user;Password=YOUR_DB_PASSWORD'
```

### Step 4: Configure Pipeline Triggers

The pipeline is configured to trigger on:
- **Main branch**: Full build and deploy
- **Develop branch**: Build only
- **Path filters**: Only when `Sftp.CenterData/*` or `Sftp.Core/*` files change

## 🚀 Pipeline Execution

### Automatic Deployment
- **Push to main branch** → Automatic build and deploy
- **Pull request** → Build and test only

### Manual Deployment
1. Go to **Pipelines** → **Your Pipeline**
2. Click **Run pipeline**
3. Select branch and click **Run**

## 📊 Pipeline Stages

### **Stage 1: Build and Test**
- ✅ Checkout source code
- ✅ Install .NET SDK 9.0
- ✅ Restore NuGet packages
- ✅ Build application
- ✅ Run unit tests
- ✅ Publish code coverage

### **Stage 2: Deploy to GCP**
- ✅ Install Google Cloud SDK
- ✅ Authenticate with service account
- ✅ Build Docker image
- ✅ Push to Google Container Registry
- ✅ Deploy/update Cloud Run job
- ✅ Test deployment
- ✅ Output summary

## 🔍 Monitoring and Troubleshooting

### **View Pipeline Logs**
1. Go to **Pipelines** → **Your Pipeline**
2. Click on a specific run
3. View logs for each stage/task

### **Common Issues**

#### **Authentication Failed**
```bash
# Verify service account key is uploaded correctly
# Check that the secure file name matches in the pipeline
```

#### **Permission Denied**
```bash
# Verify service account has required roles:
gcloud projects get-iam-policy mapp-dev-457512 \
  --flatten="bindings[].members" \
  --filter="bindings.members:<EMAIL>"
```

#### **Docker Build Failed**
```bash
# Check Dockerfile syntax and dependencies
# Verify base images are accessible
```

#### **Cloud Run Deployment Failed**
```bash
# Check if secrets exist in Secret Manager
gcloud secrets list --project=mapp-dev-457512

# Verify service account can access secrets
gcloud secrets versions access latest --secret=centerdata-db-connection
```

### **Debug Commands**

```bash
# Check Cloud Run job status
gcloud run jobs describe centerdata-sftp-processor --region=us-central1

# View recent executions
gcloud run jobs executions list --job=centerdata-sftp-processor --region=us-central1

# View logs
gcloud logging read 'resource.type="cloud_run_job"' --limit=50
```

## 📋 Pipeline Features

### **✅ Security Best Practices**
- Service account authentication
- Secure file handling for credentials
- No hardcoded secrets in pipeline
- Least privilege access

### **✅ Deployment Safety**
- Build validation before deploy
- Automatic testing after deployment
- Rollback capability via image tags
- Environment-specific configurations

### **✅ Monitoring & Observability**
- Detailed pipeline logs
- Deployment status reporting
- Cloud Run execution testing
- Error handling and reporting

## 🎯 Success Criteria

After pipeline setup, verify:
- ✅ Pipeline triggers on code changes
- ✅ Build stage completes successfully
- ✅ Docker image builds and pushes to GCR
- ✅ Cloud Run job deploys successfully
- ✅ Test execution runs without errors
- ✅ Deployment summary shows correct details

## 📞 Support

For pipeline issues:
1. Check Azure DevOps pipeline logs
2. Verify GCP service account permissions
3. Test GCP authentication manually
4. Review Secret Manager configuration
5. Check Cloud Run job logs in GCP Console

## 🔗 Useful Links

- **Azure DevOps**: https://dev.azure.com/
- **GCP Console**: https://console.cloud.google.com/run/jobs?project=mapp-dev-457512
- **Cloud Run Logs**: https://console.cloud.google.com/logs?project=mapp-dev-457512
- **Secret Manager**: https://console.cloud.google.com/security/secret-manager?project=mapp-dev-457512
