#!/bin/bash

# CenterData SFTP Processor Setup Script
# This script helps set up the development environment for the CenterData SFTP Processor

set -e

echo "🚀 CenterData SFTP Processor Setup"
echo "=================================="

# Check prerequisites
echo "📋 Checking prerequisites..."

# Check .NET 9
if ! command -v dotnet &> /dev/null; then
    echo "❌ .NET SDK not found. Please install .NET 9 SDK from https://dotnet.microsoft.com/download"
    exit 1
fi

DOTNET_VERSION=$(dotnet --version)
echo "✅ .NET SDK found: $DOTNET_VERSION"

# Check PostgreSQL
if ! command -v psql &> /dev/null; then
    echo "⚠️  PostgreSQL client not found. Please install PostgreSQL client tools."
    echo "   On Ubuntu/Debian: sudo apt-get install postgresql-client"
    echo "   On macOS: brew install postgresql"
    echo "   On Windows: Download from https://www.postgresql.org/download/"
fi

# Create project structure
echo "📁 Setting up project structure..."

# Create temp directory
mkdir -p temp
echo "✅ Created temp directory"

# Create logs directory
mkdir -p logs
echo "✅ Created logs directory"

# Create sample data directory
mkdir -p sample-data
echo "✅ Created sample-data directory"

# Restore dependencies
echo "📦 Restoring dependencies..."
dotnet restore
echo "✅ Dependencies restored"

# Build project
echo "🔨 Building project..."
dotnet build
if [ $? -eq 0 ]; then
    echo "✅ Build successful"
else
    echo "❌ Build failed"
    exit 1
fi

# Create sample configuration
echo "⚙️  Creating sample configuration..."

cat > appsettings.Development.json << 'EOF'
{
  "AppConfiguration": {
    "IsCloudEnvironment": false,
    "Database": {
      "Host": "localhost",
      "Port": 5432,
      "Database": "worldplanning",
      "Username": "centerdata_user",
      "Password": "your_password_here",
      "ConnectionString": ""
    },
    "Sftp": {
      "Host": "your-sftp-server.com",
      "Port": 22,
      "Username": "your_sftp_username",
      "Password": "your_sftp_password",
      "PrivateKeyPath": "",
      "KnownHostsPath": ""
    },
    "Processing": {
      "RemoteDirectory": "/data/center-files",
      "TempDirectory": "./temp",
      "BatchSize": 1000,
      "DeleteTempFilesAfterProcessing": true
    }
  },
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.AspNetCore": "Warning",
      "Sftp.CenterData": "Debug"
    }
  }
}
EOF

echo "✅ Created appsettings.Development.json"

# Create sample data file
echo "📄 Creating sample data file..."

cat > sample-data/sample_center_data.txt << 'EOF'
School_ID|School_Name|Status|Center_Live_Date|Regions|State|School_Number|Center_Primary_Email|Center_Address_Line_1|Center_Address_Line_2|City|Postal_Code|Center_Phone_Number|Center_Model|Timezone|Operational_Hours|Center_Director_Name|Center_Director_Primary_Email|Regional_Manager_Name|Regional_Manager_Email|Divisional_Vice_President_Name|Divisional_Vice_President_Email|Primary_Evacuation_Location|Primary_Evacuation_Location_Address Line_1|Primary_Evacuation_Location_Address_Line_2|Primary_Evacuation_City|Primary_Evacuation_State|Primary_Evacuation_Postal_Code|Center_Emergency_Cell_Phone|Secondary_Evacuation_Location|Secondary_Evacuation_Location_Address_Line_1|Secondary_Evacuation_Location_Address_Line_2|Secondary_Evacuation_City|Secondary_Evacuation_State|Secondary_Evacuation_Postal_Code
12345|Sample Childcare Center|Active|2024-01-15|Region North|1.0|SC001|<EMAIL>|123 Main Street||Sample City|12345|(*************|Standard|America/New_York|6:00 AM - 6:00 PM|Jane Director|<EMAIL>|John Manager|<EMAIL>|Sarah VP|<EMAIL>|Community Center|456 Oak Avenue||Sample City|1.0|12345|(*************|Library|789 Pine Street||Sample City|1.0|12345
67890|Another Center|Active|2024-02-01|Region South|2.0|SC002|<EMAIL>|456 Oak Street|Suite 100|Another City|67890|(*************|Premium|America/Chicago|7:00 AM - 7:00 PM|Bob Director|<EMAIL>|Alice Manager|<EMAIL>|Mike VP|<EMAIL>|School Gym|321 Elm Street||Another City|2.0|67890|(*************|Fire Station|654 Maple Avenue||Another City|2.0|67890
EOF

echo "✅ Created sample data file: sample-data/sample_center_data.txt"

# Create database setup script
echo "🗄️  Creating database setup script..."

cat > setup-database.sql << 'EOF'
-- CenterData Database Setup Script
-- Run this script to create the database and user for the CenterData SFTP Processor

-- Create database
CREATE DATABASE worldplanning;

-- Create user
CREATE USER centerdata_user WITH PASSWORD 'your_password_here';

-- Grant privileges
GRANT ALL PRIVILEGES ON DATABASE worldplanning TO centerdata_user;

-- Connect to the database and grant schema privileges
\c worldplanning;
GRANT ALL ON SCHEMA public TO centerdata_user;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO centerdata_user;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO centerdata_user;

-- Grant future privileges
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO centerdata_user;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON SEQUENCES TO centerdata_user;

SELECT 'Database setup completed successfully!' as status;
EOF

echo "✅ Created database setup script: setup-database.sql"

# Create run scripts
echo "🏃 Creating run scripts..."

cat > run-sample.sh << 'EOF'
#!/bin/bash
# Run the processor with sample data

echo "🚀 Running CenterData processor with sample data..."
dotnet run "./sample-data/sample_center_data.txt"
EOF

chmod +x run-sample.sh
echo "✅ Created run-sample.sh"

cat > run-remote.sh << 'EOF'
#!/bin/bash
# Run the processor to fetch and process files from SFTP

echo "🚀 Running CenterData processor for remote files..."
echo "Make sure to configure SFTP settings in appsettings.Development.json first!"
dotnet run
EOF

chmod +x run-remote.sh
echo "✅ Created run-remote.sh"

# Create Docker files
echo "🐳 Creating Docker configuration..."

cat > Dockerfile << 'EOF'
FROM mcr.microsoft.com/dotnet/aspnet:9.0 AS base
WORKDIR /app

FROM mcr.microsoft.com/dotnet/sdk:9.0 AS build
WORKDIR /src
COPY ["Sftp.CenterData.csproj", "."]
RUN dotnet restore "./Sftp.CenterData.csproj"
COPY . .
WORKDIR "/src/."
RUN dotnet build "Sftp.CenterData.csproj" -c Release -o /app/build

FROM build AS publish
RUN dotnet publish "Sftp.CenterData.csproj" -c Release -o /app/publish

FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .
ENTRYPOINT ["dotnet", "Sftp.CenterData.dll"]
EOF

echo "✅ Created Dockerfile"

cat > .dockerignore << 'EOF'
**/.dockerignore
**/.env
**/.git
**/.gitignore
**/.project
**/.settings
**/.toolstarget
**/.vs
**/.vscode
**/*.*proj.user
**/*.dbmdl
**/*.jfm
**/azds.yaml
**/bin
**/charts
**/docker-compose*
**/Dockerfile*
**/node_modules
**/npm-debug.log
**/obj
**/secrets.dev.yaml
**/values.dev.yaml
LICENSE
README.md
temp/
logs/
sample-data/
EOF

echo "✅ Created .dockerignore"

# Final instructions
echo ""
echo "🎉 Setup completed successfully!"
echo ""
echo "📋 Next Steps:"
echo "1. 🗄️  Set up PostgreSQL database:"
echo "   psql -U postgres -f setup-database.sql"
echo ""
echo "2. ⚙️  Configure your settings in appsettings.Development.json:"
echo "   - Update database password"
echo "   - Configure SFTP server details"
echo ""
echo "3. 🧪 Test with sample data:"
echo "   ./run-sample.sh"
echo ""
echo "4. 🌐 Process remote files (after SFTP configuration):"
echo "   ./run-remote.sh"
echo ""
echo "5. 🐳 Build Docker image (optional):"
echo "   docker build -t centerdata-processor ."
echo ""
echo "📖 For detailed documentation, see README.md"
echo ""
echo "⚠️  Remember to:"
echo "   - Update passwords in configuration files"
echo "   - Configure SFTP server access"
echo "   - Test database connectivity"
echo ""
echo "✅ Happy processing! 🚀"
