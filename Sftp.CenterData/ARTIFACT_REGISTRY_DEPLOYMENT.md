# Sftp.CenterData - Artifact Registry Deployment Guide

## 🎯 Overview

This guide covers deploying the Sftp.CenterData image to Google Cloud Artifact Registry and then to Cloud Run. The deployment scripts have been updated to use Artifact Registry instead of the legacy Google Container Registry (GCR).

## 📋 Prerequisites

1. **Google Cloud CLI** installed and authenticated
2. **Docker** installed and running
3. **GCP Project** with billing enabled
4. **Required permissions**:
   - Artifact Registry Admin
   - Cloud Run Admin
   - Service Account User
   - Secret Manager Admin

## 🏗️ Artifact Registry Setup

### Automatic Setup (Recommended)

The deployment scripts now automatically create the Artifact Registry repository:

```bash
# The scripts will create this repository automatically
Repository Name: sftp-processors
Location: us-central1
Format: Docker
```

### Manual Setup (Optional)

If you prefer to create the repository manually:

```bash
# Set your project
export PROJECT_ID="mapp-dev-457512"
export REGION="us-central1"
export REPOSITORY_NAME="sftp-processors"

# Create Artifact Registry repository
gcloud artifacts repositories create ${REPOSITORY_NAME} \
    --repository-format=docker \
    --location=${REGION} \
    --description="Docker repository for SFTP processors" \
    --project=${PROJECT_ID}

# Configure Docker authentication
gcloud auth configure-docker ${REGION}-docker.pkg.dev
```

## 🚀 Deployment Options

### Option 1: Local Deployment Script

Use the updated `deploy-cloud-run.sh` script:

```bash
# Make script executable
chmod +x deploy-cloud-run.sh

# Run deployment
./deploy-cloud-run.sh
```

**What the script does:**
1. ✅ Enables required APIs (including Artifact Registry)
2. ✅ Creates Artifact Registry repository if it doesn't exist
3. ✅ Configures Docker authentication for Artifact Registry
4. ✅ Builds Docker image with timestamp tags
5. ✅ Pushes image to Artifact Registry
6. ✅ Creates or updates Cloud Run job
7. ✅ Provides management commands and URLs

### Option 2: Azure DevOps Pipeline

Use the updated `deploy-azure-devops.sh` script:

```bash
# Set required environment variables
export CLOUD_RUN_JOB_NAME="centerdata-sftp-processor"
export SECRET_DB_NAME="postgresdb-connection"
export SECRET_SFTP_NAME="sftp-password"

# Run deployment
./deploy-azure-devops.sh
```

**Additional features:**
- ✅ Automatic test execution after deployment
- ✅ Comprehensive error handling
- ✅ Build ID tracking for versioning
- ✅ Pipeline-friendly output and logging

## 🐳 Image Management

### Image Naming Convention

```
{REGION}-docker.pkg.dev/{PROJECT_ID}/{REPOSITORY_NAME}/{JOB_NAME}
```

**Example:**
```
us-central1-docker.pkg.dev/mapp-dev-457512/sftp-processors/centerdata-sftp-processor
```

### Image Tags

The deployment creates multiple tags:
- `latest` - Always points to the most recent build
- `YYYYMMDD-HHMMSS` - Timestamp-based versioning for rollbacks

### View Images

```bash
# List all images in the repository
gcloud artifacts docker images list \
    us-central1-docker.pkg.dev/mapp-dev-457512/sftp-processors

# View specific image details
gcloud artifacts docker images describe \
    us-central1-docker.pkg.dev/mapp-dev-457512/sftp-processors/centerdata-sftp-processor:latest
```

## 🔧 Configuration

### Environment Variables

The deployment scripts support these environment variables:

```bash
# Required
export CLOUD_RUN_JOB_NAME="centerdata-sftp-processor"

# Optional (with defaults)
export GCP_PROJECT_ID="mapp-dev-457512"
export GCP_REGION="us-central1"
export REPOSITORY_NAME="sftp-processors"
export SECRET_DB_NAME="postgresdb-connection"
export SECRET_SFTP_NAME="sftp-password"
```

### Secret Manager Integration

The Cloud Run job uses these secrets:
- `postgresdb-connection` - Database connection string
- `sftp-password` - SFTP server password

Create secrets before deployment:
```bash
# Database connection string
echo "Host=your-db-host;Port=5432;Database=centerdata_db;Username=centerdata_user;Password=your_password" | \
gcloud secrets create postgresdb-connection --data-file=-

# SFTP password
echo "your_sftp_password" | \
gcloud secrets create sftp-password --data-file=-
```

## 🔍 Verification

### Check Deployment Status

```bash
# View Cloud Run job
gcloud run jobs describe centerdata-sftp-processor --region=us-central1

# View Artifact Registry images
gcloud artifacts docker images list \
    us-central1-docker.pkg.dev/mapp-dev-457512/sftp-processors

# Test execution
gcloud run jobs execute centerdata-sftp-processor --region=us-central1
```

### View Logs

```bash
# View job execution logs
gcloud logging read \
    "resource.type=cloud_run_job AND resource.labels.job_name=centerdata-sftp-processor" \
    --limit=50 \
    --format="table(timestamp,textPayload)"
```

## 🔄 Updates and Rollbacks

### Update to Latest

```bash
# Simply re-run the deployment script
./deploy-cloud-run.sh
```

### Rollback to Previous Version

```bash
# List available image tags
gcloud artifacts docker images list \
    us-central1-docker.pkg.dev/mapp-dev-457512/sftp-processors/centerdata-sftp-processor

# Update job to specific version
gcloud run jobs replace-image centerdata-sftp-processor \
    --image=us-central1-docker.pkg.dev/mapp-dev-457512/sftp-processors/centerdata-sftp-processor:20241215-143022 \
    --region=us-central1
```

## 🛠️ Troubleshooting

### Common Issues

**Issue: Permission Denied**
```bash
# Ensure you have the required roles
gcloud projects add-iam-policy-binding mapp-dev-457512 \
    --member="user:<EMAIL>" \
    --role="roles/artifactregistry.admin"
```

**Issue: Repository Not Found**
```bash
# Verify repository exists
gcloud artifacts repositories list --location=us-central1

# Create if missing
gcloud artifacts repositories create sftp-processors \
    --repository-format=docker \
    --location=us-central1
```

**Issue: Docker Push Failed**
```bash
# Re-configure Docker authentication
gcloud auth configure-docker us-central1-docker.pkg.dev
```

## 📊 Monitoring

### Cloud Console URLs

- **Artifact Registry**: https://console.cloud.google.com/artifacts/docker/mapp-dev-457512/us-central1/sftp-processors
- **Cloud Run Jobs**: https://console.cloud.google.com/run/jobs?project=mapp-dev-457512
- **Logs**: https://console.cloud.google.com/logs/query?project=mapp-dev-457512

### Metrics and Alerts

Set up monitoring for:
- Job execution success/failure rates
- Execution duration
- Image pull latency
- Storage usage in Artifact Registry

## 🎯 Next Steps

1. **Set up Cloud Scheduler** for automated daily runs
2. **Configure monitoring and alerting**
3. **Implement CI/CD pipeline** with Azure DevOps or GitHub Actions
4. **Set up backup and disaster recovery** procedures

## 📞 Support

For issues with deployment:
1. Check the deployment logs
2. Verify all prerequisites are met
3. Ensure proper IAM permissions
4. Review the troubleshooting section above
