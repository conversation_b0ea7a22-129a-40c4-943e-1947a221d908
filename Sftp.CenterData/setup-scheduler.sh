#!/bin/bash

# =====================================================
# Cloud Scheduler Setup Script for CenterData SFTP Processor
# =====================================================
#
# This script creates a Cloud Scheduler job to run the CenterData SFTP processor
# daily at 6 AM UTC (automatically processes current date files).
#
# Prerequisites:
# 1. gcloud CLI installed and authenticated
# 2. Cloud Run job deployed
# 3. Cloud Scheduler API enabled
# 4. Appropriate IAM permissions
#
# Usage:
#   ./setup-scheduler.sh
#
# =====================================================

set -e  # Exit on any error

# Configuration
PROJECT_ID="mapp-dev-457512"
REGION="us-central1"
JOB_NAME="centerdata-sftp-processor"
SCHEDULER_JOB_NAME="centerdata-daily-import"
SERVICE_ACCOUNT="centerdata-processor@${PROJECT_ID}.iam.gserviceaccount.com"

echo "⏰ Setting up Cloud Scheduler for CenterData SFTP Processor"
echo "Project: ${PROJECT_ID}"
echo "Region: ${REGION}"
echo "Schedule: Daily at 6:00 AM UTC"
echo ""

# Set the project
gcloud config set project ${PROJECT_ID}

# Enable Cloud Scheduler API
echo "🔧 Enabling Cloud Scheduler API..."
gcloud services enable cloudscheduler.googleapis.com

# Grant Cloud Run Invoker role to service account
echo "🔑 Granting Cloud Run Invoker permissions..."
gcloud projects add-iam-policy-binding ${PROJECT_ID} \
    --member="serviceAccount:${SERVICE_ACCOUNT}" \
    --role="roles/run.invoker"

# Create Cloud Scheduler job
echo "⏰ Creating Cloud Scheduler job..."
gcloud scheduler jobs create http ${SCHEDULER_JOB_NAME} \
    --location=${REGION} \
    --schedule='0 6 * * *' \
    --time-zone='UTC' \
    --uri="https://${REGION}-run.googleapis.com/apis/run.googleapis.com/v1/namespaces/${PROJECT_ID}/jobs/${JOB_NAME}:run" \
    --http-method=POST \
    --oauth-service-account-email=${SERVICE_ACCOUNT} \
    --headers='Content-Type=application/json' \
    --message-body='{}' \
    --description="Daily import of CenterData from SFTP server at 6 AM UTC" \
    --max-retry-attempts=3 \
    --max-retry-duration=3600s \
    --min-backoff-duration=60s \
    --max-backoff-duration=300s

echo ""
echo "✅ Cloud Scheduler setup completed!"
echo ""
echo "📋 Scheduler Details:"
echo "  Job Name: ${SCHEDULER_JOB_NAME}"
echo "  Schedule: Daily at 6:00 AM UTC"
echo "  Target: Cloud Run Job '${JOB_NAME}'"
echo "  Max Retries: 3"
echo "  Timeout: 1 hour"
echo ""
echo "🔧 Management commands:"
echo "  List jobs:    gcloud scheduler jobs list --location=${REGION}"
echo "  Run now:      gcloud scheduler jobs run ${SCHEDULER_JOB_NAME} --location=${REGION}"
echo "  Pause:        gcloud scheduler jobs pause ${SCHEDULER_JOB_NAME} --location=${REGION}"
echo "  Resume:       gcloud scheduler jobs resume ${SCHEDULER_JOB_NAME} --location=${REGION}"
echo "  Delete:       gcloud scheduler jobs delete ${SCHEDULER_JOB_NAME} --location=${REGION}"
echo ""
echo "📊 Monitoring:"
echo "  View logs:    gcloud logging read 'resource.type=\"cloud_scheduler_job\" AND resource.labels.job_id=\"${SCHEDULER_JOB_NAME}\"' --limit=50 --format='table(timestamp,severity,textPayload)'"
echo "  Cloud Console: https://console.cloud.google.com/cloudscheduler?project=${PROJECT_ID}"
echo ""
echo "🎯 Scheduler ready! The job will run daily at 6 AM UTC and process current date files automatically."
