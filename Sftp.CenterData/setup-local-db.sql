-- =====================================================
-- Local Development Database Setup
-- =====================================================
-- 
-- This script sets up the required tables in your existing
-- "worldplanning" database for local development.
--
-- Usage:
--   psql -U postgres -d worldplanning -f setup-local-db.sql
--
-- =====================================================

-- Set client encoding and timezone
SET client_encoding = 'UTF8';
SET timezone = 'UTC';

\echo '🚀 Setting up CenterData tables in worldplanning database...'

-- Begin transaction
BEGIN;

-- =====================================================
-- Create shared functions
-- =====================================================

-- Function to update the updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- =====================================================
-- Create center_data_centers table
-- =====================================================

-- Drop existing objects (if they exist)
DROP INDEX IF EXISTS ix_center_data_centers_center_id;
DROP INDEX IF EXISTS ix_center_data_centers_center_name;
DROP INDEX IF EXISTS ix_center_data_centers_state;
DROP INDEX IF EXISTS ix_center_data_centers_regions;
DROP INDEX IF EXISTS ix_center_data_centers_import_batch_id;
DROP INDEX IF EXISTS uk_center_data_centers_center_id;
DROP TABLE IF EXISTS center_data_centers CASCADE;

-- Create center_data_centers table
CREATE TABLE center_data_centers (
    -- Primary key
    id                              SERIAL PRIMARY KEY,
    
    -- Core identification fields
    center_id                       BIGINT NOT NULL,
    center_name                     TEXT,
    
    -- Operational information
    timezone_name                   TEXT,
    center_live_date                TIMESTAMP,
    regions                         TEXT,
    state                           DOUBLE PRECISION,
    
    -- Audit fields
    edited_by                       TEXT,
    created_by                      DOUBLE PRECISION,
    edited_date                     TEXT,
    created_date                    DOUBLE PRECISION,
    
    -- Business logic fields
    center_type_enum                BIGINT,
    center_type_enum_placeholder    BIGINT,
    
    -- Contact information
    center_email                    TEXT,
    center_director_name            TEXT,
    center_director_email           TEXT,
    dvp_name                        TEXT,
    dvp_email                       TEXT,
    regional_manager_name           TEXT,
    regional_manager_email          TEXT,
    
    -- Feature flags
    is_using_assessment             BOOLEAN NOT NULL DEFAULT FALSE,
    is_using_planning               BOOLEAN NOT NULL DEFAULT FALSE,
    
    -- Import tracking
    import_batch_id                 TEXT,
    
    -- Timestamps
    created_at                      TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at                      TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes
CREATE UNIQUE INDEX uk_center_data_centers_center_id ON center_data_centers (center_id);
CREATE INDEX ix_center_data_centers_center_name ON center_data_centers (center_name);
CREATE INDEX ix_center_data_centers_state ON center_data_centers (state);
CREATE INDEX ix_center_data_centers_regions ON center_data_centers (regions);
CREATE INDEX ix_center_data_centers_import_batch_id ON center_data_centers (import_batch_id);

-- Add constraints
ALTER TABLE center_data_centers ADD CONSTRAINT chk_center_data_centers_center_id_positive CHECK (center_id > 0);

-- Create trigger for automatic updated_at timestamp
CREATE TRIGGER tr_center_data_centers_updated_at
    BEFORE UPDATE ON center_data_centers
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- =====================================================
-- Create import_logs table
-- =====================================================

-- Drop existing objects (if they exist)
DROP INDEX IF EXISTS ix_import_logs_batch_id;
DROP INDEX IF EXISTS ix_import_logs_start_time;
DROP INDEX IF EXISTS ix_import_logs_status;
DROP INDEX IF EXISTS ix_import_logs_processor_type;
DROP VIEW IF EXISTS v_recent_imports;
DROP TABLE IF EXISTS import_logs CASCADE;

-- Create import_logs table
CREATE TABLE import_logs (
    id                      SERIAL PRIMARY KEY,
    batch_id                VARCHAR(100) NOT NULL,
    file_name               VARCHAR(500),
    file_path               VARCHAR(1000),
    processor_type          VARCHAR(100),
    start_time              TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    end_time                TIMESTAMP,
    file_size               BIGINT,
    records_processed       INTEGER DEFAULT 0,
    records_inserted        INTEGER DEFAULT 0,
    records_updated         INTEGER DEFAULT 0,
    records_deleted         INTEGER DEFAULT 0,
    records_failed          INTEGER DEFAULT 0,
    status                  VARCHAR(50) NOT NULL DEFAULT 'Processing',
    error_message           TEXT,
    created_at              TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at              TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes
CREATE INDEX ix_import_logs_batch_id ON import_logs (batch_id);
CREATE INDEX ix_import_logs_start_time ON import_logs (start_time DESC);
CREATE INDEX ix_import_logs_status ON import_logs (status);
CREATE INDEX ix_import_logs_processor_type ON import_logs (processor_type);

-- Add constraints
ALTER TABLE import_logs ADD CONSTRAINT chk_import_logs_status_valid CHECK (status IN ('Processing', 'Completed', 'Failed'));

-- Create trigger
CREATE TRIGGER tr_import_logs_updated_at
    BEFORE UPDATE ON import_logs
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- =====================================================
-- Create monitoring view
-- =====================================================

CREATE OR REPLACE VIEW v_recent_imports AS
SELECT 
    id, batch_id, file_name, processor_type, start_time, end_time,
    CASE WHEN end_time IS NOT NULL THEN EXTRACT(EPOCH FROM (end_time - start_time))::INTEGER ELSE NULL END as duration_seconds,
    records_processed, records_inserted, records_updated, records_deleted, records_failed, status,
    CASE 
        WHEN records_failed = 0 AND status = 'Completed' THEN 'Success'
        WHEN records_failed > 0 AND status = 'Completed' THEN 'Partial Success'
        WHEN status = 'Failed' THEN 'Failed'
        ELSE 'In Progress'
    END as result_summary
FROM import_logs
ORDER BY start_time DESC;

-- Commit transaction
COMMIT;

-- =====================================================
-- Verification
-- =====================================================

\echo '✅ Tables created successfully!'
\echo ''
\echo 'Verification:'

-- Check tables
SELECT 
    'Tables created' as status,
    COUNT(*) as table_count
FROM information_schema.tables 
WHERE table_schema = 'public' 
AND table_name IN ('center_data_centers', 'import_logs');

-- Check indexes
SELECT 
    'Indexes created' as status,
    COUNT(*) as index_count
FROM pg_indexes 
WHERE schemaname = 'public' 
AND tablename IN ('center_data_centers', 'import_logs');

\echo ''
\echo '🎯 Ready for local testing!'
\echo ''
\echo 'Next steps:'
\echo '1. Test connection: dotnet run --help'
\echo '2. Process files: dotnet run'
\echo '3. Monitor: SELECT * FROM v_recent_imports;'
\echo ''
