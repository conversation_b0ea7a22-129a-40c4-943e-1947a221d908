using Microsoft.Extensions.Logging;
using Sftp.CenterData.Models;
using Sftp.Core.Abstractions;
using Sftp.Core.Configuration;
using Sftp.Core.Models;
using Sftp.Core.Services;

namespace Sftp.CenterData.Services;

public class CenterDataOrchestrationService : IImportOrchestrationService<Center>
{
    private readonly ILogger<CenterDataOrchestrationService> _logger;
    private readonly IConfigurationService _configService;
    private readonly ISftpService _sftpService;
    private readonly IFileProcessor<Center> _fileProcessor;
    private readonly IDataImportService<Center> _dataImportService;
    private readonly AppConfiguration _appConfig;

    public CenterDataOrchestrationService(
        ILogger<CenterDataOrchestrationService> logger,
        IConfigurationService configService,
        ISftpService sftpService,
        IFileProcessor<Center> fileProcessor,
        IDataImportService<Center> dataImportService,
        AppConfiguration appConfig)
    {
        _logger = logger;
        _configService = configService;
        _sftpService = sftpService;
        _fileProcessor = fileProcessor;
        _dataImportService = dataImportService;
        _appConfig = appConfig;
    }

    public async Task<bool> ExecuteImportAsync(string remoteFilePath, CancellationToken cancellationToken = default)
    {
        var batchId = Guid.NewGuid().ToString();
        var fileName = Path.GetFileName(remoteFilePath);
        
        _logger.LogInformation("Starting CenterData import process for file: {RemoteFilePath} with batch ID: {BatchId}",
            remoteFilePath, batchId);

        // Create import log
        var importLog = new ImportLog
        {
            BatchId = batchId,
            FileName = fileName,
            FilePath = remoteFilePath,
            StartTime = DateTime.UtcNow,
            Status = "Processing",
            ProcessorType = _fileProcessor.ProcessorType
        };

        try
        {
            importLog = await _dataImportService.CreateImportLogAsync(importLog, cancellationToken);

            // Step 1: Download file from SFTP
            var localFilePath = await DownloadFileFromSftpAsync(remoteFilePath, cancellationToken);
            if (localFilePath == null)
            {
                await UpdateImportLogWithError(importLog, "Failed to download file from SFTP", cancellationToken);
                return false;
            }

            // Step 2: Process the downloaded file
            var success = await ProcessFileAndImportAsync(localFilePath, batchId, importLog, cancellationToken);

            // Step 3: Cleanup
            await CleanupTempFileAsync(localFilePath);

            return success;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "CenterData import process failed for file: {RemoteFilePath}", remoteFilePath);
            await UpdateImportLogWithError(importLog, $"Import process failed: {ex.Message}", cancellationToken);
            return false;
        }
    }

    public async Task<bool> ProcessLocalFileAsync(string localFilePath, CancellationToken cancellationToken = default)
    {
        var batchId = Guid.NewGuid().ToString();
        var fileName = Path.GetFileName(localFilePath);

        _logger.LogInformation("Starting CenterData local file processing for: {LocalFilePath} with batch ID: {BatchId}",
            localFilePath, batchId);

        // Create import log
        var importLog = new ImportLog
        {
            BatchId = batchId,
            FileName = fileName,
            FilePath = localFilePath,
            StartTime = DateTime.UtcNow,
            Status = "Processing",
            ProcessorType = _fileProcessor.ProcessorType
        };

        try
        {
            importLog = await _dataImportService.CreateImportLogAsync(importLog, cancellationToken);

            // Get file size
            if (File.Exists(localFilePath))
            {
                var fileInfo = new FileInfo(localFilePath);
                importLog.FileSize = fileInfo.Length;
                await _dataImportService.UpdateImportLogAsync(importLog, cancellationToken);
            }

            return await ProcessFileAndImportAsync(localFilePath, batchId, importLog, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "CenterData local file processing failed for: {LocalFilePath}", localFilePath);
            await UpdateImportLogWithError(importLog, $"Local file processing failed: {ex.Message}", cancellationToken);
            return false;
        }
    }

    public async Task<int> ProcessMatchingFilesAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            var sftpConfig = await _configService.GetSftpConfigurationAsync(cancellationToken);
            var remoteDirectory = _appConfig.Processing.RemoteDirectory;

            _logger.LogInformation("Processing CenterData files from remote directory: {RemoteDirectory}", remoteDirectory);

            var files = await _sftpService.ListFilesAsync(sftpConfig, remoteDirectory, cancellationToken);
            var matchingFiles = files.Where(f => _fileProcessor.CanProcess(f)).ToList();

            _logger.LogInformation("Found {FileCount} CenterData files to process", matchingFiles.Count);

            // Filter files based on environment and date logic
            var filesToProcess = FilterFilesForProcessing(matchingFiles);

            _logger.LogInformation("After filtering, {FilteredFileCount} files selected for processing", filesToProcess.Count);

            var successCount = 0;
            foreach (var file in filesToProcess)
            {
                cancellationToken.ThrowIfCancellationRequested();

                var remoteFilePath = Path.Combine(remoteDirectory, file).Replace('\\', '/');
                _logger.LogInformation("Processing CenterData file: {RemoteFilePath}", remoteFilePath);

                var success = await ExecuteImportAsync(remoteFilePath, cancellationToken);

                if (success)
                {
                    successCount++;
                    _logger.LogInformation("Successfully processed CenterData file: {RemoteFilePath}", remoteFilePath);
                }
                else
                {
                    _logger.LogError("Failed to process CenterData file: {RemoteFilePath}", remoteFilePath);
                }
            }

            _logger.LogInformation("CenterData batch processing completed. {SuccessCount}/{TotalCount} files processed successfully",
                successCount, filesToProcess.Count);

            return successCount;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing CenterData matching files");
            return 0;
        }
    }

    /// <summary>
    /// Filters files for processing based on environment and configuration settings.
    /// When ProcessCurrentDateOnly is enabled, selects only files with current date.
    /// Otherwise, processes all matching files.
    /// </summary>
    /// <param name="matchingFiles">List of files that match the processor pattern</param>
    /// <returns>Filtered list of files to process</returns>
    private List<string> FilterFilesForProcessing(List<string> matchingFiles)
    {
        // Check configuration setting for current date filtering
        var processCurrentDateOnly = _appConfig.Processing.ProcessCurrentDateOnly;
        var isCloudRun = IsRunningInCloudRun();

        // Apply current date filtering if configured or if running in cloud environment
        var shouldFilterByDate = processCurrentDateOnly || isCloudRun;

        if (!shouldFilterByDate)
        {
            _logger.LogInformation("Date filtering disabled - processing all {FileCount} matching files", matchingFiles.Count);
            return matchingFiles;
        }

        _logger.LogInformation("Date filtering enabled (ProcessCurrentDateOnly: {ProcessCurrentDateOnly}, IsCloudRun: {IsCloudRun}) - filtering for current date file",
            processCurrentDateOnly, isCloudRun);

        // Get current date in the expected format (YYYYMMDD)
        var currentDate = DateTime.UtcNow.ToString("yyyyMMdd");
        _logger.LogInformation("Looking for file with current date: {CurrentDate}", currentDate);

        // Filter files that contain the current date
        var currentDateFiles = matchingFiles
            .Where(file => ExtractDateFromFileName(file) == currentDate)
            .ToList();

        if (currentDateFiles.Count == 0)
        {
            _logger.LogWarning("No files found for current date {CurrentDate}. Available files: {AvailableFiles}",
                currentDate, string.Join(", ", matchingFiles));

            // Log available dates for troubleshooting
            var availableDates = matchingFiles
                .Select(file => ExtractDateFromFileName(file))
                .Where(date => !string.IsNullOrEmpty(date))
                .Distinct()
                .OrderBy(date => date)
                .ToList();

            _logger.LogInformation("Available dates in files: {AvailableDates}", string.Join(", ", availableDates));

            return new List<string>(); // Return empty list if no current date file found
        }

        if (currentDateFiles.Count > 1)
        {
            _logger.LogWarning("Multiple files found for current date {CurrentDate}: {Files}. Processing all of them.",
                currentDate, string.Join(", ", currentDateFiles));
        }
        else
        {
            _logger.LogInformation("Found file for current date {CurrentDate}: {FileName}",
                currentDate, currentDateFiles.First());
        }

        return currentDateFiles;
    }

    /// <summary>
    /// Determines if the application is running in Google Cloud Run environment.
    /// </summary>
    /// <returns>True if running in Cloud Run, false otherwise</returns>
    private bool IsRunningInCloudRun()
    {
        // Check for Cloud Run specific environment variables
        var cloudRunService = Environment.GetEnvironmentVariable("K_SERVICE");
        var cloudRunRevision = Environment.GetEnvironmentVariable("K_REVISION");
        var cloudRunConfiguration = Environment.GetEnvironmentVariable("K_CONFIGURATION");

        // Check for Google Cloud metadata server (available in all Google Cloud environments)
        var googleCloudProject = Environment.GetEnvironmentVariable("GOOGLE_CLOUD_PROJECT");
        var gcloudProject = Environment.GetEnvironmentVariable("GCLOUD_PROJECT");

        // Check application configuration
        var isCloudEnvironment = _appConfig.IsCloudEnvironment;

        var isCloudRun = !string.IsNullOrEmpty(cloudRunService) ||
                        !string.IsNullOrEmpty(cloudRunRevision) ||
                        !string.IsNullOrEmpty(cloudRunConfiguration) ||
                        !string.IsNullOrEmpty(googleCloudProject) ||
                        !string.IsNullOrEmpty(gcloudProject) ||
                        isCloudEnvironment;

        _logger.LogDebug("Cloud Run detection - K_SERVICE: {KService}, IsCloudEnvironment: {IsCloudEnvironment}, Result: {IsCloudRun}",
            cloudRunService ?? "null", isCloudEnvironment, isCloudRun);

        return isCloudRun;
    }

    /// <summary>
    /// Extracts the date portion from a CenterData file name.
    /// Expected format: CenterData_Tadpoles_Center_YYYYMMDD.txt or BrightStar_Tadpoles_Center_YYYYMMDD.txt
    /// </summary>
    /// <param name="fileName">The file name to extract date from</param>
    /// <returns>Date string in YYYYMMDD format, or empty string if not found</returns>
    private string ExtractDateFromFileName(string fileName)
    {
        try
        {
            // Remove file extension
            var nameWithoutExtension = Path.GetFileNameWithoutExtension(fileName);

            // Split by underscore and look for date pattern (8 digits)
            var parts = nameWithoutExtension.Split('_');

            foreach (var part in parts)
            {
                // Check if part is exactly 8 digits (YYYYMMDD format)
                if (part.Length == 8 && part.All(char.IsDigit))
                {
                    // Validate it's a reasonable date (year between 2020-2030)
                    if (part.StartsWith("202") && int.TryParse(part.Substring(0, 4), out var year) && year >= 2020 && year <= 2030)
                    {
                        _logger.LogDebug("Extracted date {Date} from file name {FileName}", part, fileName);
                        return part;
                    }
                }
            }

            _logger.LogDebug("No valid date found in file name {FileName}", fileName);
            return string.Empty;
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error extracting date from file name {FileName}", fileName);
            return string.Empty;
        }
    }

    private async Task<string?> DownloadFileFromSftpAsync(string remoteFilePath, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogDebug("Downloading Center file from SFTP: {RemoteFilePath}", remoteFilePath);

            var sftpConfig = await _configService.GetSftpConfigurationAsync(cancellationToken);
            var fileName = Path.GetFileName(remoteFilePath);
            var localFilePath = Path.Combine(_appConfig.Processing.TempDirectory, $"{Guid.NewGuid()}_{fileName}");

            var success = await _sftpService.DownloadFileAsync(sftpConfig, remoteFilePath, localFilePath, cancellationToken);

            if (success)
            {
                _logger.LogDebug("Successfully downloaded Center file to: {LocalFilePath}", localFilePath);
                return localFilePath;
            }

            _logger.LogWarning("Failed to download Center file from SFTP: {RemoteFilePath}", remoteFilePath);
            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error downloading Center file from SFTP: {RemoteFilePath}", remoteFilePath);
            return null;
        }
    }

    private async Task<bool> ProcessFileAndImportAsync(string localFilePath, string batchId, ImportLog importLog, CancellationToken cancellationToken)
    {
        try
        {
            // Validate file format
            var isValid = await _fileProcessor.ValidateFileAsync(localFilePath, cancellationToken);
            if (!isValid)
            {
                await UpdateImportLogWithError(importLog, "Center file validation failed", cancellationToken);
                return false;
            }

            // Get file size if not already set
            if (importLog.FileSize == null)
            {
                var fileInfo = new FileInfo(localFilePath);
                importLog.FileSize = fileInfo.Length;
            }

            // Process file and parse records
            var records = await _fileProcessor.ProcessFileAsync(localFilePath, batchId, cancellationToken);
            var recordsList = records.ToList();

            importLog.RecordsProcessed = recordsList.Count;
            await _dataImportService.UpdateImportLogAsync(importLog, cancellationToken);

            if (recordsList.Count == 0)
            {
                await UpdateImportLogWithError(importLog, "No valid Center records found in file", cancellationToken);
                return false;
            }

            // Import records to database
            var importResult = await _dataImportService.ImportRecordsAsync(recordsList, batchId, _fileProcessor.ProcessorType, cancellationToken);

            // Update import log with results
            importLog.RecordsInserted = importResult.RecordsInserted;
            importLog.RecordsUpdated = importResult.RecordsUpdated;
            importLog.RecordsFailed = importResult.RecordsFailed;
            importLog.EndTime = DateTime.UtcNow;
            importLog.Status = importResult.IsSuccess ? "Completed" : "Failed";

            if (!importResult.IsSuccess && importResult.Errors.Count > 0)
            {
                importLog.ErrorMessage = string.Join("; ", importResult.Errors);
            }

            await _dataImportService.UpdateImportLogAsync(importLog, cancellationToken);

            _logger.LogInformation("Center file processing completed. Batch ID: {BatchId}, Status: {Status}, " +
                "Inserted: {Inserted}, Updated: {Updated}, Failed: {Failed}",
                batchId, importLog.Status, importLog.RecordsInserted, importLog.RecordsUpdated, importLog.RecordsFailed);

            return importResult.IsSuccess;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing Center file: {LocalFilePath}", localFilePath);
            await UpdateImportLogWithError(importLog, $"Center file processing error: {ex.Message}", cancellationToken);
            return false;
        }
    }

    private async Task UpdateImportLogWithError(ImportLog importLog, string errorMessage, CancellationToken cancellationToken)
    {
        try
        {
            importLog.Status = "Failed";
            importLog.ErrorMessage = errorMessage;
            importLog.EndTime = DateTime.UtcNow;
            await _dataImportService.UpdateImportLogAsync(importLog, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to update import log with error message: {ErrorMessage}", errorMessage);
        }
    }

    private async Task CleanupTempFileAsync(string filePath)
    {
        try
        {
            if (_appConfig.Processing.DeleteTempFilesAfterProcessing && File.Exists(filePath))
            {
                await Task.Run(() => File.Delete(filePath));
                _logger.LogDebug("Deleted temporary Center file: {FilePath}", filePath);
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to delete temporary Center file: {FilePath}", filePath);
        }
    }
}
