using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Sftp.CenterData.Data;
using Sftp.CenterData.Models;
using Sftp.Core.Abstractions;
using Sftp.Core.Configuration;
using Sftp.Core.Models;
using Sftp.Core.Services;

namespace Sftp.CenterData.Services;

/// <summary>
/// Data import service implementation for CenterData childcare center data.
/// Provides database persistence operations with upsert capabilities and comprehensive error handling.
/// </summary>
/// <remarks>
/// This service handles the database persistence layer for CenterData center data including:
///
/// **Import Operations:**
/// - Bulk import processing with configurable batch sizes
/// - Upsert operations (insert new records, update existing ones)
/// - Transaction management for data consistency
/// - Comprehensive error handling and recovery
///
/// **Data Management:**
/// - Duplicate detection based on SchoolId
/// - Selective field updates preserving audit information
/// - Batch tracking for data lineage and troubleshooting
/// - Import logging and statistics tracking
///
/// **Performance Features:**
/// - Configurable batch processing for optimal performance
/// - Database transaction management for consistency
/// - Efficient existence checking using Entity Framework
/// - Memory-efficient processing of large datasets
///
/// **Error Handling:**
/// - Individual record error isolation
/// - Transaction rollback on batch failures
/// - Comprehensive error logging and reporting
/// - Graceful degradation for partial failures
///
/// The service uses Entity Framework Core for database operations and provides
/// detailed logging and metrics for monitoring import operations.
/// </remarks>
public class CenterImportService : IDataImportService<Center>
{
    private readonly ILogger<CenterImportService> _logger;
    private readonly CenterDataDbContext _context;
    private readonly AppConfiguration _config;

    /// <summary>
    /// Initializes a new instance of the CenterImportService class.
    /// </summary>
    /// <param name="logger">Logger instance for structured logging of import operations</param>
    /// <param name="context">Database context for CenterData data operations</param>
    /// <param name="config">Application configuration containing processing parameters</param>
    public CenterImportService(
        ILogger<CenterImportService> logger,
        CenterDataDbContext context,
        AppConfiguration config)
    {
        _logger = logger;
        _context = context;
        _config = config;
    }

    public async Task<ImportResult> ImportRecordsAsync(IEnumerable<Center> records, string batchId, string processorType, CancellationToken cancellationToken = default)
    {
        var result = new ImportResult();
        var recordsList = records.ToList();
        result.RecordsProcessed = recordsList.Count;

        _logger.LogInformation("Starting import of {RecordCount} CenterData records with batch ID: {BatchId}", recordsList.Count, batchId);

        try
        {
            // Step 1: Process upsert operations in batches
            var batchSize = _config.Processing.BatchSize;
            var batches = recordsList.Chunk(batchSize);

            foreach (var batch in batches)
            {
                cancellationToken.ThrowIfCancellationRequested();

                var batchResult = await ProcessBatchAsync(batch, batchId, cancellationToken);

                result.RecordsInserted += batchResult.RecordsInserted;
                result.RecordsUpdated += batchResult.RecordsUpdated;
                result.RecordsFailed += batchResult.RecordsFailed;
                result.Errors.AddRange(batchResult.Errors);

                _logger.LogDebug("Processed batch: {Inserted} inserted, {Updated} updated, {Failed} failed",
                    batchResult.RecordsInserted, batchResult.RecordsUpdated, batchResult.RecordsFailed);
            }

            // Step 2: Handle deletions - remove records that are no longer in the source
            var deletionResult = await ProcessDeletionsAsync(recordsList, batchId, cancellationToken);
            result.RecordsDeleted = deletionResult.RecordsDeleted;
            result.Errors.AddRange(deletionResult.Errors);

            _logger.LogInformation("CenterData import completed. Total: {Inserted} inserted, {Updated} updated, {Deleted} deleted, {Failed} failed",
                result.RecordsInserted, result.RecordsUpdated, result.RecordsDeleted, result.RecordsFailed);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to import CenterData records for batch ID: {BatchId}", batchId);
            result.Errors.Add($"Import failed: {ex.Message}");
            result.RecordsFailed = result.RecordsProcessed - result.RecordsInserted - result.RecordsUpdated;
            return result;
        }
    }

    public async Task<ImportLog> CreateImportLogAsync(ImportLog importLog, CancellationToken cancellationToken = default)
    {
        _logger.LogDebug("Creating import log for batch ID: {BatchId}", importLog.BatchId);

        _context.ImportLogs.Add(importLog);
        await _context.SaveChangesAsync(cancellationToken);

        _logger.LogDebug("Created import log with ID: {ImportLogId}", importLog.Id);
        return importLog;
    }

    public async Task<ImportLog> UpdateImportLogAsync(ImportLog importLog, CancellationToken cancellationToken = default)
    {
        _logger.LogDebug("Updating import log with ID: {ImportLogId}", importLog.Id);

        _context.ImportLogs.Update(importLog);
        await _context.SaveChangesAsync(cancellationToken);

        _logger.LogDebug("Updated import log with ID: {ImportLogId}", importLog.Id);
        return importLog;
    }

    /// <summary>
    /// Processes a batch of Center records with transaction management and error handling.
    /// </summary>
    /// <param name="batch">Collection of Center records to process</param>
    /// <param name="batchId">Unique identifier for the import batch</param>
    /// <param name="cancellationToken">Cancellation token for the operation</param>
    /// <returns>ImportResult containing processing statistics and any errors</returns>
    /// <remarks>
    /// This method provides transactional batch processing with:
    /// - Database transaction management for consistency
    /// - Individual record error isolation
    /// - Upsert operations based on SchoolId
    /// - Comprehensive error logging and reporting
    /// - Automatic rollback on batch failures
    ///
    /// The method uses Entity Framework's change tracking to efficiently
    /// detect and update existing records while inserting new ones.
    /// </remarks>
    private async Task<ImportResult> ProcessBatchAsync(IEnumerable<Center> batch, string batchId, CancellationToken cancellationToken)
    {
        var result = new ImportResult();
        var batchList = batch.ToList();

        using var transaction = await _context.Database.BeginTransactionAsync(cancellationToken);

        try
        {
            foreach (var record in batchList)
            {
                cancellationToken.ThrowIfCancellationRequested();

                try
                {
                    // Check if record already exists based on CenterId
                    var existingRecord = await _context.Centers
                        .FirstOrDefaultAsync(x => x.CenterId == record.CenterId, cancellationToken);

                    if (existingRecord != null)
                    {
                        // Update existing record
                        UpdateExistingRecord(existingRecord, record);
                        result.RecordsUpdated++;
                    }
                    else
                    {
                        // Insert new record
                        _context.Centers.Add(record);
                        result.RecordsInserted++;
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Failed to process CenterData record with CenterId: {CenterId}", record.CenterId);
                    result.RecordsFailed++;
                    result.Errors.Add($"Failed to process CenterId {record.CenterId}: {ex.Message}");
                }
            }

            await _context.SaveChangesAsync(cancellationToken);
            await transaction.CommitAsync(cancellationToken);

            return result;
        }
        catch (Exception ex)
        {
            await transaction.RollbackAsync(cancellationToken);
            _logger.LogError(ex, "Failed to process CenterData batch for batch ID: {BatchId}", batchId);

            result.RecordsFailed = batchList.Count;
            result.RecordsInserted = 0;
            result.RecordsUpdated = 0;
            result.Errors.Add($"Batch processing failed: {ex.Message}");

            return result;
        }
    }

    /// <summary>
    /// Updates an existing Center record with new data while preserving audit information.
    /// </summary>
    /// <param name="existing">The existing record to update</param>
    /// <param name="newRecord">The new record containing updated data</param>
    /// <remarks>
    /// This method performs selective field updates that:
    /// - Updates all business data fields with new values
    /// - Preserves the original Id and CreatedAt timestamp
    /// - Updates the UpdatedAt timestamp to current time
    /// - Updates the ImportBatchId for data lineage tracking
    ///
    /// The method ensures that audit trail information is maintained
    /// while allowing all business data to be refreshed from the source.
    /// </remarks>
    private static void UpdateExistingRecord(Center existing, Center newRecord)
    {
        // Update all fields except Id and CenterId (which should not change)
        existing.CenterName = newRecord.CenterName;
        existing.TimezoneName = newRecord.TimezoneName;
        existing.CenterLiveDate = newRecord.CenterLiveDate;
        existing.Regions = newRecord.Regions;
        existing.State = newRecord.State;
        existing.CenterEmail = newRecord.CenterEmail;
        existing.CenterDirectorName = newRecord.CenterDirectorName;
        existing.CenterDirectorEmail = newRecord.CenterDirectorEmail;
        existing.DvpName = newRecord.DvpName;
        existing.DvpEmail = newRecord.DvpEmail;
        existing.RegionalManagerName = newRecord.RegionalManagerName;
        existing.RegionalManagerEmail = newRecord.RegionalManagerEmail;
        existing.IsUsingAssessment = newRecord.IsUsingAssessment;
        existing.IsUsingPlanning = newRecord.IsUsingPlanning;

        // Update audit fields
        existing.EditedBy = newRecord.EditedBy;
        existing.EditedDate = DateTime.UtcNow.ToString("yyyy-MM-dd HH:mm:ss");
        existing.CenterTypeEnum = newRecord.CenterTypeEnum;
        existing.CenterTypeEnumPlaceholder = newRecord.CenterTypeEnumPlaceholder;

        // Update import tracking
        existing.ImportBatchId = newRecord.ImportBatchId;
    }

    /// <summary>
    /// Processes deletions by removing records that are no longer present in the source data.
    /// </summary>
    /// <param name="sourceRecords">The records from the current import</param>
    /// <param name="batchId">The current import batch ID</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Result containing deletion statistics</returns>
    private async Task<ImportResult> ProcessDeletionsAsync(IList<Center> sourceRecords, string batchId, CancellationToken cancellationToken)
    {
        var result = new ImportResult();

        try
        {
            // Get all center IDs from the source data
            var sourceCenterIds = sourceRecords.Select(r => r.CenterId).ToHashSet();

            // Find existing records that are not in the source data
            var recordsToDelete = await _context.Centers
                .Where(c => !sourceCenterIds.Contains(c.CenterId))
                .ToListAsync(cancellationToken);

            if (recordsToDelete.Count > 0)
            {
                _logger.LogInformation("Found {DeleteCount} records to delete (not present in source data)", recordsToDelete.Count);

                _context.Centers.RemoveRange(recordsToDelete);
                await _context.SaveChangesAsync(cancellationToken);

                result.RecordsDeleted = recordsToDelete.Count;

                _logger.LogInformation("Successfully deleted {DeleteCount} records", recordsToDelete.Count);
            }
            else
            {
                _logger.LogDebug("No records to delete - all existing records are present in source data");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to process deletions for batch ID: {BatchId}", batchId);
            result.Errors.Add($"Deletion processing failed: {ex.Message}");
        }

        return result;
    }
}
