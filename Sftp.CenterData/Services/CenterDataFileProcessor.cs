using Microsoft.Extensions.Logging;
using Sftp.CenterData.Models;
using Sftp.Core.Abstractions;

namespace Sftp.CenterData.Services;

/// <summary>
/// File processor implementation for CenterData Tadpoles Center data files.
/// Handles parsing of pipe-delimited text files containing comprehensive childcare center information.
/// </summary>
/// <remarks>
/// This processor is specifically designed for CenterData Tadpoles Center data files with the following characteristics:
///
/// **File Format:**
/// - Pipe-delimited (|) text files
/// - Header row with predefined column names
/// - Fixed column structure with 35 expected fields
/// - UTF-8 text encoding
///
/// **Data Processing:**
/// - Comprehensive center information including locations and contacts
/// - Staff and management contact details
/// - Emergency evacuation location information
/// - Operational details and scheduling information
///
/// **Validation Features:**
/// - Strict header validation against expected column structure
/// - Field count validation for each data row
/// - Data type validation and conversion
/// - Graceful error handling for malformed records
///
/// **Supported File Patterns:**
/// - "CenterData_Tadpoles_Center_*.txt" - Standard naming convention
/// - "*CenterData*Tadpoles*Center*.txt" - Flexible pattern matching
///
/// **Error Handling:**
/// - Individual record error isolation (continues processing on single record failures)
/// - Comprehensive logging for troubleshooting and data quality monitoring
/// - Detailed validation error reporting
///
/// The processor creates Center entities with complete center information
/// including emergency procedures and contact hierarchies.
/// </remarks>
public class CenterDataFileProcessor : BaseFileProcessor<Center>
{
    /// <summary>
    /// The delimiter character used to separate fields in the CenterData data files.
    /// </summary>
    private const char Delimiter = '|';

    /// <summary>
    /// The expected header columns for CenterData Tadpoles Center data files.
    /// Defines the exact order and naming of columns that must be present in the file.
    /// </summary>
    /// <remarks>
    /// This array defines the complete structure of CenterData center data files including:
    /// - Basic center identification and operational information
    /// - Complete address and contact details
    /// - Staff and management hierarchy contacts
    /// - Primary and secondary evacuation location details
    /// - Emergency contact information
    ///
    /// The order of headers must match exactly with the data file structure.
    /// </remarks>
    private static readonly string[] ExpectedHeaders = {
        "School_ID",
        "School_Name",
        "Status",
        "Center_Live_Date",
        "Regions",
        "State",
        "School_Number",
        "Center_Primary_Email",
        "Center_Address_Line_1",
        "Center_Address_Line_2",
        "City",
        "Postal_Code",
        "Center_Phone_Number",
        "Center_Model",
        "Timezone",
        "Operational_Hours",
        "Center_Director_Name",
        "Center_Director_Primary_Email",
        "Regional_Manager_Name",
        "Regional_Manager_Email",
        "Divisional_Vice_President_Name",
        "Divisional_Vice_President_Email",
        "Primary_Evacuation_Location",
        "Primary_Evacuation_Location_Address Line_1",
        "Primary_Evacuation_Location_Address_Line_2",
        "Primary_Evacuation_City",
        "Primary_Evacuation_State",
        "Primary_Evacuation_Postal_Code",
        "Center_Emergency_Cell_Phone",
        "Secondary_Evacuation_Location",
        "Secondary_Evacuation_Location_Address_Line_1",
        "Secondary_Evacuation_Location_Address_Line_2",
        "Secondary_Evacuation_City",
        "Secondary_Evacuation_State",
        "Secondary_Evacuation_Postal_Code"
    };

    /// <summary>
    /// Initializes a new instance of the CenterDataFileProcessor class.
    /// </summary>
    /// <param name="logger">Logger instance for structured logging of processing operations</param>
    public CenterDataFileProcessor(ILogger<CenterDataFileProcessor> logger) : base(logger)
    {
    }

    /// <summary>
    /// Gets the processor type identifier for CenterData Tadpoles Center files.
    /// </summary>
    public override string ProcessorType => "CenterData_Tadpoles_Center";

    /// <summary>
    /// Gets the file patterns that this processor can handle.
    /// Supports both standard naming conventions and flexible pattern matching.
    /// </summary>
    public override string[] SupportedFilePatterns => new[] {
        "CenterData_Tadpoles_Center_*.txt",
        "BrightStar_Tadpoles_Center_*.txt",
        "*CenterData*Tadpoles*Center*.txt",
        "*BrightStar*Tadpoles*Center*.txt"
    };

    /// <summary>
    /// Gets the expected header columns for CenterData data files.
    /// </summary>
    /// <returns>Array of expected column headers in the correct order</returns>
    public override string[] GetExpectedHeaders() => ExpectedHeaders;

    /// <summary>
    /// Processes a CenterData Tadpoles Center data file and converts it to Center entities.
    /// </summary>
    /// <param name="filePath">Path to the CenterData data file to process</param>
    /// <param name="batchId">Unique identifier for this import batch</param>
    /// <param name="cancellationToken">Cancellation token for the operation</param>
    /// <returns>Collection of Center entities parsed from the file</returns>
    /// <exception cref="FileNotFoundException">Thrown when the specified file does not exist</exception>
    /// <exception cref="InvalidOperationException">Thrown when file format validation fails</exception>
    /// <remarks>
    /// This method performs the complete file processing workflow:
    /// 1. Validates file existence and accessibility
    /// 2. Reads and validates the header row against expected structure
    /// 3. Processes each data row with individual error handling
    /// 4. Creates Center entities with proper data type conversion
    /// 5. Assigns batch tracking information for data lineage
    ///
    /// The method uses resilient processing that continues even if individual records fail,
    /// logging warnings for problematic records while successfully processing valid ones.
    /// </remarks>
    public override async Task<IEnumerable<Center>> ProcessFileAsync(string filePath, string batchId, CancellationToken cancellationToken = default)
    {
        Logger.LogInformation("Starting to process CenterData file: {FilePath} with batch ID: {BatchId}", filePath, batchId);

        if (!File.Exists(filePath))
        {
            throw new FileNotFoundException($"File not found: {filePath}");
        }

        var records = new List<Center>();
        var lineNumber = 0;

        try
        {
            using var reader = new StreamReader(filePath);
            string? line;

            // Read and validate header
            line = await reader.ReadLineAsync(cancellationToken);
            lineNumber++;

            if (line == null)
            {
                throw new InvalidOperationException("File is empty");
            }

            var headers = line.Split(Delimiter);
            if (!ValidateHeaders(headers))
            {
                throw new InvalidOperationException("File headers do not match expected format");
            }

            Logger.LogDebug("File headers validated successfully");

            // Process data rows
            while ((line = await reader.ReadLineAsync(cancellationToken)) != null)
            {
                lineNumber++;
                cancellationToken.ThrowIfCancellationRequested();

                if (string.IsNullOrWhiteSpace(line))
                {
                    continue;
                }

                try
                {
                    var record = ParseLine(line, batchId, lineNumber);
                    if (record != null)
                    {
                        records.Add(record);
                    }
                }
                catch (Exception ex)
                {
                    Logger.LogWarning(ex, "Failed to parse line {LineNumber}: {Line}", lineNumber, line);
                    // Continue processing other lines
                }
            }

            Logger.LogInformation("Successfully processed {RecordCount} records from CenterData file: {FilePath}", records.Count, filePath);
            return records;
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Failed to process CenterData file: {FilePath} at line {LineNumber}", filePath, lineNumber);
            throw;
        }
    }

    /// <summary>
    /// Validates the content of a CenterData data file by checking the header structure.
    /// </summary>
    /// <param name="filePath">Path to the file being validated</param>
    /// <param name="firstLine">The first line of the file containing headers</param>
    /// <param name="cancellationToken">Cancellation token for the operation</param>
    /// <returns>True if the file content is valid, false otherwise</returns>
    /// <remarks>
    /// This method overrides the base validation to provide CenterData-specific
    /// header validation using the pipe delimiter and expected column structure.
    /// </remarks>
    protected override async Task<bool> ValidateFileContentAsync(string filePath, string firstLine, CancellationToken cancellationToken)
    {
        var headers = firstLine.Split(Delimiter);
        return ValidateHeaders(headers);
    }

    /// <summary>
    /// Validates that the file headers match the expected CenterData data structure.
    /// </summary>
    /// <param name="headers">Array of header strings from the file</param>
    /// <returns>True if headers are valid, false otherwise</returns>
    /// <remarks>
    /// This method performs comprehensive header validation including:
    /// - Column count verification against expected structure
    /// - Case-insensitive header name matching
    /// - Position-specific validation for proper column ordering
    /// - Detailed logging of validation failures for troubleshooting
    /// </remarks>
    private bool ValidateHeaders(string[] headers)
    {
        if (headers.Length != ExpectedHeaders.Length)
        {
            Logger.LogWarning("Header count mismatch. Expected: {Expected}, Actual: {Actual}",
                ExpectedHeaders.Length, headers.Length);
            return false;
        }

        for (int i = 0; i < ExpectedHeaders.Length; i++)
        {
            var expectedHeader = ExpectedHeaders[i];
            var actualHeader = headers[i].Trim();

            if (!string.Equals(expectedHeader, actualHeader, StringComparison.OrdinalIgnoreCase))
            {
                Logger.LogWarning("Header mismatch at position {Position}. Expected: '{Expected}', Actual: '{Actual}'",
                    i, expectedHeader, actualHeader);
                return false;
            }
        }

        return true;
    }

    /// <summary>
    /// Parses a single data line from the CenterData file into a Center entity.
    /// </summary>
    /// <param name="line">The data line to parse</param>
    /// <param name="batchId">The batch identifier for tracking this import operation</param>
    /// <param name="lineNumber">The line number in the file for error reporting</param>
    /// <returns>A Center entity if parsing succeeds, null if parsing fails</returns>
    /// <remarks>
    /// This method performs comprehensive data parsing including:
    /// - Field count validation against expected structure
    /// - Data type conversion with null handling
    /// - Date parsing with multiple format support
    /// - Batch tracking assignment for data lineage
    /// - Timestamp assignment for audit trails
    ///
    /// The method uses resilient parsing that returns null for invalid records
    /// rather than throwing exceptions, allowing batch processing to continue.
    /// </remarks>
    private Center? ParseLine(string line, string batchId, int lineNumber)
    {
        var fields = line.Split(Delimiter);

        if (fields.Length != ExpectedHeaders.Length)
        {
            Logger.LogWarning("Field count mismatch at line {LineNumber}. Expected: {Expected}, Actual: {Actual}", 
                lineNumber, ExpectedHeaders.Length, fields.Length);
            return null;
        }

        try
        {
            var record = new Center
            {
                // Map School_ID to center_id
                CenterId = ParseLong(fields[0]) ?? 0,

                // Map School_Name to center_name
                CenterName = ParseString(fields[1]),

                // Skip Status field (not needed in new model)

                // Map Center_Live_Date to center_live_date
                CenterLiveDate = ParseDateTime(fields[3]),

                // Map Regions to regions
                Regions = ParseString(fields[4]),

                // Map State to state (as double precision)
                State = ParseDouble(fields[5]),

                // Skip School_Number (not needed)

                // Map Center_Primary_Email to CenterEmail
                CenterEmail = ParseString(fields[7]),

                // Skip address fields (not needed in new model)

                // Skip phone, model, operational hours (not needed)

                // Map Timezone to timezone_name
                TimezoneName = ParseString(fields[14]),

                // Map Center_Director_Name to CenterDirectorName
                CenterDirectorName = ParseString(fields[16]),

                // Map Center_Director_Primary_Email to CenterDirectorEmail
                CenterDirectorEmail = ParseString(fields[17]),

                // Map Regional_Manager_Name to RegionalManagerName
                RegionalManagerName = ParseString(fields[18]),

                // Map Regional_Manager_Email to RegionalManagerEmail
                RegionalManagerEmail = ParseString(fields[19]),

                // Map Divisional_Vice_President_Name to DvpName
                DvpName = ParseString(fields[20]),

                // Map Divisional_Vice_President_Email to DvpEmail
                DvpEmail = ParseString(fields[21]),

                // Set default values for fields with no mapping
                IsUsingAssessment = false, // No mapping - default value
                IsUsingPlanning = false,   // No mapping - default value

                // Audit fields - set defaults for now
                EditedBy = null,
                CreatedBy = null,
                EditedDate = null,
                CreatedDate = null,
                CenterTypeEnum = null,
                CenterTypeEnumPlaceholder = null,

                ImportBatchId = batchId
            };

            return record;
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error parsing line {LineNumber}: {Line}", lineNumber, line);
            return null;
        }
    }

    /// <summary>
    /// Parses a string value to a long integer.
    /// </summary>
    /// <param name="value">The string value to parse</param>
    /// <returns>The parsed long value or null if parsing fails</returns>
    private static long? ParseLong(string value)
    {
        if (string.IsNullOrWhiteSpace(value))
            return null;

        return long.TryParse(value.Trim(), out var result) ? result : null;
    }

    /// <summary>
    /// Parses a string value to a double.
    /// </summary>
    /// <param name="value">The string value to parse</param>
    /// <returns>The parsed double value or null if parsing fails</returns>
    private static double? ParseDouble(string value)
    {
        if (string.IsNullOrWhiteSpace(value))
            return null;

        return double.TryParse(value.Trim(), out var result) ? result : null;
    }

    /// <summary>
    /// Parses a string value to a DateTime.
    /// </summary>
    /// <param name="value">The string value to parse</param>
    /// <returns>The parsed DateTime value or null if parsing fails</returns>
    private static DateTime? ParseDateTime(string value)
    {
        if (string.IsNullOrWhiteSpace(value))
            return null;

        return DateTime.TryParse(value.Trim(), out var result) ? result : null;
    }
}
