using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Sftp.CenterData.Models;
using Sftp.Core.Configuration;
using Sftp.Core.Services;

namespace Sftp.CenterData.Services;

public class CenterDataWorkerService : BackgroundService
{
    private readonly ILogger<CenterDataWorkerService> _logger;
    private readonly IServiceProvider _serviceProvider;
    private readonly AppConfiguration _appConfig;

    public CenterDataWorkerService(
        ILogger<CenterDataWorkerService> logger,
        IServiceProvider serviceProvider,
        AppConfiguration appConfig)
    {
        _logger = logger;
        _serviceProvider = serviceProvider;
        _appConfig = appConfig;
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        _logger.LogInformation("CenterData Import Worker Service started");

        try
        {
            using var scope = _serviceProvider.CreateScope();
            var orchestrationService = scope.ServiceProvider.GetRequiredService<IImportOrchestrationService<Center>>();

            // Get command line arguments or environment variables to determine what to process
            var args = Environment.GetCommandLineArgs();

            if (args.Length > 1)
            {
                var filePath = args[1];

                if (File.Exists(filePath))
                {
                    // Process local file
                    _logger.LogInformation("Processing local CenterData file: {FilePath}", filePath);
                    var success = await orchestrationService.ProcessLocalFileAsync(filePath, stoppingToken);

                    if (success)
                    {
                        _logger.LogInformation("Local CenterData file processing completed successfully");
                    }
                    else
                    {
                        _logger.LogError("Local CenterData file processing failed");
                        Environment.ExitCode = 1;
                    }
                }
                else
                {
                    // Assume it's a remote file path
                    _logger.LogInformation("Processing remote CenterData file: {FilePath}", filePath);
                    var success = await orchestrationService.ExecuteImportAsync(filePath, stoppingToken);

                    if (success)
                    {
                        _logger.LogInformation("Remote CenterData file processing completed successfully");
                    }
                    else
                    {
                        _logger.LogError("Remote CenterData file processing failed");
                        Environment.ExitCode = 1;
                    }
                }
            }
            else
            {
                // Default behavior - process matching files from configured remote directory
                _logger.LogInformation("Processing all matching CenterData files from remote directory");
                var processedCount = await orchestrationService.ProcessMatchingFilesAsync(stoppingToken);

                if (processedCount > 0)
                {
                    _logger.LogInformation("Successfully processed {ProcessedCount} CenterData files", processedCount);
                }
                else
                {
                    _logger.LogWarning("No CenterData files were processed successfully");
                    Environment.ExitCode = 1;
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "CenterData Import Worker Service encountered an error");
            Environment.ExitCode = 1;
        }
        finally
        {
            _logger.LogInformation("CenterData Import Worker Service completed");
        }
    }
}
