#!/bin/bash

# =====================================================
# Azure DevOps Cloud Run Deployment Script
# =====================================================
#
# This script is designed to be called from Azure DevOps pipeline
# to deploy the CenterData SFTP processor to Cloud Run.
#
# Environment Variables Required:
# - GCP_PROJECT_ID: GCP project ID
# - GCP_REGION: GCP region for deployment
# - GCP_SERVICE_ACCOUNT: Service account email
# - CLOUD_RUN_JOB_NAME: Name of the Cloud Run job
# - DOCKER_IMAGE_NAME: Full Docker image name with tag
# - BUILD_ID: Build identifier for tagging
#
# Usage (from Azure DevOps):
#   ./deploy-azure-devops.sh
#
# =====================================================

set -e  # Exit on any error

# Default values (can be overridden by environment variables)
GCP_PROJECT_ID=${GCP_PROJECT_ID:-"mapp-dev-457512"}
GCP_REGION=${GCP_REGION:-"us-central1"}
GCP_SERVICE_ACCOUNT=${GCP_SERVICE_ACCOUNT:-"<EMAIL>"}
BUILD_ID=${BUILD_ID:-$(date +%Y%m%d-%H%M%S)}

# CLOUD_RUN_JOB_NAME is required - no default value
if [ -z "$CLOUD_RUN_JOB_NAME" ]; then
    echo "❌ Error: CLOUD_RUN_JOB_NAME environment variable is required"
    echo "Example: export CLOUD_RUN_JOB_NAME=centerdata-sftp-processor"
    exit 1
fi

# Artifact Registry Configuration
REPOSITORY_NAME=${REPOSITORY_NAME:-"sftp-processors"}
DOCKER_IMAGE_NAME=${DOCKER_IMAGE_NAME:-"${GCP_REGION}-docker.pkg.dev/${GCP_PROJECT_ID}/${REPOSITORY_NAME}/${CLOUD_RUN_JOB_NAME}"}

# Secret names (configurable for different SFTP processors)
SECRET_DB_NAME=${SECRET_DB_NAME:-"${CLOUD_RUN_JOB_NAME}-db-connection"}
SECRET_SFTP_NAME=${SECRET_SFTP_NAME:-"${CLOUD_RUN_JOB_NAME}-sftp-password"}

echo "🚀 Starting Azure DevOps Cloud Run deployment"
echo "Project: ${GCP_PROJECT_ID}"
echo "Region: ${GCP_REGION}"
echo "Job Name: ${CLOUD_RUN_JOB_NAME}"
echo "Repository: ${REPOSITORY_NAME}"
echo "Service Account: ${GCP_SERVICE_ACCOUNT}"
echo "Image: ${DOCKER_IMAGE_NAME}:${BUILD_ID}"
echo "DB Secret: ${SECRET_DB_NAME}"
echo "SFTP Secret: ${SECRET_SFTP_NAME}"
echo ""

# Verify required environment variables
if [ -z "$GCP_PROJECT_ID" ] || [ -z "$GCP_REGION" ] || [ -z "$GCP_SERVICE_ACCOUNT" ] || [ -z "$CLOUD_RUN_JOB_NAME" ]; then
    echo "❌ Error: Required environment variables not set"
    echo "Required: GCP_PROJECT_ID, GCP_REGION, GCP_SERVICE_ACCOUNT, CLOUD_RUN_JOB_NAME"
    echo ""
    echo "Example usage:"
    echo "  export CLOUD_RUN_JOB_NAME=centerdata-sftp-processor"
    echo "  export SECRET_DB_NAME=centerdata-db-connection"
    echo "  export SECRET_SFTP_NAME=centerdata-sftp-password"
    echo "  ./deploy-azure-devops.sh"
    exit 1
fi

# Set GCP project
echo "📋 Configuring GCP project..."
gcloud config set project ${GCP_PROJECT_ID}
gcloud config set compute/region ${GCP_REGION}

# Enable required APIs
echo "🔧 Enabling required GCP APIs..."
gcloud services enable cloudbuild.googleapis.com --quiet
gcloud services enable run.googleapis.com --quiet
gcloud services enable secretmanager.googleapis.com --quiet
gcloud services enable artifactregistry.googleapis.com --quiet

# Create Artifact Registry repository if it doesn't exist
echo "🏗️ Setting up Artifact Registry repository..."
if ! gcloud artifacts repositories describe ${REPOSITORY_NAME} --location=${GCP_REGION} >/dev/null 2>&1; then
    echo "Creating Artifact Registry repository: ${REPOSITORY_NAME}"
    gcloud artifacts repositories create ${REPOSITORY_NAME} \
        --repository-format=docker \
        --location=${GCP_REGION} \
        --description="Docker repository for SFTP processors" \
        --quiet
else
    echo "Artifact Registry repository ${REPOSITORY_NAME} already exists"
fi

# Configure Docker for Artifact Registry
echo "🐳 Configuring Docker for Artifact Registry..."
gcloud auth configure-docker ${GCP_REGION}-docker.pkg.dev --quiet

# Build Docker image
echo "🔨 Building Docker image..."
docker build -t ${DOCKER_IMAGE_NAME}:${BUILD_ID} .
docker tag ${DOCKER_IMAGE_NAME}:${BUILD_ID} ${DOCKER_IMAGE_NAME}:latest

# Push Docker image
echo "📤 Pushing Docker image to Artifact Registry..."
docker push ${DOCKER_IMAGE_NAME}:${BUILD_ID}
docker push ${DOCKER_IMAGE_NAME}:latest

# Check if Cloud Run job exists
echo "🔍 Checking if Cloud Run job exists..."
if gcloud run jobs describe ${CLOUD_RUN_JOB_NAME} --region=${GCP_REGION} >/dev/null 2>&1; then
    echo "📝 Updating existing Cloud Run job..."
    
    # Update existing job with new image
    gcloud run jobs replace-image ${CLOUD_RUN_JOB_NAME} \
        --image=${DOCKER_IMAGE_NAME}:${BUILD_ID} \
        --region=${GCP_REGION}
    
    echo "✅ Cloud Run job updated successfully"
else
    echo "🆕 Creating new Cloud Run job..."
    
    # Create new Cloud Run job
    gcloud run jobs create ${CLOUD_RUN_JOB_NAME} \
        --image=${DOCKER_IMAGE_NAME}:${BUILD_ID} \
        --region=${GCP_REGION} \
        --service-account=${GCP_SERVICE_ACCOUNT} \
        --set-env-vars="ASPNETCORE_ENVIRONMENT=Production" \
        --set-secrets="DATABASE_CONNECTION_STRING=${SECRET_DB_NAME}:latest" \
        --set-secrets="SFTP_PASSWORD=${SECRET_SFTP_NAME}:latest" \
        --memory=1Gi \
        --cpu=1 \
        --max-retries=3 \
        --parallelism=1 \
        --task-count=1 \
        --task-timeout=3600 \
        --labels="app=${CLOUD_RUN_JOB_NAME},environment=production,type=sftp-import,deployed-by=azure-devops,build-id=${BUILD_ID}"
    
    echo "✅ Cloud Run job created successfully"
fi

# Test the deployment (optional)
if [ "${SKIP_TEST:-false}" != "true" ]; then
    echo "🧪 Testing deployment with manual execution..."
    
    EXECUTION_NAME=$(gcloud run jobs execute ${CLOUD_RUN_JOB_NAME} \
        --region=${GCP_REGION} \
        --format="value(metadata.name)" 2>/dev/null || echo "")
    
    if [ -n "$EXECUTION_NAME" ]; then
        echo "🏃 Execution started: $EXECUTION_NAME"
        echo "⏳ Waiting for execution to complete (timeout: 5 minutes)..."
        
        # Wait for execution to complete (max 5 minutes for pipeline)
        timeout=300
        elapsed=0
        while [ $elapsed -lt $timeout ]; do
            status=$(gcloud run jobs executions describe $EXECUTION_NAME \
                --region=${GCP_REGION} \
                --format="value(status.conditions[0].type)" 2>/dev/null || echo "Unknown")
            
            case "$status" in
                "Completed")
                    echo "✅ Test execution completed successfully"
                    break
                    ;;
                "Failed")
                    echo "❌ Test execution failed"
                    echo "📋 Execution details:"
                    gcloud run jobs executions describe $EXECUTION_NAME --region=${GCP_REGION} || true
                    echo "⚠️ Deployment succeeded but test execution failed - check logs"
                    break
                    ;;
                *)
                    if [ $((elapsed % 30)) -eq 0 ]; then
                        echo "⏳ Still running... ($elapsed/$timeout seconds)"
                    fi
                    ;;
            esac
            
            sleep 10
            elapsed=$((elapsed + 10))
        done
        
        if [ $elapsed -ge $timeout ]; then
            echo "⏰ Test execution timeout - check Cloud Console for final status"
            echo "🔗 Execution URL: https://console.cloud.google.com/run/jobs/executions/details/${GCP_REGION}/${EXECUTION_NAME}?project=${GCP_PROJECT_ID}"
        fi
    else
        echo "⚠️ Could not start test execution - deployment may still be successful"
    fi
else
    echo "⏭️ Skipping test execution (SKIP_TEST=true)"
fi

# Output deployment summary
echo ""
echo "🎉 Deployment completed successfully!"
echo ""
echo "📋 Deployment Summary:"
echo "  Project: ${GCP_PROJECT_ID}"
echo "  Region: ${GCP_REGION}"
echo "  Repository: ${REPOSITORY_NAME}"
echo "  Job Name: ${CLOUD_RUN_JOB_NAME}"
echo "  Image: ${DOCKER_IMAGE_NAME}:${BUILD_ID}"
echo "  Service Account: ${GCP_SERVICE_ACCOUNT}"
echo "  Build ID: ${BUILD_ID}"
echo ""
echo "🔧 Management Commands:"
echo "  Manual execution:"
echo "    gcloud run jobs execute ${CLOUD_RUN_JOB_NAME} --region=${GCP_REGION}"
echo ""
echo "  View job details:"
echo "    gcloud run jobs describe ${CLOUD_RUN_JOB_NAME} --region=${GCP_REGION}"
echo ""
echo "  View logs:"
echo "    gcloud logging read 'resource.type=\"cloud_run_job\" AND resource.labels.job_name=\"${CLOUD_RUN_JOB_NAME}\"' --limit=50"
echo ""
echo "🔗 Cloud Console URLs:"
echo "  Job: https://console.cloud.google.com/run/jobs/details/${GCP_REGION}/${CLOUD_RUN_JOB_NAME}?project=${GCP_PROJECT_ID}"
echo "  Logs: https://console.cloud.google.com/logs/query;query=resource.type%3D%22cloud_run_job%22%0Aresource.labels.job_name%3D%22${CLOUD_RUN_JOB_NAME}%22?project=${GCP_PROJECT_ID}"
echo ""
echo "🎯 Deployment successful! The job is ready for scheduled execution."
