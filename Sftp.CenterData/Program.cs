﻿using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Sftp.CenterData.Data;
using Sftp.CenterData.Models;
using Sftp.CenterData.Services;
using Sftp.Core.Abstractions;
using Sftp.Core.Configuration;
using Sftp.Core.Services;

var builder = Host.CreateApplicationBuilder(args);

// Debug environment detection
Console.WriteLine($"ASPNETCORE_ENVIRONMENT: {Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT")}");
Console.WriteLine($"DOTNET_ENVIRONMENT: {Environment.GetEnvironmentVariable("DOTNET_ENVIRONMENT")}");
Console.WriteLine($"Builder Environment: {builder.Environment.EnvironmentName}");

// Configuration
var appConfig = new AppConfiguration();
builder.Configuration.GetSection(AppConfiguration.SectionName).Bind(appConfig);
builder.Services.AddSingleton(appConfig);

// Database
var connectionString = await GetConnectionStringAsync(builder.Configuration, appConfig);
builder.Services.AddDbContext<CenterDataDbContext>(options =>
    options.UseNpgsql(connectionString));

// Core Services
builder.Services.AddScoped<ISftpService, SftpService>();
builder.Services.AddScoped<ISecretManagerService, SecretManagerService>();
builder.Services.AddScoped<IConfigurationService, ConfigurationService>();

// CenterData-specific Services
builder.Services.AddScoped<IFileProcessor<Center>, CenterDataFileProcessor>();
builder.Services.AddScoped<IDataImportService<Center>, CenterImportService>();
builder.Services.AddScoped<IImportOrchestrationService<Center>, CenterDataOrchestrationService>();

// Worker service
builder.Services.AddHostedService<CenterDataWorkerService>();

// Logging
builder.Services.AddLogging(logging =>
{
    logging.ClearProviders();
    logging.AddConsole();
    if (appConfig.IsCloudEnvironment)
    {
        // Add Google Cloud Logging if needed
    }
});

var host = builder.Build();

// Ensure database is created
using (var scope = host.Services.CreateScope())
{
    var context = scope.ServiceProvider.GetRequiredService<CenterDataDbContext>();
    await context.Database.EnsureCreatedAsync();
}

// For Cloud Run jobs, we want to run once and exit
// Check if we're in a cloud environment (Cloud Run job)
var isCloudJob = Environment.GetEnvironmentVariable("K_SERVICE") != null; // K_SERVICE is set in Cloud Run

if (isCloudJob)
{
    // Start the host but don't wait indefinitely
    await host.StartAsync();

    // Wait for the background service to complete or application to stop
    var applicationLifetime = host.Services.GetRequiredService<IHostApplicationLifetime>();
    var cancellationTokenSource = new CancellationTokenSource();

    // Set a reasonable timeout for the job (e.g., 30 minutes)
    cancellationTokenSource.CancelAfter(TimeSpan.FromMinutes(30));

    try
    {
        // Wait for the application to signal it wants to stop
        var tcs = new TaskCompletionSource<bool>();
        using var registration = applicationLifetime.ApplicationStopping.Register(() => tcs.SetResult(true));
        using var timeoutRegistration = cancellationTokenSource.Token.Register(() => tcs.SetCanceled());
        await tcs.Task;

        Console.WriteLine("Application stopping signal received");
    }
    catch (OperationCanceledException)
    {
        Console.WriteLine("Job timed out after 30 minutes");
        Environment.ExitCode = 1;
    }

    await host.StopAsync();
    Console.WriteLine("Host stopped, exiting");
}
else
{
    // For local development, run as a hosted service
    await host.RunAsync();
}

static async Task<string> GetConnectionStringAsync(IConfiguration configuration, AppConfiguration appConfig)
{
    Console.WriteLine($"Environment: {appConfig.Environment}");
    Console.WriteLine($"IsCloudEnvironment: {appConfig.IsCloudEnvironment}");
    Console.WriteLine($"ConnectionString: {appConfig.Database.ConnectionString}");

    // Force local mode for development
    var forceLocal = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") == "Development" ||
                     !string.IsNullOrEmpty(appConfig.Database.ConnectionString);

    Console.WriteLine($"ForceLocal: {forceLocal}");

    if (appConfig.IsCloudEnvironment && !forceLocal)
    {
        // In cloud environment, use configuration service to get connection string with secrets
        var serviceProvider = new ServiceCollection()
            .AddSingleton(appConfig)
            .AddScoped<ISecretManagerService, SecretManagerService>()
            .AddScoped<IConfigurationService, ConfigurationService>()
            .AddLogging()
            .BuildServiceProvider();

        var configService = serviceProvider.GetRequiredService<IConfigurationService>();
        return await configService.GetDatabaseConnectionStringAsync();
    }
    else
    {
        // In local environment, use connection string from appsettings
        Console.WriteLine($"Database.Host: {appConfig.Database.Host}");
        Console.WriteLine($"Database.Database: {appConfig.Database.Database}");
        Console.WriteLine($"Database.Username: {appConfig.Database.Username}");

        if (!string.IsNullOrEmpty(appConfig.Database.ConnectionString))
        {
            Console.WriteLine($"Using provided connection string: {appConfig.Database.ConnectionString}");
            return appConfig.Database.ConnectionString;
        }

        var connectionString = $"Host={appConfig.Database.Host};Port={appConfig.Database.Port};Database={appConfig.Database.Database};Username={appConfig.Database.Username};Password={appConfig.Database.Password};";
        Console.WriteLine($"Built connection string: {connectionString}");
        return connectionString;
    }
}
