﻿using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Sftp.CenterData.Data;
using Sftp.CenterData.Models;
using Sftp.CenterData.Services;
using Sftp.Core.Abstractions;
using Sftp.Core.Configuration;
using Sftp.Core.Services;

var builder = Host.CreateApplicationBuilder(args);

// Debug environment detection
Console.WriteLine($"ASPNETCORE_ENVIRONMENT: {Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT")}");
Console.WriteLine($"DOTNET_ENVIRONMENT: {Environment.GetEnvironmentVariable("DOTNET_ENVIRONMENT")}");
Console.WriteLine($"Builder Environment: {builder.Environment.EnvironmentName}");

// Configuration
var appConfig = new AppConfiguration();
builder.Configuration.GetSection(AppConfiguration.SectionName).Bind(appConfig);
builder.Services.AddSingleton(appConfig);

// Database
var connectionString = await GetConnectionStringAsync(builder.Configuration, appConfig);
builder.Services.AddDbContext<CenterDataDbContext>(options =>
    options.UseNpgsql(connectionString));

// Core Services
builder.Services.AddScoped<ISftpService, SftpService>();
builder.Services.AddScoped<ISecretManagerService, SecretManagerService>();
builder.Services.AddScoped<IConfigurationService, ConfigurationService>();

// CenterData-specific Services
builder.Services.AddScoped<IFileProcessor<Center>, CenterDataFileProcessor>();
builder.Services.AddScoped<IDataImportService<Center>, CenterImportService>();
builder.Services.AddScoped<IImportOrchestrationService<Center>, CenterDataOrchestrationService>();

// Worker service
builder.Services.AddHostedService<CenterDataWorkerService>();

// Logging
builder.Services.AddLogging(logging =>
{
    logging.ClearProviders();
    logging.AddConsole();
    if (appConfig.IsCloudEnvironment)
    {
        // Add Google Cloud Logging if needed
    }
});

var host = builder.Build();

// Ensure database is created
using (var scope = host.Services.CreateScope())
{
    var context = scope.ServiceProvider.GetRequiredService<CenterDataDbContext>();
    await context.Database.EnsureCreatedAsync();
}

await host.RunAsync();

static async Task<string> GetConnectionStringAsync(IConfiguration configuration, AppConfiguration appConfig)
{
    Console.WriteLine($"Environment: {appConfig.Environment}");
    Console.WriteLine($"IsCloudEnvironment: {appConfig.IsCloudEnvironment}");
    Console.WriteLine($"ConnectionString: {appConfig.Database.ConnectionString}");

    // Force local mode for development
    var forceLocal = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") == "Development" ||
                     !string.IsNullOrEmpty(appConfig.Database.ConnectionString);

    Console.WriteLine($"ForceLocal: {forceLocal}");

    if (appConfig.IsCloudEnvironment && !forceLocal)
    {
        // In cloud environment, use configuration service to get connection string with secrets
        var serviceProvider = new ServiceCollection()
            .AddSingleton(appConfig)
            .AddScoped<ISecretManagerService, SecretManagerService>()
            .AddScoped<IConfigurationService, ConfigurationService>()
            .AddLogging()
            .BuildServiceProvider();

        var configService = serviceProvider.GetRequiredService<IConfigurationService>();
        return await configService.GetDatabaseConnectionStringAsync();
    }
    else
    {
        // In local environment, use connection string from appsettings
        Console.WriteLine($"Database.Host: {appConfig.Database.Host}");
        Console.WriteLine($"Database.Database: {appConfig.Database.Database}");
        Console.WriteLine($"Database.Username: {appConfig.Database.Username}");

        if (!string.IsNullOrEmpty(appConfig.Database.ConnectionString))
        {
            Console.WriteLine($"Using provided connection string: {appConfig.Database.ConnectionString}");
            return appConfig.Database.ConnectionString;
        }

        var connectionString = $"Host={appConfig.Database.Host};Port={appConfig.Database.Port};Database={appConfig.Database.Database};Username={appConfig.Database.Username};Password={appConfig.Database.Password};";
        Console.WriteLine($"Built connection string: {connectionString}");
        return connectionString;
    }
}
