#!/bin/bash

# Google Cloud Pub/Sub Emulator Setup Script
# This script sets up and manages the Pub/Sub emulator for local development

set -e

# Configuration
PROJECT_ID="local-emulator-project-id"
TOPIC_NAME="test-topic"
SUBSCRIPTION_NAME="test-topic-sub"
EMULATOR_HOST="localhost:8085"
EMULATOR_DATA_DIR="./pubsub-emulator-data"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if emulator is running
check_emulator_status() {
    if curl -s http://$EMULATOR_HOST > /dev/null 2>&1; then
        return 0
    else
        return 1
    fi
}

# Function to start the emulator
start_emulator() {
    print_status "Starting Google Cloud Pub/Sub emulator..."
    
    # Create data directory if it doesn't exist
    mkdir -p "$EMULATOR_DATA_DIR"
    
    # Start the emulator in the background
    gcloud beta emulators pubsub start \
        --project="$PROJECT_ID" \
        --host-port="$EMULATOR_HOST" \
        --data-dir="$EMULATOR_DATA_DIR" \
        --log-http > emulator.log 2>&1 &
    
    # Store the PID
    echo $! > emulator.pid
    
    print_status "Waiting for emulator to start..."
    
    # Wait for emulator to be ready (max 30 seconds)
    for i in {1..30}; do
        if check_emulator_status; then
            print_success "Pub/Sub emulator started successfully on $EMULATOR_HOST"
            return 0
        fi
        sleep 1
    done
    
    print_error "Failed to start emulator within 30 seconds"
    return 1
}

# Function to stop the emulator
stop_emulator() {
    print_status "Stopping Google Cloud Pub/Sub emulator..."
    
    if [ -f emulator.pid ]; then
        PID=$(cat emulator.pid)
        if kill -0 $PID > /dev/null 2>&1; then
            kill $PID
            rm emulator.pid
            print_success "Emulator stopped successfully"
        else
            print_warning "Emulator process not found"
            rm emulator.pid
        fi
    else
        print_warning "No emulator PID file found"
    fi
}

# Function to setup topics and subscriptions
setup_topics_and_subscriptions() {
    print_status "Setting up topics and subscriptions..."
    
    # Set environment variable for emulator
    export PUBSUB_EMULATOR_HOST="$EMULATOR_HOST"
    
    # Create topic
    print_status "Creating topic: $TOPIC_NAME"
    gcloud pubsub topics create "$TOPIC_NAME" --project="$PROJECT_ID" || print_warning "Topic may already exist"
    
    # Create subscription
    print_status "Creating subscription: $SUBSCRIPTION_NAME"
    gcloud pubsub subscriptions create "$SUBSCRIPTION_NAME" \
        --topic="$TOPIC_NAME" \
        --project="$PROJECT_ID" || print_warning "Subscription may already exist"
    
    print_success "Topics and subscriptions setup complete"
}

# Function to publish a test message
publish_test_message() {
    print_status "Publishing test message..."
    
    export PUBSUB_EMULATOR_HOST="$EMULATOR_HOST"
    
    local message="Hello from Pub/Sub emulator! Timestamp: $(date)"
    
    gcloud pubsub topics publish "$TOPIC_NAME" \
        --message="$message" \
        --project="$PROJECT_ID"
    
    print_success "Test message published: $message"
}

# Function to show environment variables
show_env_vars() {
    print_status "Environment variables for your application:"
    echo ""
    echo "export PUBSUB_EMULATOR_HOST=\"$EMULATOR_HOST\""
    echo "export GOOGLE_CLOUD_PROJECT=\"$PROJECT_ID\""
    echo ""
    print_status "Add these to your shell or IDE configuration"
}

# Function to show status
show_status() {
    print_status "Checking emulator status..."
    
    if check_emulator_status; then
        print_success "Emulator is running on $EMULATOR_HOST"
        
        export PUBSUB_EMULATOR_HOST="$EMULATOR_HOST"
        
        print_status "Topics:"
        gcloud pubsub topics list --project="$PROJECT_ID" 2>/dev/null || print_warning "Could not list topics"
        
        print_status "Subscriptions:"
        gcloud pubsub subscriptions list --project="$PROJECT_ID" 2>/dev/null || print_warning "Could not list subscriptions"
    else
        print_error "Emulator is not running"
    fi
}

# Main script logic
case "${1:-help}" in
    start)
        if check_emulator_status; then
            print_warning "Emulator is already running"
        else
            start_emulator
            if [ $? -eq 0 ]; then
                setup_topics_and_subscriptions
                show_env_vars
            fi
        fi
        ;;
    stop)
        stop_emulator
        ;;
    restart)
        stop_emulator
        sleep 2
        start_emulator
        if [ $? -eq 0 ]; then
            setup_topics_and_subscriptions
        fi
        ;;
    setup)
        if check_emulator_status; then
            setup_topics_and_subscriptions
        else
            print_error "Emulator is not running. Start it first with: $0 start"
        fi
        ;;
    publish)
        if check_emulator_status; then
            publish_test_message
        else
            print_error "Emulator is not running. Start it first with: $0 start"
        fi
        ;;
    status)
        show_status
        ;;
    env)
        show_env_vars
        ;;
    help|*)
        echo "Google Cloud Pub/Sub Emulator Management Script"
        echo ""
        echo "Usage: $0 {start|stop|restart|setup|publish|status|env|help}"
        echo ""
        echo "Commands:"
        echo "  start    - Start the emulator and setup topics/subscriptions"
        echo "  stop     - Stop the emulator"
        echo "  restart  - Restart the emulator"
        echo "  setup    - Setup topics and subscriptions (emulator must be running)"
        echo "  publish  - Publish a test message"
        echo "  status   - Show emulator status and list topics/subscriptions"
        echo "  env      - Show environment variables needed for your application"
        echo "  help     - Show this help message"
        echo ""
        echo "Configuration:"
        echo "  Project ID: $PROJECT_ID"
        echo "  Topic: $TOPIC_NAME"
        echo "  Subscription: $SUBSCRIPTION_NAME"
        echo "  Emulator Host: $EMULATOR_HOST"
        ;;
esac
