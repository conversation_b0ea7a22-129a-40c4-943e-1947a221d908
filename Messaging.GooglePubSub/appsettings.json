{"Logging": {"LogLevel": {"Default": "Debug", "Microsoft.EntityFrameworkCore": "Warning", "Microsoft.Hosting.Lifetime": "Information", "Messaging.GooglePubSub.Services.GooglePubSubHttpService": "Debug"}}, "MessagingConfiguration": {"Environment": "Development", "IsCloudEnvironmentOverride": false, "GcpProjectId": "local-emulator-project", "Database": {"ConnectionString": "Host=127.0.0.1;Port=5432;Database=messaging_db;Username=postgres;Password=********;", "Host": "127.0.0.1", "Port": 5432, "Database": "messaging_db", "Username": "postgres", "Password": "********"}, "AzureServiceBus": {"ConnectionString": "", "QueueName": "", "TopicName": "", "SubscriptionName": "", "MaxConcurrentMessages": 10, "MessageLockDurationMinutes": 5, "MaxRetryAttempts": 3}, "GooglePubSub": {"ProjectId": "local-emulator-project-id", "SubscriptionName": "test-topic-sub", "TopicName": "test-topic", "MaxMessages": 100, "AckDeadlineSeconds": 600, "MaxRetryAttempts": 3, "EnableMessageOrdering": false, "Protocol": "http", "EnableMultipleTopics": false, "TopicSubscriptions": [{"SubscriptionName": "user-events-subscription", "TopicName": "user-events-topic", "MaxConcurrentMessages": 10, "AckDeadlineSeconds": 300, "MaxDeliveryAttempts": 3, "DeadLetterTopic": "user-events-deadletter", "EnableMessageOrdering": true, "RetryPolicy": {"MaxRetryAttempts": 3, "InitialRetryDelaySeconds": 30, "MaxRetryDelaySeconds": 600, "RetryDelayMultiplier": 2.0, "UseExponentialBackoff": true}}, {"SubscriptionName": "analytics-subscription", "TopicName": "analytics-topic", "MaxConcurrentMessages": 20, "AckDeadlineSeconds": 600, "MaxDeliveryAttempts": 5, "EnableMessageOrdering": false}], "Routing": {"EnableRouting": true, "LogRoutingDecisions": true, "DefaultProcessorType": "GenericProcessor", "Rules": [{"Name": "UserEventsToUserProcessor", "Description": "Route user events to user processor", "Priority": 100, "Enabled": true, "Conditions": [{"Field": "TopicName", "Operator": "Equals", "Value": "user-events-topic"}], "Action": {"ActionType": "RouteToProcessor", "ProcessorType": "UserEventProcessor", "StopProcessing": true}}, {"Name": "HighPriorityMessages", "Description": "Route high priority messages to priority processor", "Priority": 200, "Enabled": true, "Conditions": [{"Field": "Labels.priority", "Operator": "Equals", "Value": "high"}], "Action": {"ActionType": "RouteToProcessor", "ProcessorType": "PriorityProcessor", "StopProcessing": true}}]}}, "Processing": {"BatchSize": 100, "MaxConcurrentBatches": 5, "ProcessingTimeoutMinutes": 30, "EnableDeadLetterQueue": true, "RetryDelaySeconds": 30, "TempDirectory": "/tmp"}}}