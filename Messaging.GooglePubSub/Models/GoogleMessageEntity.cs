using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Messaging.GooglePubSub.Models;

[Table("google_messages")]
public class GoogleMessageEntity
{
    [Key]
    [Column("id")]
    public int Id { get; set; }

    [Column("message_id")]
    [MaxLength(500)]
    public string MessageId { get; set; } = string.Empty;

    [Column("ordering_key")]
    [MaxLength(500)]
    public string? OrderingKey { get; set; }

    [Column("message_type")]
    [MaxLength(100)]
    public string? MessageType { get; set; }

    [Column("data")]
    public string Data { get; set; } = string.Empty;

    [Column("attributes")]
    public string? Attributes { get; set; } // JSON serialized attributes

    [Column("publish_time")]
    public DateTime PublishTime { get; set; }

    [Column("delivery_attempt")]
    public int? DeliveryAttempt { get; set; }

    [Column("ack_id")]
    [MaxLength(1000)]
    public string? AckId { get; set; }

    [Column("topic_name")]
    [MaxLength(200)]
    public string? TopicName { get; set; }

    [Column("subscription_name")]
    [MaxLength(200)]
    public string? SubscriptionName { get; set; }

    [Column("processed_at")]
    public DateTime ProcessedAt { get; set; } = DateTime.UtcNow;

    [Column("created_at")]
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    [Column("import_batch_id")]
    [MaxLength(100)]
    public string? ImportBatchId { get; set; }

    // Parsed business data fields - customize based on your message structure
    [Column("event_id")]
    [MaxLength(100)]
    public string? EventId { get; set; }

    [Column("user_id")]
    [MaxLength(100)]
    public string? UserId { get; set; }

    [Column("event_type")]
    [MaxLength(100)]
    public string? EventType { get; set; }

    [Column("event_data")]
    public string? EventData { get; set; } // JSON serialized event data

    [Column("source_system")]
    [MaxLength(100)]
    public string? SourceSystem { get; set; }

    [Column("schema_version")]
    [MaxLength(20)]
    public string? SchemaVersion { get; set; }

    [Column("trace_id")]
    [MaxLength(100)]
    public string? TraceId { get; set; }
}
