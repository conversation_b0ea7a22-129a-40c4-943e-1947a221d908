using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Messaging.GooglePubSub.Data;
using Messaging.GooglePubSub.Models;
using Messaging.GooglePubSub.Services;
using Messaging.Core.Abstractions;
using Messaging.Core.Configuration;
using Messaging.Core.Services;

var builder = Host.CreateApplicationBuilder(args);

// Configuration
var messagingConfig = new MessagingConfiguration();
builder.Configuration.GetSection(MessagingConfiguration.SectionName).Bind(messagingConfig);
builder.Services.AddSingleton(messagingConfig);

// Database
var connectionString = await GetConnectionStringAsync(builder.Configuration, messagingConfig);
builder.Services.AddDbContext<GoogleMessageDbContext>(options =>
    options.UseNpgsql(connectionString));

// Core Services
builder.Services.AddScoped<ISecretManagerService, SecretManagerService>();
builder.Services.AddScoped<IConfigurationService, ConfigurationService>();

// Google Pub/Sub Services - Choose between gRPC and HTTP based on configuration
builder.Services.AddSingleton<IMessagingService>(provider =>
{
    var configService = provider.GetRequiredService<IConfigurationService>();
    var pubsubConfig = configService.GetGooglePubSubConfigurationAsync().Result;
    var loggerFactory = provider.GetRequiredService<ILoggerFactory>();

    // Debug logging
    var debugLogger = loggerFactory.CreateLogger("ServiceRegistration");
    debugLogger.LogInformation("Protocol setting: {Protocol}", pubsubConfig.Protocol);
    debugLogger.LogInformation("UseHttpProtocol: {UseHttpProtocol}", pubsubConfig.UseHttpProtocol);

    if (pubsubConfig.UseHttpProtocol)
    {
        var logger = loggerFactory.CreateLogger<GooglePubSubHttpService>();
        debugLogger.LogInformation("Using GooglePubSubHttpService");
        return new GooglePubSubHttpService(logger, pubsubConfig);
    }
    else
    {
        var logger = loggerFactory.CreateLogger<GooglePubSubService>();
        debugLogger.LogInformation("Using GooglePubSubService (gRPC)");
        return new GooglePubSubService(logger, pubsubConfig);
    }
});

builder.Services.AddScoped<IMessageProcessor<GoogleMessageEntity>, GoogleMessageProcessor>();
builder.Services.AddScoped<IMessageImportService<GoogleMessageEntity>, GoogleDataImportService>();
builder.Services.AddScoped<GoogleOrchestrationService>();

// Worker service
builder.Services.AddHostedService<GoogleWorkerService>();

// Logging
builder.Services.AddLogging(logging =>
{
    logging.ClearProviders();
    logging.AddConsole();
    if (messagingConfig.IsCloudEnvironment)
    {
        // Add Google Cloud Logging if needed
    }
});

var host = builder.Build();

// Ensure database is created
using (var scope = host.Services.CreateScope())
{
    var context = scope.ServiceProvider.GetRequiredService<GoogleMessageDbContext>();
    await context.Database.EnsureCreatedAsync();
}

await host.RunAsync();

static async Task<string> GetConnectionStringAsync(IConfiguration configuration, MessagingConfiguration messagingConfig)
{
    if (messagingConfig.IsCloudEnvironment)
    {
        // In cloud environment, use configuration service to get connection string with secrets
        var serviceProvider = new ServiceCollection()
            .AddSingleton(messagingConfig)
            .AddScoped<ISecretManagerService, SecretManagerService>()
            .AddScoped<IConfigurationService, ConfigurationService>()
            .AddLogging()
            .BuildServiceProvider();

        var configService = serviceProvider.GetRequiredService<IConfigurationService>();
        return await configService.GetDatabaseConnectionStringAsync();
    }
    else
    {
        // In local environment, use connection string from appsettings
        if (!string.IsNullOrEmpty(messagingConfig.Database.ConnectionString))
        {
            return messagingConfig.Database.ConnectionString;
        }

        return $"Host={messagingConfig.Database.Host};Port={messagingConfig.Database.Port};Database={messagingConfig.Database.Database};Username={messagingConfig.Database.Username};Password={messagingConfig.Database.Password};";
    }
}
