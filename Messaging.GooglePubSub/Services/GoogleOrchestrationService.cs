using Microsoft.Extensions.Logging;
using Messaging.GooglePubSub.Models;
using Messaging.Core.Abstractions;
using Messaging.Core.Configuration;
using Messaging.Core.Models;

namespace Messaging.GooglePubSub.Services;

public class GoogleOrchestrationService
{
    private readonly ILogger<GoogleOrchestrationService> _logger;
    private readonly IMessagingService _messagingService;
    private readonly IMessageProcessor<GoogleMessageEntity> _messageProcessor;
    private readonly IMessageImportService<GoogleMessageEntity> _importService;
    private readonly MessagingConfiguration _config;

    public GoogleOrchestrationService(
        ILogger<GoogleOrchestrationService> logger,
        IMessagingService messagingService,
        IMessageProcessor<GoogleMessageEntity> messageProcessor,
        IMessageImportService<GoogleMessageEntity> importService,
        MessagingConfiguration config)
    {
        _logger = logger;
        _messagingService = messagingService;
        _messageProcessor = messageProcessor;
        _importService = importService;
        _config = config;
    }

    public async Task<bool> ProcessMessagesAsync(CancellationToken cancellationToken = default)
    {
        var batchId = Guid.NewGuid().ToString();
        
        _logger.LogInformation("Starting Google Pub/Sub message processing with batch ID: {BatchId}", batchId);

        // Create message log
        var messageLog = new MessageLog
        {
            BatchId = batchId,
            MessageId = "BATCH",
            SourceSystem = _messagingService.PlatformName,
            ProcessorType = _messageProcessor.ProcessorType,
            StartTime = DateTime.UtcNow,
            Status = "Processing"
        };

        try
        {
            messageLog = await _importService.CreateMessageLogAsync(messageLog, cancellationToken);

            // Step 1: Receive messages
            var messages = await ReceiveMessagesAsync(cancellationToken);
            if (!messages.Any())
            {
                _logger.LogInformation("No messages received from Google Pub/Sub");
                await UpdateMessageLogWithCompletion(messageLog, 0, 0, 0, 0, "No messages to process", cancellationToken);
                return true;
            }

            messageLog.MessagesProcessed = messages.Count();
            await _importService.UpdateMessageLogAsync(messageLog, cancellationToken);

            // Step 2: Process messages
            var processingResult = await _messageProcessor.ProcessMessagesAsync(messages, batchId, cancellationToken);
            
            if (!processingResult.IsSuccess)
            {
                _logger.LogWarning("Message processing completed with errors. Valid: {Valid}, Invalid: {Invalid}", 
                    processingResult.MessagesValid, processingResult.MessagesInvalid);
            }

            // Step 3: Import to database
            ImportResult importResult = new ImportResult();
            if (processingResult.Entities.Any())
            {
                importResult = await _importService.ImportEntitiesAsync(
                    processingResult.Entities, batchId, _messageProcessor.ProcessorType, cancellationToken);
            }

            // Step 4: Acknowledge messages
            var acknowledgmentResults = await AcknowledgeMessagesAsync(messages, importResult.IsSuccess, cancellationToken);

            // Step 5: Update message log
            var totalFailed = processingResult.MessagesInvalid + importResult.RecordsFailed + 
                             acknowledgmentResults.Count(r => !r.IsSuccess);

            await UpdateMessageLogWithCompletion(
                messageLog,
                processingResult.MessagesProcessed,
                importResult.RecordsInserted,
                importResult.RecordsUpdated,
                totalFailed,
                importResult.IsSuccess && processingResult.IsSuccess ? "Completed" : "Completed with errors",
                cancellationToken);

            var overallSuccess = totalFailed == 0;
            
            _logger.LogInformation("Google Pub/Sub processing completed. Batch ID: {BatchId}, Success: {Success}, " +
                "Processed: {Processed}, Inserted: {Inserted}, Updated: {Updated}, Failed: {Failed}",
                batchId, overallSuccess, processingResult.MessagesProcessed, 
                importResult.RecordsInserted, importResult.RecordsUpdated, totalFailed);

            return overallSuccess;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Google Pub/Sub processing failed for batch ID: {BatchId}", batchId);
            await UpdateMessageLogWithError(messageLog, $"Processing failed: {ex.Message}", cancellationToken);
            return false;
        }
    }

    public async Task<HealthCheckResult> CheckHealthAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Performing health check for Google Pub/Sub");
            
            var healthResult = await _messagingService.CheckHealthAsync(cancellationToken);
            
            if (healthResult.IsHealthy)
            {
                _logger.LogDebug("Google Pub/Sub health check passed");
            }
            else
            {
                _logger.LogWarning("Google Pub/Sub health check failed: {Error}", healthResult.ErrorMessage);
            }

            return healthResult;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during Google Pub/Sub health check");
            
            return new HealthCheckResult
            {
                IsHealthy = false,
                ErrorMessage = ex.Message
            };
        }
    }

    private async Task<IEnumerable<MessageData>> ReceiveMessagesAsync(CancellationToken cancellationToken)
    {
        try
        {
            var maxMessages = _config.Processing.BatchSize;
            _logger.LogDebug("Receiving up to {MaxMessages} messages from Google Pub/Sub", maxMessages);

            var messages = await _messagingService.ReceiveMessagesAsync(maxMessages, cancellationToken);
            
            _logger.LogDebug("Received {MessageCount} messages from Google Pub/Sub", messages.Count());
            return messages;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to receive messages from Google Pub/Sub");
            return Enumerable.Empty<MessageData>();
        }
    }

    private async Task<IEnumerable<AcknowledgmentResult>> AcknowledgeMessagesAsync(
        IEnumerable<MessageData> messages, 
        bool processingSuccessful, 
        CancellationToken cancellationToken)
    {
        var results = new List<AcknowledgmentResult>();

        foreach (var message in messages)
        {
            try
            {
                AcknowledgmentResult result;

                if (processingSuccessful)
                {
                    // Acknowledge the message if processing was successful
                    result = await _messagingService.CompleteMessageAsync(message, cancellationToken);
                }
                else
                {
                    // Abandon the message if processing failed (for retry)
                    result = await _messagingService.AbandonMessageAsync(message, cancellationToken);
                }

                results.Add(result);

                if (!result.IsSuccess)
                {
                    _logger.LogWarning("Failed to acknowledge message {MessageId}: {Error}", 
                        message.MessageId, result.ErrorMessage);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error acknowledging message {MessageId}", message.MessageId);
                
                results.Add(new AcknowledgmentResult
                {
                    MessageId = message.MessageId,
                    IsSuccess = false,
                    ErrorMessage = ex.Message,
                    Action = processingSuccessful ? AcknowledgmentAction.Complete : AcknowledgmentAction.Abandon
                });
            }
        }

        var successCount = results.Count(r => r.IsSuccess);
        _logger.LogDebug("Acknowledged {SuccessCount}/{TotalCount} messages", successCount, results.Count);

        return results;
    }

    private async Task UpdateMessageLogWithCompletion(
        MessageLog messageLog, 
        int processed, 
        int inserted, 
        int updated, 
        int failed,
        string status,
        CancellationToken cancellationToken)
    {
        try
        {
            messageLog.MessagesProcessed = processed;
            messageLog.MessagesInserted = inserted;
            messageLog.MessagesUpdated = updated;
            messageLog.MessagesFailed = failed;
            messageLog.EndTime = DateTime.UtcNow;
            messageLog.Status = status;

            await _importService.UpdateMessageLogAsync(messageLog, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to update message log with completion status");
        }
    }

    private async Task UpdateMessageLogWithError(MessageLog messageLog, string errorMessage, CancellationToken cancellationToken)
    {
        try
        {
            messageLog.Status = "Failed";
            messageLog.ErrorMessage = errorMessage;
            messageLog.EndTime = DateTime.UtcNow;
            await _importService.UpdateMessageLogAsync(messageLog, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to update message log with error message: {ErrorMessage}", errorMessage);
        }
    }
}
