using Microsoft.Extensions.Logging;
using Messaging.GooglePubSub.Models;
using Messaging.Core.Abstractions;
using Messaging.Core.Models;
using System.Text.Json;

namespace Messaging.GooglePubSub.Services;

public class GoogleMessageProcessor : BaseMessageProcessor<GoogleMessageEntity>
{
    public override string ProcessorType => "GooglePubSub_Generic";
    public override string[] SupportedMessageTypes => new[] { "*" }; // Support all message types
    public override string PlatformName => "GooglePubSub";

    public GoogleMessageProcessor(ILogger<GoogleMessageProcessor> logger) : base(logger)
    {
    }

    public override async Task<ProcessingResult<GoogleMessageEntity>> ProcessMessagesAsync(
        IEnumerable<MessageData> messages, 
        string batchId, 
        CancellationToken cancellationToken = default)
    {
        Logger.LogInformation("Starting to process {MessageCount} Google Pub/Sub messages with batch ID: {BatchId}", 
            messages.Count(), batchId);

        var result = new ProcessingResult<GoogleMessageEntity>();
        var entities = new List<GoogleMessageEntity>();
        var messagesList = messages.ToList();

        result.MessagesProcessed = messagesList.Count;

        try
        {
            foreach (var message in messagesList)
            {
                cancellationToken.ThrowIfCancellationRequested();

                try
                {
                    var entity = await ProcessSingleMessageAsync(message, batchId, cancellationToken);
                    if (entity != null)
                    {
                        entities.Add(entity);
                        result.MessagesValid++;
                    }
                    else
                    {
                        result.MessagesInvalid++;
                        result.Errors.Add($"Failed to process message: {message.MessageId}");
                    }
                }
                catch (Exception ex)
                {
                    Logger.LogWarning(ex, "Failed to process message {MessageId}", message.MessageId);
                    result.MessagesInvalid++;
                    result.Errors.Add($"Error processing message {message.MessageId}: {ex.Message}");
                }
            }

            result.Entities = entities;
            UpdateProcessingStats(result.MessagesProcessed, result.MessagesValid, result.MessagesInvalid);

            Logger.LogInformation("Successfully processed {ValidCount}/{TotalCount} Google Pub/Sub messages", 
                result.MessagesValid, result.MessagesProcessed);

            return result;
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Failed to process Google Pub/Sub messages for batch ID: {BatchId}", batchId);
            result.Errors.Add($"Batch processing failed: {ex.Message}");
            return result;
        }
    }

    protected override async Task<bool> ValidateMessageContentAsync(MessageData message, CancellationToken cancellationToken)
    {
        try
        {
            // Basic validation for Google Pub/Sub messages
            if (string.IsNullOrEmpty(message.Body))
            {
                Logger.LogWarning("Message {MessageId} has empty body", message.MessageId);
                return false;
            }

            // Try to parse as JSON if it looks like JSON
            if (message.Body.TrimStart().StartsWith("{") || message.Body.TrimStart().StartsWith("["))
            {
                try
                {
                    JsonDocument.Parse(message.Body);
                }
                catch (JsonException ex)
                {
                    Logger.LogWarning("Message {MessageId} has invalid JSON body: {Error}", message.MessageId, ex.Message);
                    return false;
                }
            }

            return true;
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error validating Google Pub/Sub message: {MessageId}", message.MessageId);
            return false;
        }
    }

    private async Task<GoogleMessageEntity?> ProcessSingleMessageAsync(MessageData message, string batchId, CancellationToken cancellationToken)
    {
        try
        {
            var entity = new GoogleMessageEntity
            {
                MessageId = message.MessageId,
                MessageType = message.MessageType,
                Data = message.Body,
                PublishTime = message.EnqueuedTime,
                DeliveryAttempt = message.DeliveryCount,
                ImportBatchId = batchId,
                ProcessedAt = DateTime.UtcNow,
                CreatedAt = DateTime.UtcNow
            };

            // Extract properties from message
            if (message.Properties.Count > 0)
            {
                entity.Attributes = JsonSerializer.Serialize(message.Properties);
                
                // Extract Pub/Sub specific properties
                entity.OrderingKey = GetMessageProperty<string>(message, "OrderingKey");
                entity.AckId = GetMessageProperty<string>(message, "AckId");
                entity.TopicName = GetMessageProperty<string>(message, "TopicName");
                entity.SubscriptionName = GetMessageProperty<string>(message, "SubscriptionName");
                
                // Extract delivery attempt if available
                var deliveryAttempt = GetMessageProperty<int?>(message, "DeliveryAttempt");
                if (deliveryAttempt.HasValue)
                {
                    entity.DeliveryAttempt = deliveryAttempt.Value;
                }
            }

            // Parse message body for common business data
            await ParseBusinessDataAsync(entity, message, cancellationToken);

            return entity;
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error processing single Google Pub/Sub message: {MessageId}", message.MessageId);
            return null;
        }
    }

    private async Task ParseBusinessDataAsync(GoogleMessageEntity entity, MessageData message, CancellationToken cancellationToken)
    {
        try
        {
            // Try to parse common business data from JSON body
            if (message.Body.TrimStart().StartsWith("{"))
            {
                var jsonDoc = JsonDocument.Parse(message.Body);
                var root = jsonDoc.RootElement;

                // Extract common fields - customize based on your message structure
                if (root.TryGetProperty("eventId", out var eventIdElement))
                {
                    entity.EventId = eventIdElement.GetString();
                }

                if (root.TryGetProperty("userId", out var userIdElement))
                {
                    entity.UserId = userIdElement.GetString();
                }

                if (root.TryGetProperty("eventType", out var eventTypeElement))
                {
                    entity.EventType = eventTypeElement.GetString();
                }

                if (root.TryGetProperty("sourceSystem", out var sourceSystemElement))
                {
                    entity.SourceSystem = sourceSystemElement.GetString();
                }

                if (root.TryGetProperty("schemaVersion", out var schemaVersionElement))
                {
                    entity.SchemaVersion = schemaVersionElement.GetString();
                }

                if (root.TryGetProperty("traceId", out var traceIdElement))
                {
                    entity.TraceId = traceIdElement.GetString();
                }

                // Store the entire event data as JSON
                if (root.TryGetProperty("data", out var dataElement))
                {
                    entity.EventData = dataElement.GetRawText();
                }
                else if (root.TryGetProperty("payload", out var payloadElement))
                {
                    entity.EventData = payloadElement.GetRawText();
                }
                else
                {
                    // If no specific "data" or "payload" property, store the entire message
                    entity.EventData = message.Body;
                }
            }
            else
            {
                // For non-JSON messages, store as plain text
                entity.EventData = message.Body;
                entity.EventType = message.MessageType ?? "Unknown";
            }

            // Extract additional metadata from message attributes
            if (message.Properties.TryGetValue("eventType", out var attrEventType))
            {
                entity.EventType ??= attrEventType?.ToString();
            }

            if (message.Properties.TryGetValue("sourceSystem", out var attrSourceSystem))
            {
                entity.SourceSystem ??= attrSourceSystem?.ToString();
            }

            if (message.Properties.TryGetValue("traceId", out var attrTraceId))
            {
                entity.TraceId ??= attrTraceId?.ToString();
            }
        }
        catch (Exception ex)
        {
            Logger.LogWarning(ex, "Failed to parse business data from message {MessageId}, storing as raw data", message.MessageId);
            entity.EventData = message.Body;
            entity.EventType = message.MessageType ?? "Unknown";
        }

        await Task.CompletedTask; // Placeholder for async operations if needed
    }
}
