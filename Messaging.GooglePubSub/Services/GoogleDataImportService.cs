using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Messaging.GooglePubSub.Data;
using Messaging.GooglePubSub.Models;
using Messaging.Core.Abstractions;
using Messaging.Core.Configuration;
using Messaging.Core.Models;

namespace Messaging.GooglePubSub.Services;

public class GoogleDataImportService : IMessageImportService<GoogleMessageEntity>
{
    private readonly ILogger<GoogleDataImportService> _logger;
    private readonly GoogleMessageDbContext _context;
    private readonly MessagingConfiguration _config;

    public GoogleDataImportService(
        ILogger<GoogleDataImportService> logger,
        GoogleMessageDbContext context,
        MessagingConfiguration config)
    {
        _logger = logger;
        _context = context;
        _config = config;
    }

    public async Task<ImportResult> ImportEntitiesAsync(IEnumerable<GoogleMessageEntity> entities, string batchId, string processorType, CancellationToken cancellationToken = default)
    {
        var result = new ImportResult();
        var entitiesList = entities.ToList();
        result.MessagesProcessed = entitiesList.Count;

        _logger.LogInformation("Starting import of {EntityCount} Google Pub/Sub message entities with batch ID: {BatchId}", entitiesList.Count, batchId);

        try
        {
            // Process entities in batches
            var batchSize = _config.Processing.BatchSize;
            var batches = entitiesList.Chunk(batchSize);

            foreach (var batch in batches)
            {
                cancellationToken.ThrowIfCancellationRequested();
                
                var batchResult = await ProcessBatchAsync(batch, batchId, cancellationToken);
                
                result.RecordsInserted += batchResult.RecordsInserted;
                result.RecordsUpdated += batchResult.RecordsUpdated;
                result.RecordsFailed += batchResult.RecordsFailed;
                result.Errors.AddRange(batchResult.Errors);

                _logger.LogDebug("Processed Google Pub/Sub message batch: {Inserted} inserted, {Updated} updated, {Failed} failed", 
                    batchResult.RecordsInserted, batchResult.RecordsUpdated, batchResult.RecordsFailed);
            }

            _logger.LogInformation("Google Pub/Sub message import completed. Total: {Inserted} inserted, {Updated} updated, {Failed} failed", 
                result.RecordsInserted, result.RecordsUpdated, result.RecordsFailed);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to import Google Pub/Sub message entities for batch ID: {BatchId}", batchId);
            result.Errors.Add($"Import failed: {ex.Message}");
            result.RecordsFailed = result.MessagesProcessed - result.RecordsInserted - result.RecordsUpdated;
            return result;
        }
    }

    public async Task<MessageLog> CreateMessageLogAsync(MessageLog messageLog, CancellationToken cancellationToken = default)
    {
        _logger.LogDebug("Creating message log for batch ID: {BatchId}", messageLog.BatchId);

        _context.MessageLogs.Add(messageLog);
        await _context.SaveChangesAsync(cancellationToken);

        _logger.LogDebug("Created message log with ID: {MessageLogId}", messageLog.Id);
        return messageLog;
    }

    public async Task<MessageLog> UpdateMessageLogAsync(MessageLog messageLog, CancellationToken cancellationToken = default)
    {
        _logger.LogDebug("Updating message log with ID: {MessageLogId}", messageLog.Id);

        _context.MessageLogs.Update(messageLog);
        await _context.SaveChangesAsync(cancellationToken);

        _logger.LogDebug("Updated message log with ID: {MessageLogId}", messageLog.Id);
        return messageLog;
    }

    public async Task<IEnumerable<MessageLog>> GetMessageLogsByBatchAsync(string batchId, CancellationToken cancellationToken = default)
    {
        return await _context.MessageLogs
            .Where(ml => ml.BatchId == batchId)
            .OrderBy(ml => ml.CreatedAt)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<MessageLog>> GetRecentMessageLogsAsync(int hours = 24, CancellationToken cancellationToken = default)
    {
        var cutoffTime = DateTime.UtcNow.AddHours(-hours);
        
        return await _context.MessageLogs
            .Where(ml => ml.CreatedAt >= cutoffTime && ml.SourceSystem == "GooglePubSub")
            .OrderByDescending(ml => ml.CreatedAt)
            .Take(1000) // Limit to prevent large result sets
            .ToListAsync(cancellationToken);
    }

    private async Task<ImportResult> ProcessBatchAsync(IEnumerable<GoogleMessageEntity> batch, string batchId, CancellationToken cancellationToken)
    {
        var result = new ImportResult();
        var batchList = batch.ToList();

        using var transaction = await _context.Database.BeginTransactionAsync(cancellationToken);

        try
        {
            foreach (var entity in batchList)
            {
                cancellationToken.ThrowIfCancellationRequested();

                try
                {
                    // Check if message already exists based on MessageId
                    var existingEntity = await _context.GoogleMessages
                        .FirstOrDefaultAsync(x => x.MessageId == entity.MessageId, cancellationToken);

                    if (existingEntity != null)
                    {
                        // Update existing entity (in case of reprocessing)
                        UpdateExistingEntity(existingEntity, entity);
                        result.RecordsUpdated++;
                    }
                    else
                    {
                        // Insert new entity
                        _context.GoogleMessages.Add(entity);
                        result.RecordsInserted++;
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Failed to process Google Pub/Sub message entity with MessageId: {MessageId}", entity.MessageId);
                    result.RecordsFailed++;
                    result.Errors.Add($"Failed to process MessageId {entity.MessageId}: {ex.Message}");
                }
            }

            await _context.SaveChangesAsync(cancellationToken);
            await transaction.CommitAsync(cancellationToken);

            return result;
        }
        catch (Exception ex)
        {
            await transaction.RollbackAsync(cancellationToken);
            _logger.LogError(ex, "Failed to process Google Pub/Sub message batch for batch ID: {BatchId}", batchId);
            
            result.RecordsFailed = batchList.Count;
            result.RecordsInserted = 0;
            result.RecordsUpdated = 0;
            result.Errors.Add($"Batch processing failed: {ex.Message}");
            
            return result;
        }
    }

    private static void UpdateExistingEntity(GoogleMessageEntity existing, GoogleMessageEntity newEntity)
    {
        // Update fields that might change on reprocessing
        existing.DeliveryAttempt = newEntity.DeliveryAttempt;
        existing.ProcessedAt = newEntity.ProcessedAt;
        existing.ImportBatchId = newEntity.ImportBatchId;
        
        // Update business data if it has changed
        existing.EventId = newEntity.EventId;
        existing.UserId = newEntity.UserId;
        existing.EventType = newEntity.EventType;
        existing.EventData = newEntity.EventData;
        existing.SourceSystem = newEntity.SourceSystem;
        existing.SchemaVersion = newEntity.SchemaVersion;
        existing.TraceId = newEntity.TraceId;
        existing.Attributes = newEntity.Attributes;
    }
}
