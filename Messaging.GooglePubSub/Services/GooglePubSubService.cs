using Google.Cloud.PubSub.V1;
using Microsoft.Extensions.Logging;
using Messaging.Core.Abstractions;
using Messaging.Core.Configuration;
using Messaging.Core.Models;
using System.Text.Json;
using Grpc.Net.Client;
using System.Net.Http;
using Google.Api.Gax.Grpc;

namespace Messaging.GooglePubSub.Services;

public class GooglePubSubService : IMessagingService
{
    private readonly ILogger<GooglePubSubService> _logger;
    private readonly GooglePubSubConfiguration _config;
    private readonly IMessageRouter? _messageRouter;
    private SubscriberServiceApiClient? _subscriber;
    private SubscriptionName? _subscriptionName;
    private readonly Dictionary<string, SubscriptionName> _topicSubscriptions = new();

    public string PlatformName => "GooglePubSub";

    public GooglePubSubService(
        ILogger<GooglePubSubService> logger,
        GooglePubSubConfiguration config,
        IMessageRouter? messageRouter = null)
    {
        _logger = logger;
        _config = config;
        _messageRouter = messageRouter;
    }

    public async Task StartAsync(CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Starting Google Pub/Sub service");

        try
        {
            var emulatorHost = Environment.GetEnvironmentVariable("PUBSUB_EMULATOR_HOST");

            if (!string.IsNullOrEmpty(emulatorHost))
            {
                _logger.LogInformation("Using Google Pub/Sub emulator at: {EmulatorHost}", emulatorHost);
                _logger.LogInformation("Creating Google Pub/Sub subscriber client for emulator");
            }
            else
            {
                _logger.LogInformation("Creating Google Pub/Sub subscriber client for production");
            }

            // Use the standard client creation - it will automatically detect emulator
            _subscriber = await SubscriberServiceApiClient.CreateAsync();

            if (_config.UseMultipleTopics)
            {
                // Initialize multiple topic subscriptions
                foreach (var subscription in _config.TopicSubscriptions)
                {
                    var subscriptionName = SubscriptionName.FromProjectSubscription(_config.ProjectId, subscription.SubscriptionName);
                    var key = $"{subscription.TopicName}/{subscription.SubscriptionName}";
                    _topicSubscriptions[key] = subscriptionName;
                    _logger.LogInformation("Added subscription: {SubscriptionName} for topic: {TopicName}",
                        subscription.SubscriptionName, subscription.TopicName);
                }

                if (!_topicSubscriptions.Any())
                {
                    throw new InvalidOperationException("No topic subscriptions configured for multiple topic mode");
                }
            }
            else
            {
                _subscriptionName = SubscriptionName.FromProjectSubscription(_config.ProjectId, _config.SubscriptionName);
                _logger.LogInformation("Created subscriber for subscription: {SubscriptionName}", _config.SubscriptionName);
            }

            _logger.LogInformation("Google Pub/Sub service started successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to start Google Pub/Sub service");
            throw;
        }
    }

    public async Task StopAsync(CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Stopping Google Pub/Sub service");

        try
        {
            if (_subscriber != null)
            {
                await SubscriberServiceApiClient.ShutdownDefaultChannelsAsync();
            }

            _logger.LogInformation("Google Pub/Sub service stopped successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error stopping Google Pub/Sub service");
        }
    }

    public async Task<IEnumerable<MessageData>> ReceiveMessagesAsync(int maxMessages, CancellationToken cancellationToken = default)
    {
        if (_config.UseMultipleTopics)
        {
            return await ReceiveFromMultipleTopicsAsync(maxMessages, cancellationToken);
        }

        if (_subscriber == null || _subscriptionName == null)
        {
            throw new InvalidOperationException("Pub/Sub subscriber is not initialized. Call StartAsync first.");
        }

        try
        {
            _logger.LogDebug("Receiving up to {MaxMessages} messages from Google Pub/Sub", maxMessages);

            var pullRequest = new PullRequest
            {
                SubscriptionAsSubscriptionName = _subscriptionName,
                MaxMessages = maxMessages
            };

            var response = await _subscriber.PullAsync(pullRequest, cancellationToken);
            var messageDataList = new List<MessageData>();

            foreach (var receivedMessage in response.ReceivedMessages)
            {
                var messageData = ConvertToMessageData(receivedMessage);
                messageDataList.Add(messageData);
            }

            _logger.LogDebug("Received {MessageCount} messages from Google Pub/Sub", messageDataList.Count);
            return messageDataList;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to receive messages from Google Pub/Sub");
            return Enumerable.Empty<MessageData>();
        }
    }

    public async Task<AcknowledgmentResult> CompleteMessageAsync(MessageData messageData, CancellationToken cancellationToken = default)
    {
        if (_subscriber == null || _subscriptionName == null)
        {
            return new AcknowledgmentResult
            {
                MessageId = messageData.MessageId,
                IsSuccess = false,
                ErrorMessage = "Pub/Sub subscriber is not initialized",
                Action = AcknowledgmentAction.Complete
            };
        }

        try
        {
            if (messageData.PlatformMessage is ReceivedMessage receivedMessage)
            {
                var ackRequest = new AcknowledgeRequest
                {
                    SubscriptionAsSubscriptionName = _subscriptionName,
                    AckIds = { receivedMessage.AckId }
                };

                await _subscriber.AcknowledgeAsync(ackRequest, cancellationToken);
                
                _logger.LogDebug("Acknowledged message: {MessageId}", messageData.MessageId);
                
                return new AcknowledgmentResult
                {
                    MessageId = messageData.MessageId,
                    IsSuccess = true,
                    Action = AcknowledgmentAction.Complete
                };
            }

            return new AcknowledgmentResult
            {
                MessageId = messageData.MessageId,
                IsSuccess = false,
                ErrorMessage = "Invalid platform message type",
                Action = AcknowledgmentAction.Complete
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to acknowledge message: {MessageId}", messageData.MessageId);
            
            return new AcknowledgmentResult
            {
                MessageId = messageData.MessageId,
                IsSuccess = false,
                ErrorMessage = ex.Message,
                Action = AcknowledgmentAction.Complete
            };
        }
    }

    public async Task<AcknowledgmentResult> AbandonMessageAsync(MessageData messageData, CancellationToken cancellationToken = default)
    {
        if (_subscriber == null || _subscriptionName == null)
        {
            return new AcknowledgmentResult
            {
                MessageId = messageData.MessageId,
                IsSuccess = false,
                ErrorMessage = "Pub/Sub subscriber is not initialized",
                Action = AcknowledgmentAction.Abandon
            };
        }

        try
        {
            if (messageData.PlatformMessage is ReceivedMessage receivedMessage)
            {
                // In Pub/Sub, we modify the ack deadline to 0 to make the message available for redelivery
                var modifyRequest = new ModifyAckDeadlineRequest
                {
                    SubscriptionAsSubscriptionName = _subscriptionName,
                    AckIds = { receivedMessage.AckId },
                    AckDeadlineSeconds = 0
                };

                await _subscriber.ModifyAckDeadlineAsync(modifyRequest, cancellationToken);
                
                _logger.LogDebug("Abandoned message: {MessageId}", messageData.MessageId);
                
                return new AcknowledgmentResult
                {
                    MessageId = messageData.MessageId,
                    IsSuccess = true,
                    Action = AcknowledgmentAction.Abandon
                };
            }

            return new AcknowledgmentResult
            {
                MessageId = messageData.MessageId,
                IsSuccess = false,
                ErrorMessage = "Invalid platform message type",
                Action = AcknowledgmentAction.Abandon
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to abandon message: {MessageId}", messageData.MessageId);
            
            return new AcknowledgmentResult
            {
                MessageId = messageData.MessageId,
                IsSuccess = false,
                ErrorMessage = ex.Message,
                Action = AcknowledgmentAction.Abandon
            };
        }
    }

    public async Task<AcknowledgmentResult> DeadLetterMessageAsync(MessageData messageData, string reason, CancellationToken cancellationToken = default)
    {
        // Google Pub/Sub handles dead lettering automatically based on subscription configuration
        // We'll just acknowledge the message and log the reason
        _logger.LogWarning("Dead lettering message: {MessageId}, Reason: {Reason}", messageData.MessageId, reason);
        
        var result = await CompleteMessageAsync(messageData, cancellationToken);
        result.Action = AcknowledgmentAction.DeadLetter;
        
        return result;
    }

    public async Task<HealthCheckResult> CheckHealthAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            if (_subscriber == null || _subscriptionName == null)
            {
                return new HealthCheckResult
                {
                    IsHealthy = false,
                    ErrorMessage = "Pub/Sub subscriber is not initialized"
                };
            }

            // Try to get subscription details as a health check
            var subscriptionRequest = new GetSubscriptionRequest
            {
                SubscriptionAsSubscriptionName = _subscriptionName
            };

            var subscription = await _subscriber.GetSubscriptionAsync(subscriptionRequest, cancellationToken);
            
            return new HealthCheckResult
            {
                IsHealthy = true,
                Details = new Dictionary<string, object>
                {
                    ["SubscriptionName"] = _config.SubscriptionName,
                    ["TopicName"] = subscription.TopicAsTopicName?.TopicId ?? "Unknown",
                    ["AckDeadlineSeconds"] = subscription.AckDeadlineSeconds,
                    ["MessageRetentionDuration"] = subscription.MessageRetentionDuration?.ToString() ?? "Unknown"
                }
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Health check failed for Google Pub/Sub");
            
            return new HealthCheckResult
            {
                IsHealthy = false,
                ErrorMessage = ex.Message
            };
        }
    }

    public Dictionary<string, object> GetConnectionInfo()
    {
        return new Dictionary<string, object>
        {
            ["PlatformName"] = PlatformName,
            ["ProjectId"] = _config.ProjectId,
            ["SubscriptionName"] = _config.SubscriptionName,
            ["TopicName"] = _config.TopicName ?? "N/A",
            ["MaxMessages"] = _config.MaxMessages,
            ["AckDeadlineSeconds"] = _config.AckDeadlineSeconds
        };
    }

    private MessageData ConvertToMessageData(ReceivedMessage receivedMessage)
    {
        var message = receivedMessage.Message;
        var properties = new Dictionary<string, object>();
        
        // Add message attributes
        foreach (var attr in message.Attributes)
        {
            properties[attr.Key] = attr.Value;
        }

        // Add Pub/Sub specific properties
        properties["OrderingKey"] = message.OrderingKey ?? string.Empty;
        properties["AckId"] = receivedMessage.AckId;
        properties["DeliveryAttempt"] = receivedMessage.DeliveryAttempt;

        // Decode message data
        var messageBody = System.Text.Encoding.UTF8.GetString(message.Data.ToByteArray());

        var messageData = new MessageData
        {
            MessageId = message.MessageId,
            MessageType = message.Attributes.TryGetValue("messageType", out var msgType) ? msgType : null,
            Body = messageBody,
            Properties = properties,
            EnqueuedTime = message.PublishTime.ToDateTime(),
            DeliveryCount = receivedMessage.DeliveryAttempt,
            RoutingKey = message.Attributes.TryGetValue("routingKey", out var routingKey) ? routingKey : null,
            ContentType = message.Attributes.TryGetValue("contentType", out var contentType) ? contentType : null,
            Priority = message.Attributes.TryGetValue("priority", out var priority) && int.TryParse(priority, out var p) ? p : 0,
            PlatformMessage = receivedMessage
        };

        // Add labels from attributes
        foreach (var attr in message.Attributes.Where(a => a.Key.StartsWith("label.")))
        {
            var labelName = attr.Key.Substring(6); // Remove "label." prefix
            messageData.Labels[labelName] = attr.Value;
        }

        return messageData;
    }

    private async Task<IEnumerable<MessageData>> ReceiveFromMultipleTopicsAsync(int maxMessages, CancellationToken cancellationToken)
    {
        if (_subscriber == null)
        {
            throw new InvalidOperationException("Pub/Sub subscriber is not initialized. Call StartAsync first.");
        }

        var allMessages = new List<MessageData>();
        var messagesPerSubscription = Math.Max(1, maxMessages / _topicSubscriptions.Count);

        var receiveTasks = _topicSubscriptions.Select(async kvp =>
        {
            var (key, subscriptionName) = (kvp.Key, kvp.Value);
            var parts = key.Split('/');
            var topicName = parts[0];
            var subscriptionNameStr = parts[1];

            try
            {
                _logger.LogDebug("Receiving up to {MaxMessages} messages from topic {TopicName}, subscription {SubscriptionName}",
                    messagesPerSubscription, topicName, subscriptionNameStr);

                var pullRequest = new PullRequest
                {
                    SubscriptionAsSubscriptionName = subscriptionName,
                    MaxMessages = messagesPerSubscription
                };

                var response = await _subscriber.PullAsync(pullRequest, cancellationToken);
                var messageDataList = new List<MessageData>();

                foreach (var receivedMessage in response.ReceivedMessages)
                {
                    var messageData = ConvertToMessageData(receivedMessage);
                    messageData.TopicName = topicName;
                    messageData.SubscriptionName = subscriptionNameStr;

                    // Apply message routing if configured
                    if (_messageRouter != null)
                    {
                        var routedProcessor = _messageRouter.RouteMessage(messageData);
                        if (!string.IsNullOrEmpty(routedProcessor))
                        {
                            messageData.Properties["RoutedProcessor"] = routedProcessor;
                        }
                    }

                    messageDataList.Add(messageData);
                }

                _logger.LogDebug("Received {MessageCount} messages from topic {TopicName}, subscription {SubscriptionName}",
                    messageDataList.Count, topicName, subscriptionNameStr);

                return messageDataList;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to receive messages from topic {TopicName}, subscription {SubscriptionName}",
                    topicName, subscriptionNameStr);
                return Enumerable.Empty<MessageData>();
            }
        });

        var results = await Task.WhenAll(receiveTasks);

        foreach (var result in results)
        {
            allMessages.AddRange(result);
        }

        _logger.LogDebug("Received total of {MessageCount} messages from {SubscriptionCount} topic subscriptions",
            allMessages.Count, _topicSubscriptions.Count);

        return allMessages;
    }
}
