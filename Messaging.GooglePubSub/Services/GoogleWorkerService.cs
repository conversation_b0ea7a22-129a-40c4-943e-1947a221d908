using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Messaging.Core.Configuration;

namespace Messaging.GooglePubSub.Services;

public class GoogleWorkerService : BackgroundService
{
    private readonly ILogger<GoogleWorkerService> _logger;
    private readonly IServiceProvider _serviceProvider;
    private readonly MessagingConfiguration _config;

    public GoogleWorkerService(
        ILogger<GoogleWorkerService> logger,
        IServiceProvider serviceProvider,
        MessagingConfiguration config)
    {
        _logger = logger;
        _serviceProvider = serviceProvider;
        _config = config;
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        _logger.LogInformation("Google Pub/Sub Worker Service started");

        try
        {
            using var scope = _serviceProvider.CreateScope();
            var orchestrationService = scope.ServiceProvider.GetRequiredService<GoogleOrchestrationService>();

            // Check if this is a one-time run or continuous processing
            var args = Environment.GetCommandLineArgs();
            var isOneTimeRun = args.Contains("--once") || args.Contains("-o");

            if (isOneTimeRun)
            {
                _logger.LogInformation("Running Google Pub/Sub processing once");
                var success = await orchestrationService.ProcessMessagesAsync(stoppingToken);
                
                if (success)
                {
                    _logger.LogInformation("Google Pub/Sub one-time processing completed successfully");
                }
                else
                {
                    _logger.LogError("Google Pub/Sub one-time processing failed");
                    Environment.ExitCode = 1;
                }
            }
            else
            {
                _logger.LogInformation("Starting continuous Google Pub/Sub processing");
                await RunContinuousProcessingAsync(orchestrationService, stoppingToken);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Google Pub/Sub Worker Service encountered an error");
            Environment.ExitCode = 1;
        }
        finally
        {
            _logger.LogInformation("Google Pub/Sub Worker Service completed");
        }
    }

    private async Task RunContinuousProcessingAsync(GoogleOrchestrationService orchestrationService, CancellationToken stoppingToken)
    {
        var processingInterval = TimeSpan.FromSeconds(30); // Default polling interval
        var consecutiveFailures = 0;
        const int maxConsecutiveFailures = 5;

        while (!stoppingToken.IsCancellationRequested)
        {
            try
            {
                _logger.LogDebug("Starting Google Pub/Sub processing cycle");
                
                var success = await orchestrationService.ProcessMessagesAsync(stoppingToken);
                
                if (success)
                {
                    consecutiveFailures = 0;
                    _logger.LogDebug("Google Pub/Sub processing cycle completed successfully");
                }
                else
                {
                    consecutiveFailures++;
                    _logger.LogWarning("Google Pub/Sub processing cycle failed. Consecutive failures: {FailureCount}", 
                        consecutiveFailures);

                    if (consecutiveFailures >= maxConsecutiveFailures)
                    {
                        _logger.LogError("Maximum consecutive failures ({MaxFailures}) reached. Stopping service.", 
                            maxConsecutiveFailures);
                        Environment.ExitCode = 1;
                        break;
                    }
                }

                // Wait before next processing cycle
                await Task.Delay(processingInterval, stoppingToken);
            }
            catch (OperationCanceledException)
            {
                _logger.LogInformation("Google Pub/Sub processing cancelled");
                break;
            }
            catch (Exception ex)
            {
                consecutiveFailures++;
                _logger.LogError(ex, "Error in Google Pub/Sub processing cycle. Consecutive failures: {FailureCount}", 
                    consecutiveFailures);

                if (consecutiveFailures >= maxConsecutiveFailures)
                {
                    _logger.LogError("Maximum consecutive failures ({MaxFailures}) reached. Stopping service.", 
                        maxConsecutiveFailures);
                    Environment.ExitCode = 1;
                    break;
                }

                // Wait longer after an error
                await Task.Delay(TimeSpan.FromMinutes(1), stoppingToken);
            }
        }
    }

    public override async Task StartAsync(CancellationToken cancellationToken)
    {
        _logger.LogInformation("Starting Google Pub/Sub messaging service");

        try
        {
            using var scope = _serviceProvider.CreateScope();
            var messagingService = scope.ServiceProvider.GetRequiredService<Core.Abstractions.IMessagingService>();
            
            await messagingService.StartAsync(cancellationToken);
            
            _logger.LogInformation("Google Pub/Sub messaging service started successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to start Google Pub/Sub messaging service");
            throw;
        }

        await base.StartAsync(cancellationToken);
    }

    public override async Task StopAsync(CancellationToken cancellationToken)
    {
        _logger.LogInformation("Stopping Google Pub/Sub messaging service");

        try
        {
            using var scope = _serviceProvider.CreateScope();
            var messagingService = scope.ServiceProvider.GetRequiredService<Core.Abstractions.IMessagingService>();
            
            await messagingService.StopAsync(cancellationToken);
            
            _logger.LogInformation("Google Pub/Sub messaging service stopped successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error stopping Google Pub/Sub messaging service");
        }

        await base.StopAsync(cancellationToken);
    }
}
