using Google.Cloud.PubSub.V1;
using Microsoft.Extensions.Logging;
using Messaging.Core.Abstractions;
using Messaging.Core.Configuration;
using Messaging.Core.Models;

namespace Messaging.GooglePubSub.Services;

/// <summary>
/// Google Pub/Sub implementation of topic subscription management
/// </summary>
public class GoogleTopicSubscriptionManager : ITopicSubscriptionManager
{
    private readonly ILogger<GoogleTopicSubscriptionManager> _logger;
    private readonly GooglePubSubConfiguration _config;
    private readonly SubscriberServiceApiClient _subscriberClient;
    private readonly PublisherServiceApiClient _publisherClient;

    public string PlatformName => "GooglePubSub";

    public GoogleTopicSubscriptionManager(
        ILogger<GoogleTopicSubscriptionManager> logger,
        GooglePubSubConfiguration config)
    {
        _logger = logger;
        _config = config;
        _subscriberClient = SubscriberServiceApiClient.Create();
        _publisherClient = PublisherServiceApiClient.Create();
    }

    public async Task<IEnumerable<TopicSubscription>> GetActiveSubscriptionsAsync(CancellationToken cancellationToken = default)
    {
        var subscriptions = new List<TopicSubscription>();

        try
        {
            var projectName = $"projects/{_config.ProjectId}";
            var request = new ListSubscriptionsRequest
            {
                Project = projectName
            };

            await foreach (var subscription in _subscriberClient.ListSubscriptionsAsync(request))
            {
                var topicSubscription = new TopicSubscription
                {
                    SubscriptionName = subscription.SubscriptionName.SubscriptionId,
                    TopicName = subscription.TopicAsTopicName?.TopicId ?? "Unknown",
                    Status = SubscriptionStatus.Active, // Google Pub/Sub doesn't have explicit status
                    CreatedAt = DateTime.UtcNow, // Google Pub/Sub doesn't provide creation time
                    UpdatedAt = DateTime.UtcNow,
                    Configuration = new TopicSubscriptionConfig
                    {
                        SubscriptionName = subscription.SubscriptionName.SubscriptionId,
                        TopicName = subscription.TopicAsTopicName?.TopicId ?? "Unknown",
                        AckDeadlineSeconds = subscription.AckDeadlineSeconds,
                        EnableMessageOrdering = subscription.EnableMessageOrdering
                    },
                    PlatformDetails = new Dictionary<string, object>
                    {
                        ["AckDeadlineSeconds"] = subscription.AckDeadlineSeconds,
                        ["MessageRetentionDuration"] = subscription.MessageRetentionDuration?.ToString() ?? "Unknown",
                        ["EnableMessageOrdering"] = subscription.EnableMessageOrdering,
                        ["Filter"] = subscription.Filter ?? "No filter",
                        ["DeadLetterPolicy"] = subscription.DeadLetterPolicy?.ToString() ?? "None",
                        ["RetryPolicy"] = subscription.RetryPolicy?.ToString() ?? "Default"
                    }
                };

                subscriptions.Add(topicSubscription);
            }

            _logger.LogInformation("Retrieved {Count} active subscriptions", subscriptions.Count);
            return subscriptions;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get active subscriptions");
            throw;
        }
    }

    public async Task<TopicSubscription> CreateSubscriptionAsync(TopicSubscriptionConfig subscription, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Creating subscription {SubscriptionName} for topic {TopicName}", 
                subscription.SubscriptionName, subscription.TopicName);

            var topicName = TopicName.FromProjectTopic(_config.ProjectId, subscription.TopicName);
            var subscriptionName = SubscriptionName.FromProjectSubscription(_config.ProjectId, subscription.SubscriptionName);

            // Ensure topic exists
            try
            {
                await _publisherClient.GetTopicAsync(topicName, cancellationToken);
            }
            catch (Grpc.Core.RpcException ex) when (ex.StatusCode == Grpc.Core.StatusCode.NotFound)
            {
                await _publisherClient.CreateTopicAsync(topicName, cancellationToken);
                _logger.LogInformation("Created topic {TopicName}", subscription.TopicName);
            }

            // Create subscription
            var subscriptionRequest = new Subscription
            {
                SubscriptionName = subscriptionName,
                TopicAsTopicName = topicName,
                AckDeadlineSeconds = subscription.AckDeadlineSeconds,
                EnableMessageOrdering = subscription.EnableMessageOrdering
            };

            // Add retry policy if configured
            if (subscription.RetryPolicy != null)
            {
                subscriptionRequest.RetryPolicy = new RetryPolicy
                {
                    MinimumBackoff = Google.Protobuf.WellKnownTypes.Duration.FromTimeSpan(
                        TimeSpan.FromSeconds(subscription.RetryPolicy.InitialRetryDelaySeconds)),
                    MaximumBackoff = Google.Protobuf.WellKnownTypes.Duration.FromTimeSpan(
                        TimeSpan.FromSeconds(subscription.RetryPolicy.MaxRetryDelaySeconds))
                };
            }

            // Add dead letter policy if configured
            if (!string.IsNullOrEmpty(subscription.DeadLetterTopic))
            {
                var deadLetterTopicName = TopicName.FromProjectTopic(_config.ProjectId, subscription.DeadLetterTopic);
                subscriptionRequest.DeadLetterPolicy = new DeadLetterPolicy
                {
                    DeadLetterTopic = deadLetterTopicName.ToString(),
                    MaxDeliveryAttempts = subscription.MaxDeliveryAttempts
                };
            }

            var createdSubscription = await _subscriberClient.CreateSubscriptionAsync(subscriptionRequest, cancellationToken);

            _logger.LogInformation("Successfully created subscription {SubscriptionName} for topic {TopicName}", 
                subscription.SubscriptionName, subscription.TopicName);

            return new TopicSubscription
            {
                SubscriptionName = createdSubscription.SubscriptionName.SubscriptionId,
                TopicName = createdSubscription.TopicAsTopicName?.TopicId ?? subscription.TopicName,
                Status = SubscriptionStatus.Active,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow,
                Configuration = subscription
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to create subscription {SubscriptionName} for topic {TopicName}", 
                subscription.SubscriptionName, subscription.TopicName);
            throw;
        }
    }

    public async Task<TopicSubscription> UpdateSubscriptionAsync(string subscriptionName, TopicSubscriptionConfig config, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Updating subscription {SubscriptionName}", subscriptionName);

            var subscriptionNameObj = SubscriptionName.FromProjectSubscription(_config.ProjectId, subscriptionName);
            var existingSubscription = await _subscriberClient.GetSubscriptionAsync(subscriptionNameObj, cancellationToken);

            // Update properties
            existingSubscription.AckDeadlineSeconds = config.AckDeadlineSeconds;
            existingSubscription.EnableMessageOrdering = config.EnableMessageOrdering;

            // Update retry policy if configured
            if (config.RetryPolicy != null)
            {
                existingSubscription.RetryPolicy = new RetryPolicy
                {
                    MinimumBackoff = Google.Protobuf.WellKnownTypes.Duration.FromTimeSpan(
                        TimeSpan.FromSeconds(config.RetryPolicy.InitialRetryDelaySeconds)),
                    MaximumBackoff = Google.Protobuf.WellKnownTypes.Duration.FromTimeSpan(
                        TimeSpan.FromSeconds(config.RetryPolicy.MaxRetryDelaySeconds))
                };
            }

            // Update dead letter policy if configured
            if (!string.IsNullOrEmpty(config.DeadLetterTopic))
            {
                var deadLetterTopicName = TopicName.FromProjectTopic(_config.ProjectId, config.DeadLetterTopic);
                existingSubscription.DeadLetterPolicy = new DeadLetterPolicy
                {
                    DeadLetterTopic = deadLetterTopicName.ToString(),
                    MaxDeliveryAttempts = config.MaxDeliveryAttempts
                };
            }

            var updateMask = new Google.Protobuf.WellKnownTypes.FieldMask();
            updateMask.Paths.Add("ack_deadline_seconds");
            updateMask.Paths.Add("enable_message_ordering");
            if (config.RetryPolicy != null) updateMask.Paths.Add("retry_policy");
            if (!string.IsNullOrEmpty(config.DeadLetterTopic)) updateMask.Paths.Add("dead_letter_policy");

            var updateRequest = new UpdateSubscriptionRequest
            {
                Subscription = existingSubscription,
                UpdateMask = updateMask
            };

            var updatedSubscription = await _subscriberClient.UpdateSubscriptionAsync(updateRequest, cancellationToken);

            _logger.LogInformation("Successfully updated subscription {SubscriptionName}", subscriptionName);

            return new TopicSubscription
            {
                SubscriptionName = updatedSubscription.SubscriptionName.SubscriptionId,
                TopicName = updatedSubscription.TopicAsTopicName?.TopicId ?? config.TopicName,
                Status = SubscriptionStatus.Active,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow,
                Configuration = config
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to update subscription {SubscriptionName}", subscriptionName);
            throw;
        }
    }

    public async Task DeleteSubscriptionAsync(string subscriptionName, CancellationToken cancellationToken = default)
    {
        try
        {
            var subscriptionNameObj = SubscriptionName.FromProjectSubscription(_config.ProjectId, subscriptionName);
            await _subscriberClient.DeleteSubscriptionAsync(subscriptionNameObj, cancellationToken);
            
            _logger.LogInformation("Deleted subscription {SubscriptionName}", subscriptionName);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to delete subscription {SubscriptionName}", subscriptionName);
            throw;
        }
    }

    public async Task<SubscriptionHealth> GetSubscriptionHealthAsync(string subscriptionName, CancellationToken cancellationToken = default)
    {
        try
        {
            var subscriptionNameObj = SubscriptionName.FromProjectSubscription(_config.ProjectId, subscriptionName);
            var subscription = await _subscriberClient.GetSubscriptionAsync(subscriptionNameObj, cancellationToken);

            // Get subscription metrics (this would typically require additional monitoring setup)
            return new SubscriptionHealth
            {
                SubscriptionName = subscriptionName,
                IsHealthy = true, // Assume healthy if we can retrieve it
                CheckedAt = DateTime.UtcNow,
                PendingMessages = 0, // Would need monitoring API to get actual metrics
                UnacknowledgedMessages = 0,
                Metrics = new Dictionary<string, object>
                {
                    ["AckDeadlineSeconds"] = subscription.AckDeadlineSeconds,
                    ["EnableMessageOrdering"] = subscription.EnableMessageOrdering,
                    ["TopicName"] = subscription.TopicAsTopicName?.TopicId ?? "Unknown"
                }
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get health for subscription {SubscriptionName}", subscriptionName);
            
            return new SubscriptionHealth
            {
                SubscriptionName = subscriptionName,
                IsHealthy = false,
                CheckedAt = DateTime.UtcNow,
                ErrorMessage = ex.Message
            };
        }
    }

    public async Task<bool> TestSubscriptionAsync(string subscriptionName, CancellationToken cancellationToken = default)
    {
        try
        {
            var health = await GetSubscriptionHealthAsync(subscriptionName, cancellationToken);
            return health.IsHealthy;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to test subscription {SubscriptionName}", subscriptionName);
            return false;
        }
    }
}
