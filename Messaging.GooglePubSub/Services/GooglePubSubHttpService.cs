using System.Text;
using System.Text.Json;
using System.Text.Json.Serialization;
using Google.Cloud.PubSub.V1;
using Messaging.Core.Abstractions;
using Messaging.Core.Models;
using Messaging.Core.Configuration;
using Messaging.Core.Services;
using Microsoft.Extensions.Logging;

namespace Messaging.GooglePubSub.Services;

/// <summary>
/// HTTP/REST-based Google Pub/Sub service implementation that bypasses gRPC proxy issues
/// </summary>
public class GooglePubSubHttpService : IMessagingService
{
    private readonly ILogger<GooglePubSubHttpService> _logger;
    private readonly GooglePubSubConfiguration _config;
    private readonly IMessageRouter? _messageRouter;
    private readonly HttpClient _httpClient;
    private readonly string _baseUrl;
    private readonly Dictionary<string, string> _topicSubscriptions = new();

    public string PlatformName => "GooglePubSub-HTTP";

    public GooglePubSubHttpService(
        ILogger<GooglePubSubHttpService> logger,
        GooglePubSubConfiguration config,
        IMessageRouter? messageRouter = null)
    {
        _logger = logger;
        _config = config;
        _messageRouter = messageRouter;
        _httpClient = new HttpClient();
        
        // Determine base URL (emulator or production)
        var emulatorHost = Environment.GetEnvironmentVariable("PUBSUB_EMULATOR_HOST");
        if (!string.IsNullOrEmpty(emulatorHost))
        {
            _baseUrl = $"http://{emulatorHost}/v1";
            _logger.LogInformation("Using Google Pub/Sub emulator HTTP endpoint: {BaseUrl}", _baseUrl);
        }
        else
        {
            _baseUrl = "https://pubsub.googleapis.com/v1";
            _logger.LogInformation("Using Google Pub/Sub production HTTP endpoint: {BaseUrl}", _baseUrl);
        }
    }

    public async Task StartAsync(CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Starting Google Pub/Sub HTTP service");

        try
        {
            // Set up authentication for production (not needed for emulator)
            var emulatorHost = Environment.GetEnvironmentVariable("PUBSUB_EMULATOR_HOST");
            if (string.IsNullOrEmpty(emulatorHost))
            {
                // For production, we'll need to add authentication headers
                await SetupAuthenticationAsync();
            }

            if (_config.UseMultipleTopics)
            {
                // Initialize multiple topic subscriptions
                foreach (var subscription in _config.TopicSubscriptions)
                {
                    var key = $"{subscription.TopicName}/{subscription.SubscriptionName}";
                    _topicSubscriptions[key] = subscription.SubscriptionName;
                    _logger.LogInformation("Added subscription: {SubscriptionName} for topic: {TopicName}",
                        subscription.SubscriptionName, subscription.TopicName);
                }

                if (!_topicSubscriptions.Any())
                {
                    throw new InvalidOperationException("No topic subscriptions configured for multiple topic mode");
                }
            }
            else
            {
                _logger.LogInformation("Created HTTP subscriber for subscription: {SubscriptionName}", _config.SubscriptionName);
            }

            _logger.LogInformation("Google Pub/Sub HTTP service started successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to start Google Pub/Sub HTTP service");
            throw;
        }
    }

    public async Task StopAsync(CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Stopping Google Pub/Sub HTTP service");
        _httpClient?.Dispose();
        _logger.LogInformation("Google Pub/Sub HTTP service stopped");
    }

    public async Task<IEnumerable<MessageData>> ReceiveMessagesAsync(int maxMessages, CancellationToken cancellationToken = default)
    {
        if (_config.UseMultipleTopics)
        {
            return await ReceiveFromMultipleTopicsAsync(maxMessages, cancellationToken);
        }

        try
        {
            _logger.LogDebug("Receiving up to {MaxMessages} messages from Google Pub/Sub via HTTP", maxMessages);

            var subscriptionPath = $"projects/{_config.ProjectId}/subscriptions/{_config.SubscriptionName}";
            var pullUrl = $"{_baseUrl}/{subscriptionPath}:pull";

            _logger.LogDebug("Making HTTP pull request to: {PullUrl}", pullUrl);

            var pullRequest = new
            {
                maxMessages = maxMessages
            };

            var json = JsonSerializer.Serialize(pullRequest);
            var content = new StringContent(json, System.Text.Encoding.UTF8, "application/json");

            _logger.LogDebug("Pull request payload: {Json}", json);

            var response = await _httpClient.PostAsync(pullUrl, content, cancellationToken);
            
            _logger.LogDebug("HTTP response status: {StatusCode}", response.StatusCode);

            if (!response.IsSuccessStatusCode)
            {
                var errorContent = await response.Content.ReadAsStringAsync(cancellationToken);
                _logger.LogError("Failed to pull messages. Status: {StatusCode}, Error: {Error}",
                    response.StatusCode, errorContent);
                return Enumerable.Empty<MessageData>();
            }

            var responseContent = await response.Content.ReadAsStringAsync(cancellationToken);
            _logger.LogDebug("HTTP response content: {ResponseContent}", responseContent);

            var pullResponse = JsonSerializer.Deserialize<PullResponseDto>(responseContent);

            var messageDataList = new List<MessageData>();

            if (pullResponse?.ReceivedMessages != null)
            {
                foreach (var receivedMessage in pullResponse.ReceivedMessages)
                {
                    var messageData = ConvertToMessageData(receivedMessage);
                    messageDataList.Add(messageData);
                }
            }

            _logger.LogDebug("Received {MessageCount} messages from Google Pub/Sub via HTTP", messageDataList.Count);
            return messageDataList;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to receive messages from Google Pub/Sub via HTTP");
            return Enumerable.Empty<MessageData>();
        }
    }

    public async Task<AcknowledgmentResult> CompleteMessageAsync(MessageData messageData, CancellationToken cancellationToken = default)
    {
        try
        {
            if (messageData.PlatformMessage is ReceivedMessageDto receivedMessage)
            {
                var subscriptionPath = $"projects/{_config.ProjectId}/subscriptions/{_config.SubscriptionName}";
                var ackUrl = $"{_baseUrl}/{subscriptionPath}:acknowledge";

                var ackRequest = new
                {
                    ackIds = new[] { receivedMessage.AckId }
                };

                var json = JsonSerializer.Serialize(ackRequest);
                var content = new StringContent(json, System.Text.Encoding.UTF8, "application/json");

                var response = await _httpClient.PostAsync(ackUrl, content, cancellationToken);
                
                if (response.IsSuccessStatusCode)
                {
                    _logger.LogDebug("Acknowledged message: {MessageId}", messageData.MessageId);
                    
                    return new AcknowledgmentResult
                    {
                        MessageId = messageData.MessageId,
                        IsSuccess = true,
                        Action = AcknowledgmentAction.Complete
                    };
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync(cancellationToken);
                    _logger.LogError("Failed to acknowledge message. Status: {StatusCode}, Error: {Error}", 
                        response.StatusCode, errorContent);
                }
            }

            return new AcknowledgmentResult
            {
                MessageId = messageData.MessageId,
                IsSuccess = false,
                ErrorMessage = "Invalid platform message type or acknowledgment failed",
                Action = AcknowledgmentAction.Complete
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to acknowledge message: {MessageId}", messageData.MessageId);
            
            return new AcknowledgmentResult
            {
                MessageId = messageData.MessageId,
                IsSuccess = false,
                ErrorMessage = ex.Message,
                Action = AcknowledgmentAction.Complete
            };
        }
    }

    public async Task<AcknowledgmentResult> AbandonMessageAsync(MessageData messageData, CancellationToken cancellationToken = default)
    {
        try
        {
            if (messageData.PlatformMessage is ReceivedMessageDto receivedMessage)
            {
                var subscriptionPath = $"projects/{_config.ProjectId}/subscriptions/{_config.SubscriptionName}";
                var modifyUrl = $"{_baseUrl}/{subscriptionPath}:modifyAckDeadline";

                var modifyRequest = new
                {
                    ackIds = new[] { receivedMessage.AckId },
                    ackDeadlineSeconds = 0
                };

                var json = JsonSerializer.Serialize(modifyRequest);
                var content = new StringContent(json, System.Text.Encoding.UTF8, "application/json");

                var response = await _httpClient.PostAsync(modifyUrl, content, cancellationToken);
                
                if (response.IsSuccessStatusCode)
                {
                    _logger.LogDebug("Abandoned message: {MessageId}", messageData.MessageId);
                    
                    return new AcknowledgmentResult
                    {
                        MessageId = messageData.MessageId,
                        IsSuccess = true,
                        Action = AcknowledgmentAction.Abandon
                    };
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync(cancellationToken);
                    _logger.LogError("Failed to abandon message. Status: {StatusCode}, Error: {Error}", 
                        response.StatusCode, errorContent);
                }
            }

            return new AcknowledgmentResult
            {
                MessageId = messageData.MessageId,
                IsSuccess = false,
                ErrorMessage = "Invalid platform message type or abandon failed",
                Action = AcknowledgmentAction.Abandon
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to abandon message: {MessageId}", messageData.MessageId);
            
            return new AcknowledgmentResult
            {
                MessageId = messageData.MessageId,
                IsSuccess = false,
                ErrorMessage = ex.Message,
                Action = AcknowledgmentAction.Abandon
            };
        }
    }

    public async Task<AcknowledgmentResult> DeadLetterMessageAsync(MessageData messageData, string reason, CancellationToken cancellationToken = default)
    {
        // Google Pub/Sub handles dead lettering automatically based on subscription configuration
        // We'll just acknowledge the message and log the reason
        _logger.LogWarning("Dead lettering message: {MessageId}, Reason: {Reason}", messageData.MessageId, reason);

        var result = await CompleteMessageAsync(messageData, cancellationToken);
        result.Action = AcknowledgmentAction.DeadLetter;

        return result;
    }

    public async Task<HealthCheckResult> CheckHealthAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            // For emulator, just check if it's responding
            var emulatorHost = Environment.GetEnvironmentVariable("PUBSUB_EMULATOR_HOST");
            if (!string.IsNullOrEmpty(emulatorHost))
            {
                var healthUrl = $"http://{emulatorHost}/";
                var response = await _httpClient.GetAsync(healthUrl, cancellationToken);

                return new HealthCheckResult
                {
                    IsHealthy = response.IsSuccessStatusCode,
                    ErrorMessage = response.IsSuccessStatusCode ? null : $"Emulator returned {response.StatusCode}",
                    CheckTime = DateTime.UtcNow
                };
            }
            else
            {
                // For production, we could check a simple API endpoint
                var healthUrl = $"{_baseUrl}/projects/{_config.ProjectId}";
                var response = await _httpClient.GetAsync(healthUrl, cancellationToken);

                return new HealthCheckResult
                {
                    IsHealthy = response.IsSuccessStatusCode,
                    ErrorMessage = response.IsSuccessStatusCode ? null : $"API returned {response.StatusCode}",
                    CheckTime = DateTime.UtcNow
                };
            }
        }
        catch (Exception ex)
        {
            return new HealthCheckResult
            {
                IsHealthy = false,
                ErrorMessage = $"Health check failed: {ex.Message}",
                CheckTime = DateTime.UtcNow
            };
        }
    }

    public Dictionary<string, object> GetConnectionInfo()
    {
        var emulatorHost = Environment.GetEnvironmentVariable("PUBSUB_EMULATOR_HOST");

        return new Dictionary<string, object>
        {
            ["Platform"] = PlatformName,
            ["Protocol"] = "HTTP",
            ["ProjectId"] = _config.ProjectId,
            ["SubscriptionName"] = _config.SubscriptionName,
            ["BaseUrl"] = _baseUrl,
            ["IsEmulator"] = !string.IsNullOrEmpty(emulatorHost),
            ["EmulatorHost"] = emulatorHost ?? "N/A",
            ["MaxMessages"] = _config.MaxMessages,
            ["UseMultipleTopics"] = _config.UseMultipleTopics,
            ["TopicCount"] = _topicSubscriptions.Count
        };
    }

    private async Task SetupAuthenticationAsync()
    {
        // For production use, we would need to set up OAuth2 authentication
        // This is a placeholder for authentication setup
        _logger.LogInformation("Setting up authentication for production Google Pub/Sub HTTP API");

        // In a real implementation, you would:
        // 1. Get access token from Google Cloud credentials
        // 2. Add Authorization header to HttpClient
        // 3. Handle token refresh

        await Task.CompletedTask;
    }

    private async Task<IEnumerable<MessageData>> ReceiveFromMultipleTopicsAsync(int maxMessages, CancellationToken cancellationToken)
    {
        var allMessages = new List<MessageData>();
        var messagesPerSubscription = Math.Max(1, maxMessages / _topicSubscriptions.Count);

        foreach (var (key, subscriptionName) in _topicSubscriptions)
        {
            try
            {
                _logger.LogDebug("Receiving messages from subscription: {SubscriptionName}", subscriptionName);

                var subscriptionPath = $"projects/{_config.ProjectId}/subscriptions/{subscriptionName}";
                var pullUrl = $"{_baseUrl}/{subscriptionPath}:pull";

                var pullRequest = new
                {
                    maxMessages = messagesPerSubscription
                };

                var json = JsonSerializer.Serialize(pullRequest);
                var content = new StringContent(json, System.Text.Encoding.UTF8, "application/json");

                var response = await _httpClient.PostAsync(pullUrl, content, cancellationToken);

                if (response.IsSuccessStatusCode)
                {
                    var responseContent = await response.Content.ReadAsStringAsync(cancellationToken);
                    var pullResponse = JsonSerializer.Deserialize<PullResponseDto>(responseContent);

                    if (pullResponse?.ReceivedMessages != null)
                    {
                        foreach (var receivedMessage in pullResponse.ReceivedMessages)
                        {
                            var messageData = ConvertToMessageData(receivedMessage);

                            // Add topic information for routing
                            var topicName = key.Split('/')[0];
                            messageData.Properties["TopicName"] = topicName;
                            messageData.Properties["SubscriptionName"] = subscriptionName;

                            allMessages.Add(messageData);
                        }
                    }
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync(cancellationToken);
                    _logger.LogError("Failed to pull messages from {SubscriptionName}. Status: {StatusCode}, Error: {Error}",
                        subscriptionName, response.StatusCode, errorContent);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error receiving messages from subscription: {SubscriptionName}", subscriptionName);
            }
        }

        _logger.LogDebug("Received {MessageCount} total messages from {SubscriptionCount} subscriptions",
            allMessages.Count, _topicSubscriptions.Count);

        return allMessages;
    }

    private MessageData ConvertToMessageData(ReceivedMessageDto receivedMessage)
    {
        var messageData = new MessageData
        {
            MessageId = receivedMessage.Message.MessageId,
            Body = receivedMessage.Message.Data != null ?
                System.Text.Encoding.UTF8.GetString(Convert.FromBase64String(receivedMessage.Message.Data)) : string.Empty,
            Properties = new Dictionary<string, object>(),
            PlatformMessage = receivedMessage,
            EnqueuedTime = DateTime.UtcNow // Use EnqueuedTime instead of ReceivedAt
        };

        // Add message attributes as properties
        if (receivedMessage.Message.Attributes != null)
        {
            foreach (var (key, value) in receivedMessage.Message.Attributes)
            {
                messageData.Properties[key] = value ?? string.Empty;
            }
        }

        // Add Pub/Sub specific properties
        messageData.Properties["AckId"] = receivedMessage.AckId;
        messageData.Properties["PublishTime"] = receivedMessage.Message.PublishTime;

        return messageData;
    }
}

// DTOs for HTTP API communication
public class PullResponseDto
{
    [JsonPropertyName("receivedMessages")]
    public ReceivedMessageDto[]? ReceivedMessages { get; set; }
}

public class ReceivedMessageDto
{
    [JsonPropertyName("ackId")]
    public string AckId { get; set; } = string.Empty;

    [JsonPropertyName("message")]
    public PubSubMessageDto Message { get; set; } = new();
}

public class PubSubMessageDto
{
    [JsonPropertyName("messageId")]
    public string MessageId { get; set; } = string.Empty;

    [JsonPropertyName("data")]
    public string? Data { get; set; }

    [JsonPropertyName("attributes")]
    public Dictionary<string, string>? Attributes { get; set; }

    [JsonPropertyName("publishTime")]
    public string? PublishTime { get; set; }
}
