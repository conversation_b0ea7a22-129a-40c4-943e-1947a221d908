#!/bin/bash

# Test script for Google Pub/Sub to PostgreSQL Importer

echo "Testing Google Pub/Sub to PostgreSQL Importer"
echo "=============================================="

# Build the application
echo "Building Google Pub/Sub application..."
dotnet build -c Release

if [ $? -ne 0 ]; then
    echo "Build failed!"
    exit 1
fi

echo "Build successful!"

echo ""
echo "Google Pub/Sub processor is ready for testing!"
echo ""
echo "To test the application:"
echo "1. Set up PostgreSQL database"
echo "2. Configure appsettings.json with your database connection"
echo "3. Set up Google Pub/Sub topic and subscription"
echo "4. Configure Google Pub/Sub project ID and subscription in appsettings.json"
echo "5. Set up authentication (service account or gcloud auth)"
echo "6. Run: dotnet run --once (for one-time processing)"
echo "7. Run: dotnet run (for continuous processing)"
echo ""
echo "For cloud deployment:"
echo "1. Build Docker image: docker build -t messaging-google-pubsub ."
echo "2. Configure GCP Secret Manager: ../Scripts/setup-messaging-secrets.sh"
echo "3. Deploy to Cloud Run: ../Scripts/deploy-messaging.sh"

echo ""
echo "Architecture benefits:"
echo "- Core messaging logic is reusable across different platforms"
echo "- Google Pub/Sub-specific logic is isolated in this project"
echo "- Easy to add new messaging platforms by creating similar projects"
echo "- Independent deployment and scaling per messaging platform"

echo ""
echo "Google Pub/Sub application structure verified successfully!"
