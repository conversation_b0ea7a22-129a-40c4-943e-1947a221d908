using Microsoft.EntityFrameworkCore;
using Messaging.GooglePubSub.Models;
using Messaging.Core.Models;

namespace Messaging.GooglePubSub.Data;

public class GoogleMessageDbContext : DbContext
{
    public GoogleMessageDbContext(DbContextOptions<GoogleMessageDbContext> options) : base(options)
    {
    }

    public DbSet<GoogleMessageEntity> GoogleMessages { get; set; }
    public DbSet<MessageLog> MessageLogs { get; set; }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);

        // Configure GoogleMessageEntity
        modelBuilder.Entity<GoogleMessageEntity>(entity =>
        {
            entity.HasIndex(e => e.MessageId)
                .HasDatabaseName("ix_google_messages_message_id");

            entity.HasIndex(e => e.OrderingKey)
                .HasDatabaseName("ix_google_messages_ordering_key");

            entity.HasIndex(e => e.MessageType)
                .HasDatabaseName("ix_google_messages_message_type");

            entity.HasIndex(e => e.EventType)
                .HasDatabaseName("ix_google_messages_event_type");

            entity.HasIndex(e => e.EventId)
                .HasDatabaseName("ix_google_messages_event_id");

            entity.HasIndex(e => e.UserId)
                .HasDatabaseName("ix_google_messages_user_id");

            entity.HasIndex(e => e.PublishTime)
                .HasDatabaseName("ix_google_messages_publish_time");

            entity.HasIndex(e => e.ProcessedAt)
                .HasDatabaseName("ix_google_messages_processed_at");

            entity.HasIndex(e => e.ImportBatchId)
                .HasDatabaseName("ix_google_messages_import_batch_id");

            entity.HasIndex(e => e.CreatedAt)
                .HasDatabaseName("ix_google_messages_created_at");

            entity.HasIndex(e => e.TraceId)
                .HasDatabaseName("ix_google_messages_trace_id");

            // Unique constraint on MessageId to prevent duplicates
            entity.HasIndex(e => e.MessageId)
                .IsUnique()
                .HasDatabaseName("uk_google_messages_message_id");
        });

        // Configure MessageLog (shared with Azure Service Bus)
        modelBuilder.Entity<MessageLog>(entity =>
        {
            entity.HasIndex(e => e.BatchId)
                .HasDatabaseName("ix_message_logs_batch_id");

            entity.HasIndex(e => e.MessageId)
                .HasDatabaseName("ix_message_logs_message_id");

            entity.HasIndex(e => e.SourceSystem)
                .HasDatabaseName("ix_message_logs_source_system");

            entity.HasIndex(e => e.ProcessorType)
                .HasDatabaseName("ix_message_logs_processor_type");

            entity.HasIndex(e => e.Status)
                .HasDatabaseName("ix_message_logs_status");

            entity.HasIndex(e => e.StartTime)
                .HasDatabaseName("ix_message_logs_start_time");

            entity.HasIndex(e => e.CreatedAt)
                .HasDatabaseName("ix_message_logs_created_at");
        });
    }
}
