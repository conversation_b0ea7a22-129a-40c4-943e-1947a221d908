using Microsoft.Extensions.Logging;
using Renci.SshNet;
using Sftp.Core.Configuration;

namespace Sftp.Core.Services;

/// <summary>
/// Service implementation for SFTP (SSH File Transfer Protocol) operations using SSH.NET library.
/// Provides secure file transfer capabilities with support for multiple authentication methods.
/// </summary>
/// <remarks>
/// This service implements secure SFTP operations including:
///
/// **Core SFTP Operations:**
/// - Secure file downloads from remote SFTP servers
/// - Directory listing and file discovery
/// - File existence verification
/// - Automatic directory creation for local file storage
///
/// **Authentication Support:**
/// - Password-based authentication
/// - SSH private key authentication with optional passphrase
/// - Automatic authentication method selection based on configuration
///
/// **Connection Management:**
/// - Configurable connection and operation timeouts
/// - Proper connection lifecycle management
/// - Automatic resource cleanup and disposal
/// - Connection status validation
///
/// **Error Handling:**
/// - Comprehensive error logging for troubleshooting
/// - Graceful handling of network connectivity issues
/// - Authentication failure handling
/// - File not found and permission error handling
///
/// **Performance Features:**
/// - Asynchronous operations with cancellation token support
/// - Efficient file streaming for large file transfers
/// - Proper resource management to prevent memory leaks
///
/// The service uses the SSH.NET library for robust SFTP protocol implementation
/// and provides a clean abstraction for application-level file operations.
/// </remarks>
public class SftpService : ISftpService
{
    private readonly ILogger<SftpService> _logger;

    /// <summary>
    /// Initializes a new instance of the SftpService class.
    /// </summary>
    /// <param name="logger">Logger instance for structured logging of SFTP operations</param>
    public SftpService(ILogger<SftpService> logger)
    {
        _logger = logger;
    }

    public async Task<bool> DownloadFileAsync(SftpConfiguration config, string remoteFilePath, string localFilePath, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Starting download of {RemoteFile} to {LocalFile}", remoteFilePath, localFilePath);

            using var client = CreateSftpClient(config);
            await ConnectAsync(client, config, cancellationToken);

            if (!client.Exists(remoteFilePath))
            {
                _logger.LogWarning("Remote file {RemoteFile} does not exist", remoteFilePath);
                return false;
            }

            // Ensure local directory exists
            var localDirectory = Path.GetDirectoryName(localFilePath);
            if (!string.IsNullOrEmpty(localDirectory) && !Directory.Exists(localDirectory))
            {
                Directory.CreateDirectory(localDirectory);
            }

            using var localFileStream = File.Create(localFilePath);
            await Task.Run(() => client.DownloadFile(remoteFilePath, localFileStream), cancellationToken);

            _logger.LogInformation("Successfully downloaded {RemoteFile} to {LocalFile}", remoteFilePath, localFilePath);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to download file {RemoteFile} to {LocalFile}", remoteFilePath, localFilePath);
            return false;
        }
    }

    public async Task<IEnumerable<string>> ListFilesAsync(SftpConfiguration config, string remotePath, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Listing files in remote directory {RemotePath}", remotePath);

            using var client = CreateSftpClient(config);
            await ConnectAsync(client, config, cancellationToken);

            var files = await Task.Run(() => 
                client.ListDirectory(remotePath)
                    .Where(f => f.IsRegularFile)
                    .Select(f => f.Name)
                    .ToList(), cancellationToken);

            _logger.LogInformation("Found {FileCount} files in {RemotePath}", files.Count, remotePath);

            // Log first few file names for debugging
            if (files.Count > 0)
            {
                var sampleFiles = files.Take(10).ToList();
                _logger.LogInformation("Sample files: {SampleFiles}", string.Join(", ", sampleFiles));
            }

            return files;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to list files in remote directory {RemotePath}", remotePath);
            return Enumerable.Empty<string>();
        }
    }

    public async Task<bool> FileExistsAsync(SftpConfiguration config, string remoteFilePath, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Checking if remote file {RemoteFile} exists", remoteFilePath);

            using var client = CreateSftpClient(config);
            await ConnectAsync(client, config, cancellationToken);

            var exists = await Task.Run(() => client.Exists(remoteFilePath), cancellationToken);
            
            _logger.LogDebug("Remote file {RemoteFile} exists: {Exists}", remoteFilePath, exists);
            return exists;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to check if remote file {RemoteFile} exists", remoteFilePath);
            return false;
        }
    }

    /// <summary>
    /// Creates and configures an SFTP client with the appropriate authentication method.
    /// </summary>
    /// <param name="config">SFTP configuration containing connection and authentication details</param>
    /// <returns>Configured SftpClient instance ready for connection</returns>
    /// <remarks>
    /// This method automatically selects the appropriate authentication method based on configuration:
    /// - Private key authentication when PrivateKeyPath is specified
    /// - Password authentication when private key is not configured
    ///
    /// The method also configures connection and operation timeouts based on the provided configuration.
    /// </remarks>
    private SftpClient CreateSftpClient(SftpConfiguration config)
    {
        ConnectionInfo connectionInfo;

        if (config.UsePrivateKey)
        {
            var privateKeyFile = new PrivateKeyFile(config.PrivateKeyPath!, config.PrivateKeyPassphrase);
            var authMethod = new PrivateKeyAuthenticationMethod(config.Username, privateKeyFile);
            connectionInfo = new ConnectionInfo(config.Host, config.Port, config.Username, authMethod);
        }
        else
        {
            var authMethod = new PasswordAuthenticationMethod(config.Username, config.Password);
            connectionInfo = new ConnectionInfo(config.Host, config.Port, config.Username, authMethod);
        }

        connectionInfo.Timeout = TimeSpan.FromSeconds(config.ConnectionTimeoutSeconds);
        connectionInfo.ChannelCloseTimeout = TimeSpan.FromSeconds(config.OperationTimeoutSeconds);

        return new SftpClient(connectionInfo);
    }

    /// <summary>
    /// Establishes a connection to the SFTP server using the provided client and configuration.
    /// </summary>
    /// <param name="client">SFTP client instance to connect</param>
    /// <param name="config">SFTP configuration for connection details</param>
    /// <param name="cancellationToken">Cancellation token for the connection operation</param>
    /// <returns>Task representing the asynchronous connection operation</returns>
    /// <exception cref="InvalidOperationException">Thrown when connection fails</exception>
    /// <remarks>
    /// This method handles the connection process and validates that the connection was successful.
    /// It provides appropriate logging for connection attempts and failures.
    /// </remarks>
    private async Task ConnectAsync(SftpClient client, SftpConfiguration config, CancellationToken cancellationToken)
    {
        _logger.LogDebug("Connecting to SFTP server {Host}:{Port}", config.Host, config.Port);

        await Task.Run(() => client.Connect(), cancellationToken);

        if (!client.IsConnected)
        {
            throw new InvalidOperationException($"Failed to connect to SFTP server {config.Host}:{config.Port}");
        }

        _logger.LogDebug("Successfully connected to SFTP server {Host}:{Port}", config.Host, config.Port);
    }
}
