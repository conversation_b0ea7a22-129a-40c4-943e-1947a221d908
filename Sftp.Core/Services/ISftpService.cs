using Sftp.Core.Configuration;

namespace Sftp.Core.Services;

/// <summary>
/// Service interface for SFTP (SSH File Transfer Protocol) operations.
/// Provides secure file transfer capabilities for downloading files from remote SFTP servers.
/// </summary>
/// <remarks>
/// This service abstracts SFTP operations and provides a clean interface for:
///
/// **Core SFTP Operations:**
/// - Secure file downloads from remote servers
/// - Directory listing and file discovery
/// - File existence verification
/// - Connection management with authentication
///
/// **Security Features:**
/// - SSH-based secure file transfer
/// - Support for various authentication methods (password, key-based)
/// - Secure connection handling with proper cleanup
///
/// **Error Handling:**
/// - Network connectivity issues
/// - Authentication failures
/// - File not found scenarios
/// - Permission and access control errors
///
/// **Performance Considerations:**
/// - Efficient connection reuse where possible
/// - Proper resource cleanup and connection disposal
/// - Support for cancellation tokens for long-running operations
///
/// Implementations should handle connection lifecycle, authentication,
/// and provide appropriate error handling and logging.
/// </remarks>
public interface ISftpService
{
    /// <summary>
    /// Downloads a file from the SFTP server to a local path
    /// </summary>
    /// <param name="config">SFTP connection configuration</param>
    /// <param name="remoteFilePath">Path to the file on the SFTP server</param>
    /// <param name="localFilePath">Local path where the file should be saved</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>True if download was successful, false otherwise</returns>
    Task<bool> DownloadFileAsync(SftpConfiguration config, string remoteFilePath, string localFilePath, CancellationToken cancellationToken = default);

    /// <summary>
    /// Lists files in a directory on the SFTP server
    /// </summary>
    /// <param name="config">SFTP connection configuration</param>
    /// <param name="remotePath">Remote directory path</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>List of file names in the directory</returns>
    Task<IEnumerable<string>> ListFilesAsync(SftpConfiguration config, string remotePath, CancellationToken cancellationToken = default);

    /// <summary>
    /// Checks if a file exists on the SFTP server
    /// </summary>
    /// <param name="config">SFTP connection configuration</param>
    /// <param name="remoteFilePath">Path to the file on the SFTP server</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>True if file exists, false otherwise</returns>
    Task<bool> FileExistsAsync(SftpConfiguration config, string remoteFilePath, CancellationToken cancellationToken = default);
}
