using Sftp.Core.Configuration;

namespace Sftp.Core.Services;

/// <summary>
/// Service interface for managing application configuration with support for secret loading.
/// Provides centralized access to configuration settings including database connections,
/// SFTP settings, and other application parameters with automatic secret resolution.
/// </summary>
/// <remarks>
/// This service abstracts configuration management and secret loading, allowing the application
/// to work seamlessly across different environments (local development, cloud deployment).
/// It handles loading sensitive configuration values from secure sources like Google Cloud Secret Manager
/// while falling back to local configuration files for development scenarios.
/// </remarks>
public interface IConfigurationService
{
    /// <summary>
    /// Gets the application configuration, loading secrets from appropriate sources
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Fully populated application configuration</returns>
    Task<AppConfiguration> GetConfigurationAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets the database connection string, loading from secrets if needed
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Database connection string</returns>
    Task<string> GetDatabaseConnectionStringAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets the SFTP configuration, loading secrets if needed
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>SFTP configuration</returns>
    Task<SftpConfiguration> GetSftpConfigurationAsync(CancellationToken cancellationToken = default);
}
