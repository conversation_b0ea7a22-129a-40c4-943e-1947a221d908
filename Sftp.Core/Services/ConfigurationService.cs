using Microsoft.Extensions.Logging;
using Sftp.Core.Configuration;

namespace Sftp.Core.Services;

/// <summary>
/// Service implementation for managing application configuration with secure secret loading.
/// Provides environment-aware configuration management that seamlessly integrates with cloud secret services.
/// </summary>
/// <remarks>
/// This service implements a hybrid configuration approach that:
///
/// **Configuration Loading Strategy:**
/// - Loads base configuration from standard .NET configuration sources (appsettings.json, environment variables)
/// - Automatically detects cloud environments and loads sensitive values from Google Cloud Secret Manager
/// - Falls back to local configuration values for development environments
/// - Provides consistent configuration interface regardless of environment
///
/// **Security Features:**
/// - Separates sensitive values (passwords, keys) from general configuration
/// - Uses secure secret management services in production environments
/// - Provides appropriate logging without exposing sensitive information
/// - <PERSON>les authentication and authorization for secret access
///
/// **Supported Secrets:**
/// - Database passwords and connection strings
/// - SFTP authentication credentials
/// - Private key passphrases for SSH authentication
/// - Any other sensitive configuration values
///
/// **Error Handling:**
/// - Graceful fallback for missing or inaccessible secrets
/// - Comprehensive error logging for troubleshooting
/// - Environment-appropriate error handling strategies
/// </remarks>
public class ConfigurationService : IConfigurationService
{
    private readonly ILogger<ConfigurationService> _logger;
    private readonly AppConfiguration _baseConfig;
    private readonly ISecretManagerService _secretManager;

    /// <summary>
    /// Initializes a new instance of the ConfigurationService class.
    /// </summary>
    /// <param name="logger">Logger instance for structured logging</param>
    /// <param name="baseConfig">Base configuration loaded from standard configuration sources</param>
    /// <param name="secretManager">Secret manager service for loading sensitive configuration values</param>
    public ConfigurationService(
        ILogger<ConfigurationService> logger,
        AppConfiguration baseConfig,
        ISecretManagerService secretManager)
    {
        _logger = logger;
        _baseConfig = baseConfig;
        _secretManager = secretManager;
    }

    public async Task<AppConfiguration> GetConfigurationAsync(CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Loading application configuration for environment: {Environment}", _baseConfig.Environment);

        var config = new AppConfiguration
        {
            Environment = _baseConfig.Environment,
            GcpProjectId = _baseConfig.GcpProjectId,
            Processing = _baseConfig.Processing
        };

        // Load database configuration
        config.Database = await LoadDatabaseConfigurationAsync(cancellationToken);

        // Load SFTP configuration
        config.Sftp = await LoadSftpConfigurationAsync(cancellationToken);

        _logger.LogInformation("Application configuration loaded successfully");
        return config;
    }

    public async Task<string> GetDatabaseConnectionStringAsync(CancellationToken cancellationToken = default)
    {
        var dbConfig = await LoadDatabaseConfigurationAsync(cancellationToken);
        
        if (dbConfig.UseConnectionString)
        {
            return dbConfig.ConnectionString;
        }

        // Build connection string from individual components
        return $"Host={dbConfig.Host};Port={dbConfig.Port};Database={dbConfig.Database};Username={dbConfig.Username};Password={dbConfig.Password};";
    }

    public async Task<SftpConfiguration> GetSftpConfigurationAsync(CancellationToken cancellationToken = default)
    {
        return await LoadSftpConfigurationAsync(cancellationToken);
    }

    private async Task<DatabaseConfiguration> LoadDatabaseConfigurationAsync(CancellationToken cancellationToken)
    {
        var dbConfig = new DatabaseConfiguration
        {
            Host = _baseConfig.Database.Host,
            Port = _baseConfig.Database.Port,
            Database = _baseConfig.Database.Database,
            Username = _baseConfig.Database.Username
        };

        if (_baseConfig.IsCloudEnvironment && _secretManager.IsAvailable())
        {
            _logger.LogDebug("Loading database secrets from GCP Secret Manager");
            
            try
            {
                // Load database password from Secret Manager
                dbConfig.Password = await _secretManager.GetSecretAsync("centerdata-db-password", cancellationToken);

                // Optionally load full connection string from Secret Manager
                try
                {
                    dbConfig.ConnectionString = await _secretManager.GetSecretAsync("centerdata-db-connection", cancellationToken);
                }
                catch (Exception ex)
                {
                    _logger.LogDebug(ex, "Database connection string not found in Secret Manager, using individual components");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to load database secrets from Secret Manager");
                throw;
            }
        }
        else
        {
            _logger.LogDebug("Loading database configuration from local settings");
            dbConfig.Password = _baseConfig.Database.Password;
            dbConfig.ConnectionString = _baseConfig.Database.ConnectionString;
        }

        return dbConfig;
    }

    private async Task<SftpConfiguration> LoadSftpConfigurationAsync(CancellationToken cancellationToken)
    {
        var sftpConfig = new SftpConfiguration
        {
            Host = _baseConfig.Sftp.Host,
            Port = _baseConfig.Sftp.Port,
            Username = _baseConfig.Sftp.Username,
            PrivateKeyPath = _baseConfig.Sftp.PrivateKeyPath,
            ConnectionTimeoutSeconds = _baseConfig.Sftp.ConnectionTimeoutSeconds,
            OperationTimeoutSeconds = _baseConfig.Sftp.OperationTimeoutSeconds
        };

        if (_baseConfig.IsCloudEnvironment && _secretManager.IsAvailable())
        {
            _logger.LogDebug("Loading SFTP secrets from GCP Secret Manager");
            
            try
            {
                // Load SFTP password from Secret Manager
                sftpConfig.Password = await _secretManager.GetSecretAsync("centerdata-sftp-password", cancellationToken);
                
                // Optionally load private key passphrase from Secret Manager
                try
                {
                    sftpConfig.PrivateKeyPassphrase = await _secretManager.GetSecretAsync("sftp-private-key-passphrase", cancellationToken);
                }
                catch (Exception ex)
                {
                    _logger.LogDebug(ex, "SFTP private key passphrase not found in Secret Manager");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to load SFTP secrets from Secret Manager");
                throw;
            }
        }
        else
        {
            _logger.LogDebug("Loading SFTP configuration from local settings");
            sftpConfig.Password = _baseConfig.Sftp.Password;
            sftpConfig.PrivateKeyPassphrase = _baseConfig.Sftp.PrivateKeyPassphrase;
        }

        return sftpConfig;
    }
}
