namespace Sftp.Core.Services;

/// <summary>
/// Service interface for securely retrieving sensitive configuration values from Google Cloud Secret Manager.
/// Provides secure access to secrets like database passwords, API keys, and other sensitive configuration data.
/// </summary>
/// <remarks>
/// This service abstracts access to Google Cloud Secret Manager, enabling applications to:
///
/// **Security Features:**
/// - Secure retrieval of sensitive configuration values
/// - Environment-aware secret access (only available in GCP environments)
/// - Automatic authentication using service account credentials
///
/// **Usage Patterns:**
/// - Database connection strings with embedded passwords
/// - SFTP credentials and authentication keys
/// - Third-party API keys and tokens
/// - Any sensitive configuration that shouldn't be stored in plain text
///
/// **Environment Handling:**
/// - Automatically detects GCP environment availability
/// - Gracefully handles local development scenarios
/// - Provides fallback mechanisms for non-GCP environments
///
/// Implementations should handle authentication, error scenarios, and provide
/// appropriate logging while ensuring sensitive data is never logged.
/// </remarks>
public interface ISecretManagerService
{
    /// <summary>
    /// Retrieves a secret value from GCP Secret Manager
    /// </summary>
    /// <param name="secretName">Name of the secret</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>The secret value</returns>
    Task<string> GetSecretAsync(string secretName, CancellationToken cancellationToken = default);

    /// <summary>
    /// Checks if the service is available (i.e., running in GCP environment)
    /// </summary>
    /// <returns>True if service is available, false otherwise</returns>
    bool IsAvailable();
}
