using Google.Cloud.SecretManager.V1;
using Microsoft.Extensions.Logging;
using Sftp.Core.Configuration;

namespace Sftp.Core.Services;

/// <summary>
/// Service implementation for securely retrieving sensitive configuration values from Google Cloud Secret Manager.
/// Provides secure access to secrets with automatic authentication and environment detection.
/// </summary>
/// <remarks>
/// This service provides secure secret management capabilities including:
///
/// **Security Features:**
/// - Automatic authentication using Google Cloud service account credentials
/// - Secure retrieval of sensitive configuration values
/// - Environment-aware availability detection
/// - Proper error handling without exposing sensitive information
///
/// **Supported Secret Types:**
/// - Database passwords and connection strings
/// - SFTP authentication credentials
/// - API keys and authentication tokens
/// - Private key passphrases
/// - Any other sensitive configuration data
///
/// **Environment Handling:**
/// - Automatically detects Google Cloud Platform environment
/// - Gracefully handles local development scenarios
/// - Provides clear availability status for configuration services
///
/// **Error Handling:**
/// - Comprehensive error logging for troubleshooting
/// - Secure error messages that don't expose sensitive data
/// - Proper exception handling for network and authentication issues
///
/// The service initializes the Google Cloud Secret Manager client only when
/// running in appropriate environments and handles authentication automatically.
/// </remarks>
public class SecretManagerService : ISecretManagerService
{
    private readonly ILogger<SecretManagerService> _logger;
    private readonly AppConfiguration _appConfig;
    private readonly SecretManagerServiceClient? _client;

    /// <summary>
    /// Initializes a new instance of the SecretManagerService class.
    /// Automatically initializes the Google Cloud Secret Manager client if running in a supported environment.
    /// </summary>
    /// <param name="logger">Logger instance for structured logging</param>
    /// <param name="appConfig">Application configuration containing GCP project information</param>
    public SecretManagerService(ILogger<SecretManagerService> logger, AppConfiguration appConfig)
    {
        _logger = logger;
        _appConfig = appConfig;

        if (IsAvailable())
        {
            try
            {
                _client = SecretManagerServiceClient.Create();
                _logger.LogInformation("Secret Manager client initialized successfully");
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to initialize Secret Manager client");
            }
        }
    }

    public async Task<string> GetSecretAsync(string secretName, CancellationToken cancellationToken = default)
    {
        if (_client == null)
        {
            throw new InvalidOperationException("Secret Manager client is not available");
        }

        try
        {
            _logger.LogDebug("Retrieving secret {SecretName} from GCP Secret Manager", secretName);

            var secretVersionName = new SecretVersionName(_appConfig.GcpProjectId, secretName, "latest");
            var response = await _client.AccessSecretVersionAsync(secretVersionName, cancellationToken);

            var secretValue = response.Payload.Data.ToStringUtf8();
            
            _logger.LogDebug("Successfully retrieved secret {SecretName}", secretName);
            return secretValue;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to retrieve secret {SecretName} from GCP Secret Manager", secretName);
            throw;
        }
    }

    public bool IsAvailable()
    {
        // Check if we're running in a cloud environment and have a project ID
        return _appConfig.IsCloudEnvironment && !string.IsNullOrEmpty(_appConfig.GcpProjectId);
    }
}
