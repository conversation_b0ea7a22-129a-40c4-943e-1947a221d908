namespace Sftp.Core.Services;

/// <summary>
/// High-level orchestration service interface that coordinates the complete file import workflow.
/// Manages the end-to-end process from SFTP file retrieval through database persistence.
/// </summary>
/// <typeparam name="TEntity">The entity type that will be created from the imported file data</typeparam>
/// <remarks>
/// This service acts as the main coordinator for the import process, orchestrating:
///
/// **Complete Import Workflow:**
/// 1. SFTP file download and local staging
/// 2. File format validation and parsing
/// 3. Data transformation and entity creation
/// 4. Database import with transaction management
/// 5. Import logging and audit trail creation
/// 6. Cleanup of temporary files
///
/// **Supported Import Modes:**
/// - Single file import from specific SFTP path
/// - Local file processing (for testing/manual imports)
/// - Batch processing of multiple files matching patterns
///
/// **Error Handling:**
/// - Comprehensive error logging and reporting
/// - Graceful handling of network, parsing, and database errors
/// - Rollback capabilities for failed imports
///
/// Implementations should ensure proper resource cleanup and provide detailed
/// logging for monitoring and troubleshooting import operations.
/// </remarks>
public interface IImportOrchestrationService<TEntity> where TEntity : class
{
    /// <summary>
    /// Orchestrates the complete import process: download from SFTP, process file, and import to database
    /// </summary>
    /// <param name="remoteFilePath">Path to the file on the SFTP server</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>True if import was successful, false otherwise</returns>
    Task<bool> ExecuteImportAsync(string remoteFilePath, CancellationToken cancellationToken = default);

    /// <summary>
    /// Processes a local file and imports it to the database
    /// </summary>
    /// <param name="localFilePath">Path to the local file to process</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>True if import was successful, false otherwise</returns>
    Task<bool> ProcessLocalFileAsync(string localFilePath, CancellationToken cancellationToken = default);

    /// <summary>
    /// Processes files from the configured remote directory that match the file pattern
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Number of files successfully processed</returns>
    Task<int> ProcessMatchingFilesAsync(CancellationToken cancellationToken = default);
}
