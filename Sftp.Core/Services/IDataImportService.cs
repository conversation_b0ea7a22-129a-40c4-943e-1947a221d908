using Sftp.Core.Abstractions;
using Sftp.Core.Models;

namespace Sftp.Core.Services;

/// <summary>
/// Generic service interface for importing processed data records into the database.
/// Provides database operations for entity persistence and import tracking functionality.
/// </summary>
/// <typeparam name="TEntity">The entity type to be imported into the database</typeparam>
/// <remarks>
/// This service handles the database persistence layer of the import process, including:
/// - Bulk import operations with upsert capabilities (insert new, update existing)
/// - Import logging and audit trail management
/// - Batch processing statistics and error tracking
/// - Transaction management for data consistency
///
/// Implementations should provide efficient bulk operations and proper error handling
/// to ensure data integrity during large import operations.
/// </remarks>
public interface IDataImportService<TEntity> where TEntity : class
{
    /// <summary>
    /// Imports records into the database
    /// </summary>
    /// <param name="records">Records to import</param>
    /// <param name="batchId">Unique identifier for this import batch</param>
    /// <param name="processorType">Type of processor used</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Import result with statistics</returns>
    Task<ImportResult> ImportRecordsAsync(IEnumerable<TEntity> records, string batchId, string processorType, CancellationToken cancellationToken = default);

    /// <summary>
    /// Creates an import log entry
    /// </summary>
    /// <param name="importLog">Import log to create</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Created import log with ID</returns>
    Task<ImportLog> CreateImportLogAsync(ImportLog importLog, CancellationToken cancellationToken = default);

    /// <summary>
    /// Updates an existing import log entry
    /// </summary>
    /// <param name="importLog">Import log to update</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Updated import log</returns>
    Task<ImportLog> UpdateImportLogAsync(ImportLog importLog, CancellationToken cancellationToken = default);
}
