namespace Sftp.Core.Abstractions;

/// <summary>
/// Generic interface for processing different file formats
/// </summary>
/// <typeparam name="TEntity">The entity type that this processor creates</typeparam>
public interface IFileProcessor<TEntity> where TEntity : class
{
    /// <summary>
    /// Gets the processor type identifier
    /// </summary>
    string ProcessorType { get; }

    /// <summary>
    /// Gets the expected file patterns this processor can handle
    /// </summary>
    string[] SupportedFilePatterns { get; }

    /// <summary>
    /// Processes a file and returns parsed records
    /// </summary>
    /// <param name="filePath">Path to the file to process</param>
    /// <param name="batchId">Unique identifier for this import batch</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Collection of parsed records</returns>
    Task<IEnumerable<TEntity>> ProcessFileAsync(string filePath, string batchId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Validates the file format and structure
    /// </summary>
    /// <param name="filePath">Path to the file to validate</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>True if file is valid, false otherwise</returns>
    Task<bool> ValidateFileAsync(string filePath, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets the expected column headers for the file (if applicable)
    /// </summary>
    /// <returns>Array of expected column headers, or empty array if not applicable</returns>
    string[] GetExpectedHeaders();

    /// <summary>
    /// Determines if this processor can handle the given file
    /// </summary>
    /// <param name="fileName">Name of the file</param>
    /// <returns>True if this processor can handle the file, false otherwise</returns>
    bool CanProcess(string fileName);
}

/// <summary>
/// Result of a file processing operation
/// </summary>
public class ProcessingResult<TEntity> where TEntity : class
{
    public IEnumerable<TEntity> Records { get; set; } = Enumerable.Empty<TEntity>();
    public int RecordsProcessed { get; set; }
    public int RecordsValid { get; set; }
    public int RecordsInvalid { get; set; }
    public List<string> Errors { get; set; } = new();
    public bool IsSuccess => RecordsInvalid == 0 && Errors.Count == 0;
}

/// <summary>
/// Import result with statistics
/// </summary>
public class ImportResult
{
    public int RecordsProcessed { get; set; }
    public int RecordsInserted { get; set; }
    public int RecordsUpdated { get; set; }
    public int RecordsDeleted { get; set; }
    public int RecordsFailed { get; set; }
    public List<string> Errors { get; set; } = new();
    public bool IsSuccess => RecordsFailed == 0 && Errors.Count == 0;
}
