using Microsoft.Extensions.Logging;

namespace Sftp.Core.Abstractions;

/// <summary>
/// Abstract base class providing common functionality for file processors.
/// Implements standard file processing patterns and utility methods for parsing various data types.
/// </summary>
/// <typeparam name="TEntity">The entity type that this processor creates from file data</typeparam>
/// <remarks>
/// This base class provides a foundation for implementing file processors with:
///
/// **Common Functionality:**
/// - File pattern matching and processor selection logic
/// - Basic file validation (existence, non-empty content)
/// - Logging infrastructure with structured logging support
/// - Standard error handling patterns
///
/// **Utility Methods:**
/// - String parsing with null/empty handling
/// - Integer parsing with validation
/// - Date parsing supporting multiple formats
/// - File pattern matching with wildcard support
///
/// **Extensibility Points:**
/// - Abstract methods for processor-specific logic
/// - Virtual methods for customizable validation
/// - Protected helper methods for common parsing scenarios
///
/// **Implementation Guidelines:**
/// - Override ProcessFileAsync for file-specific parsing logic
/// - Override ValidateFileContentAsync for custom validation rules
/// - Use provided parsing helpers for consistent data handling
/// - Leverage the Logger for structured logging throughout processing
///
/// Derived classes should focus on file format-specific logic while leveraging
/// the common infrastructure provided by this base class.
/// </remarks>
public abstract class BaseFileProcessor<TEntity> : IFileProcessor<TEntity> where TEntity : class
{
    protected readonly ILogger Logger;

    protected BaseFileProcessor(ILogger logger)
    {
        Logger = logger;
    }

    public abstract string ProcessorType { get; }
    public abstract string[] SupportedFilePatterns { get; }

    public virtual bool CanProcess(string fileName)
    {
        return SupportedFilePatterns.Any(pattern => 
            IsFileMatch(fileName, pattern));
    }

    public abstract Task<IEnumerable<TEntity>> ProcessFileAsync(string filePath, string batchId, CancellationToken cancellationToken = default);

    public virtual async Task<bool> ValidateFileAsync(string filePath, CancellationToken cancellationToken = default)
    {
        try
        {
            Logger.LogDebug("Validating file: {FilePath}", filePath);

            if (!File.Exists(filePath))
            {
                Logger.LogWarning("File does not exist: {FilePath}", filePath);
                return false;
            }

            // Basic validation - can be overridden by specific processors
            using var reader = new StreamReader(filePath);
            var firstLine = await reader.ReadLineAsync(cancellationToken);

            if (firstLine == null)
            {
                Logger.LogWarning("File is empty: {FilePath}", filePath);
                return false;
            }

            return await ValidateFileContentAsync(filePath, firstLine, cancellationToken);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error validating file: {FilePath}", filePath);
            return false;
        }
    }

    /// <summary>
    /// Override this method to provide specific file content validation
    /// </summary>
    protected virtual Task<bool> ValidateFileContentAsync(string filePath, string firstLine, CancellationToken cancellationToken)
    {
        return Task.FromResult(true);
    }

    public virtual string[] GetExpectedHeaders()
    {
        return Array.Empty<string>();
    }

    /// <summary>
    /// Helper method for file pattern matching with proper wildcard support
    /// </summary>
    protected static bool IsFileMatch(string fileName, string pattern)
    {
        if (pattern == "*" || pattern == "*.*")
            return true;

        if (pattern.StartsWith("*."))
        {
            var extension = pattern[2..];
            return fileName.EndsWith($".{extension}", StringComparison.OrdinalIgnoreCase);
        }

        // Convert pattern to regex for proper wildcard matching
        var regexPattern = "^" + System.Text.RegularExpressions.Regex.Escape(pattern)
            .Replace("\\*", ".*") + "$";

        return System.Text.RegularExpressions.Regex.IsMatch(fileName, regexPattern,
            System.Text.RegularExpressions.RegexOptions.IgnoreCase);
    }

    /// <summary>
    /// Helper method for parsing strings
    /// </summary>
    protected static string? ParseString(string value)
    {
        var trimmed = value?.Trim();
        return string.IsNullOrEmpty(trimmed) ? null : trimmed;
    }

    /// <summary>
    /// Helper method for parsing integers
    /// </summary>
    protected static int? ParseInt(string value)
    {
        var trimmed = value?.Trim();
        return string.IsNullOrEmpty(trimmed) ? null : int.TryParse(trimmed, out var result) ? result : null;
    }

    /// <summary>
    /// Helper method for parsing dates
    /// </summary>
    protected static DateOnly? ParseDateOnly(string value)
    {
        var trimmed = value?.Trim();
        if (string.IsNullOrEmpty(trimmed))
            return null;

        // Try different date formats
        var formats = new[] { "yyyy-MM-dd", "MM/dd/yyyy", "dd/MM/yyyy", "yyyy/MM/dd" };
        
        foreach (var format in formats)
        {
            if (DateTime.TryParseExact(trimmed, format, System.Globalization.CultureInfo.InvariantCulture, 
                System.Globalization.DateTimeStyles.None, out var date))
            {
                return DateOnly.FromDateTime(date);
            }
        }

        // Try general parsing as fallback
        if (DateTime.TryParse(trimmed, out var generalDate))
        {
            return DateOnly.FromDateTime(generalDate);
        }

        return null;
    }
}
