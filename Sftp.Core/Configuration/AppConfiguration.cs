namespace Sftp.Core.Configuration;

/// <summary>
/// Root configuration class containing all application settings.
/// Provides centralized configuration management with environment-aware settings.
/// </summary>
/// <remarks>
/// This configuration class serves as the main container for all application settings,
/// organizing them into logical sections for different aspects of the application:
/// - Database connection settings
/// - SFTP server configuration
/// - File processing parameters
/// - Environment-specific behaviors
///
/// The configuration supports both local development and cloud deployment scenarios,
/// with automatic environment detection and appropriate secret loading strategies.
/// </remarks>
public class AppConfiguration
{
    /// <summary>
    /// Configuration section name used in appsettings.json and other configuration sources.
    /// </summary>
    public const string SectionName = "AppConfiguration";

    /// <summary>
    /// Gets or sets the current environment (Development, Production, Cloud).
    /// Used to determine configuration loading strategies and feature availability.
    /// </summary>
    public string Environment { get; set; } = "Development";

    /// <summary>
    /// Gets or sets the Google Cloud Platform project ID.
    /// Required for cloud deployments and secret manager access.
    /// </summary>
    public string GcpProjectId { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the database configuration settings.
    /// Contains connection parameters and authentication details.
    /// </summary>
    public DatabaseConfiguration Database { get; set; } = new();

    /// <summary>
    /// Gets or sets the SFTP server configuration settings.
    /// Contains connection parameters, authentication, and timeout settings.
    /// </summary>
    public SftpConfiguration Sftp { get; set; } = new();

    /// <summary>
    /// Gets or sets the file processing configuration settings.
    /// Contains batch sizes, file patterns, and processing behavior parameters.
    /// </summary>
    public ProcessingConfiguration Processing { get; set; } = new();

    /// <summary>
    /// Gets a value indicating whether the application is running in a cloud environment.
    /// Used to determine whether to use cloud-specific features like secret manager.
    /// </summary>
    public bool IsCloudEnvironment => Environment.Equals("Production", StringComparison.OrdinalIgnoreCase) ||
                                     Environment.Equals("Cloud", StringComparison.OrdinalIgnoreCase);
}

/// <summary>
/// Configuration class for database connection settings.
/// Supports both connection string and individual parameter approaches.
/// </summary>
/// <remarks>
/// This configuration class provides flexible database connection options:
/// - Direct connection string for simple scenarios
/// - Individual parameters for programmatic construction
/// - Automatic preference for connection string when available
///
/// Sensitive values like passwords should be loaded from secure sources
/// such as environment variables or secret management systems.
/// </remarks>
public class DatabaseConfiguration
{
    /// <summary>
    /// Gets or sets the complete database connection string.
    /// When provided, takes precedence over individual connection parameters.
    /// </summary>
    public string ConnectionString { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the database server hostname or IP address.
    /// Used when constructing connection string from individual parameters.
    /// </summary>
    public string Host { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the database server port number.
    /// Defaults to 5432 (standard PostgreSQL port).
    /// </summary>
    public int Port { get; set; } = 5432;

    /// <summary>
    /// Gets or sets the database name to connect to.
    /// Used when constructing connection string from individual parameters.
    /// </summary>
    public string Database { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the database username for authentication.
    /// Used when constructing connection string from individual parameters.
    /// </summary>
    public string Username { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the database password for authentication.
    /// Should be loaded from secure sources in production environments.
    /// </summary>
    public string Password { get; set; } = string.Empty;

    /// <summary>
    /// Gets a value indicating whether to use the connection string directly
    /// rather than constructing it from individual parameters.
    /// </summary>
    public bool UseConnectionString => !string.IsNullOrEmpty(ConnectionString);
}

/// <summary>
/// Configuration class for file processing behavior and performance settings.
/// Controls how files are processed, where temporary files are stored, and batch processing parameters.
/// </summary>
/// <remarks>
/// This configuration class manages the operational aspects of file processing:
/// - Temporary file management and cleanup policies
/// - Batch processing sizes for optimal performance
/// - File pattern matching for automated processing
/// - Remote directory specifications for SFTP operations
///
/// Settings should be tuned based on:
/// - Available system resources (memory, disk space)
/// - Network bandwidth and latency
/// - Database performance characteristics
/// - File sizes and processing complexity
/// </remarks>
public class ProcessingConfiguration
{
    /// <summary>
    /// Gets or sets the directory path for storing temporary files during processing.
    /// Defaults to the system temporary directory.
    /// </summary>
    /// <remarks>
    /// This directory should have sufficient space for downloaded files and should be
    /// accessible with appropriate read/write permissions. In cloud environments,
    /// consider using ephemeral storage or mounted volumes.
    /// </remarks>
    public string TempDirectory { get; set; } = Path.GetTempPath();

    /// <summary>
    /// Gets or sets the number of records to process in each database batch operation.
    /// Defaults to 1000 records per batch.
    /// </summary>
    /// <remarks>
    /// Larger batch sizes can improve performance but consume more memory.
    /// Smaller batch sizes provide better error isolation and memory efficiency.
    /// Optimal size depends on record complexity and available system resources.
    /// </remarks>
    public int BatchSize { get; set; } = 1000;

    /// <summary>
    /// Gets or sets a value indicating whether temporary files should be deleted after processing.
    /// Defaults to true for automatic cleanup.
    /// </summary>
    /// <remarks>
    /// Set to false for debugging purposes or when temporary files need to be retained
    /// for audit or troubleshooting purposes. Ensure adequate disk space when disabled.
    /// </remarks>
    public bool DeleteTempFilesAfterProcessing { get; set; } = true;

    /// <summary>
    /// Gets or sets the file pattern used to identify files for processing.
    /// Supports wildcard patterns. Defaults to "*.txt".
    /// </summary>
    /// <remarks>
    /// Examples of valid patterns:
    /// - "*.txt" - All text files
    /// - "BrightStar_*.txt" - Files starting with "BrightStar_"
    /// - "*_Center_*.txt" - Files containing "_Center_"
    /// </remarks>
    public string FilePattern { get; set; } = "*.txt";

    /// <summary>
    /// Gets or sets the remote directory path on the SFTP server to search for files.
    /// Defaults to the root directory "/".
    /// </summary>
    /// <remarks>
    /// Should be an absolute path on the SFTP server. The application must have
    /// appropriate permissions to list and read files from this directory.
    /// </remarks>
    public string RemoteDirectory { get; set; } = "/";

    /// <summary>
    /// Gets or sets a value indicating whether to process only files with the current date.
    /// When true, filters files to only process those containing today's date in YYYYMMDD format.
    /// When false, processes all matching files. Defaults to true for Cloud Run environments.
    /// </summary>
    /// <remarks>
    /// This setting is particularly useful for scheduled Cloud Run jobs that should only
    /// process the most recent data file. In local development, you may want to set this
    /// to false to process all available files for testing purposes.
    ///
    /// The date extraction looks for 8-digit patterns (YYYYMMDD) in the filename and
    /// compares them to the current UTC date.
    /// </remarks>
    public bool ProcessCurrentDateOnly { get; set; } = true;
}

/// <summary>
/// Configuration class for SFTP server connection settings and authentication.
/// Supports both password and private key authentication methods.
/// </summary>
/// <remarks>
/// This configuration class manages SFTP connection parameters including:
/// - Server connection details (host, port)
/// - Authentication methods (password or private key)
/// - Timeout settings for connection and operations
/// - Security considerations for credential management
///
/// **Authentication Methods:**
/// - Password authentication: Simple username/password combination
/// - Private key authentication: SSH key-based authentication with optional passphrase
///
/// **Security Best Practices:**
/// - Store passwords and passphrases in secure secret management systems
/// - Use private key authentication when possible for enhanced security
/// - Ensure private key files have appropriate file system permissions
/// - Configure appropriate timeout values to balance security and usability
/// </remarks>
public class SftpConfiguration
{
    /// <summary>
    /// Gets or sets the SFTP server hostname or IP address.
    /// </summary>
    public string Host { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the SFTP server port number.
    /// Defaults to 22 (standard SSH/SFTP port).
    /// </summary>
    public int Port { get; set; } = 22;

    /// <summary>
    /// Gets or sets the username for SFTP authentication.
    /// Required for both password and private key authentication.
    /// </summary>
    public string Username { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the password for SFTP authentication.
    /// Used when private key authentication is not configured.
    /// Should be loaded from secure sources in production.
    /// </summary>
    public string Password { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the file path to the SSH private key for authentication.
    /// When specified, private key authentication is used instead of password.
    /// </summary>
    public string? PrivateKeyPath { get; set; }

    /// <summary>
    /// Gets or sets the passphrase for the SSH private key.
    /// Required only if the private key is encrypted with a passphrase.
    /// </summary>
    public string? PrivateKeyPassphrase { get; set; }

    /// <summary>
    /// Gets or sets the connection timeout in seconds.
    /// Defaults to 30 seconds. Controls how long to wait for initial connection.
    /// </summary>
    public int ConnectionTimeoutSeconds { get; set; } = 30;

    /// <summary>
    /// Gets or sets the operation timeout in seconds.
    /// Defaults to 300 seconds (5 minutes). Controls timeout for file operations.
    /// </summary>
    public int OperationTimeoutSeconds { get; set; } = 300;

    /// <summary>
    /// Gets a value indicating whether private key authentication should be used.
    /// Returns true if a private key path is configured.
    /// </summary>
    public bool UsePrivateKey => !string.IsNullOrEmpty(PrivateKeyPath);
}
