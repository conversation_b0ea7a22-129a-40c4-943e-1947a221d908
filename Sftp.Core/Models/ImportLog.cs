using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Sftp.Core.Models;

/// <summary>
/// Entity model representing an import operation log entry.
/// Tracks the complete lifecycle of file import operations including statistics, timing, and error information.
/// </summary>
/// <remarks>
/// This entity provides comprehensive audit trail and monitoring capabilities for import operations:
///
/// **Tracking Information:**
/// - Unique batch identification for grouping related operations
/// - File metadata (name, path, size) for traceability
/// - Processing statistics (records processed, inserted, updated, failed)
/// - Timing information (start time, end time, duration)
/// - Status tracking throughout the import lifecycle
///
/// **Monitoring and Alerting:**
/// - Error message capture for troubleshooting
/// - Processor type identification for multi-processor environments
/// - Status-based monitoring for operational dashboards
/// - Performance metrics for optimization analysis
///
/// **Database Mapping:**
/// - Maps to the "import_logs" table in the database
/// - Includes appropriate column mappings and constraints
/// - Supports efficient querying with proper indexing considerations
///
/// This model is used across all import processors to provide consistent
/// logging and monitoring capabilities throughout the application.
/// </remarks>
[Table("import_logs")]
public class ImportLog
{
    /// <summary>
    /// Gets or sets the unique identifier for this import log entry.
    /// Primary key for the database table.
    /// </summary>
    [Key]
    [Column("id")]
    public int Id { get; set; }

    /// <summary>
    /// Gets or sets the unique batch identifier for this import operation.
    /// Used to group related import operations and track batch processing.
    /// </summary>
    [Column("batch_id")]
    [MaxLength(100)]
    public string BatchId { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the name of the file being imported.
    /// Includes the file name without the full path for easy identification.
    /// </summary>
    [Column("file_name")]
    [MaxLength(500)]
    public string FileName { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the full path to the file being imported.
    /// May be local path or remote SFTP path depending on the import source.
    /// </summary>
    [Column("file_path")]
    [MaxLength(1000)]
    public string? FilePath { get; set; }

    /// <summary>
    /// Gets or sets the size of the imported file in bytes.
    /// Used for performance analysis and storage planning.
    /// </summary>
    [Column("file_size")]
    public long? FileSize { get; set; }

    /// <summary>
    /// Gets or sets the total number of records processed from the file.
    /// Includes both successful and failed record processing attempts.
    /// </summary>
    [Column("records_processed")]
    public int RecordsProcessed { get; set; }

    /// <summary>
    /// Gets or sets the number of records successfully inserted into the database.
    /// Represents new records that were added during this import operation.
    /// </summary>
    [Column("records_inserted")]
    public int RecordsInserted { get; set; }

    /// <summary>
    /// Gets or sets the number of existing records that were updated.
    /// Represents records that already existed and were modified during import.
    /// </summary>
    [Column("records_updated")]
    public int RecordsUpdated { get; set; }

    /// <summary>
    /// Gets or sets the number of records that failed to process.
    /// Indicates data quality issues or processing errors that need attention.
    /// </summary>
    [Column("records_failed")]
    public int RecordsFailed { get; set; }

    /// <summary>
    /// Gets or sets the timestamp when the import operation started.
    /// Used for performance analysis and operation tracking.
    /// </summary>
    [Column("start_time")]
    public DateTime StartTime { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// Gets or sets the timestamp when the import operation completed.
    /// Null if the operation is still in progress or was interrupted.
    /// </summary>
    [Column("end_time")]
    public DateTime? EndTime { get; set; }

    /// <summary>
    /// Gets or sets the current status of the import operation.
    /// Common values: "Processing", "Completed", "Failed", "Cancelled".
    /// </summary>
    [Column("status")]
    [MaxLength(50)]
    public string Status { get; set; } = "Processing";

    /// <summary>
    /// Gets or sets the error message if the import operation failed.
    /// Contains detailed error information for troubleshooting purposes.
    /// </summary>
    [Column("error_message")]
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// Gets or sets the timestamp when this log entry was created.
    /// Automatically set to the current UTC time when the entity is created.
    /// </summary>
    [Column("created_at")]
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// Gets or sets the type of processor that handled this import.
    /// Used to identify which specific processor implementation was used.
    /// </summary>
    [Column("processor_type")]
    [MaxLength(100)]
    public string? ProcessorType { get; set; }

    /// <summary>
    /// Gets the duration of the import operation.
    /// Calculated as the difference between EndTime and StartTime.
    /// Returns null if the operation hasn't completed yet.
    /// </summary>
    public TimeSpan? Duration => EndTime.HasValue ? EndTime.Value - StartTime : null;
}
