# Instructions to Process Test Messages from Azure Service Bus Topic

## 📋 Prerequisites

You'll need:
- ✅ Topic name
- ✅ Subscription name  
- ✅ Connection string for Azure Service Bus

You'll also need:
- PostgreSQL database (for storing processed messages)
- .NET 9 SDK installed

## 🔧 Step 1: Configure the Application

### 1.1 Update Configuration File

Edit the `Messaging.AzureServiceBus/appsettings.json` file with your Azure Service Bus details:

Replace these values in the AzureServiceBus section:
```json
"AzureServiceBus": {
  "ConnectionString": "YOUR_ACTUAL_CONNECTION_STRING_HERE",
  "QueueName": "",
  "TopicName": "YOUR_TOPIC_NAME_HERE",
  "SubscriptionName": "YOUR_SUBSCRIPTION_NAME_HERE",
```

### 1.2 Update Database Configuration

Update the database settings in the same file:
```json
"Database": {
  "ConnectionString": "",
  "Host": "localhost",
  "Port": 5432,
  "Database": "messaging_db",
  "Username": "postgres",
  "Password": "YOUR_POSTGRES_PASSWORD_HERE"
},
```

## 🗄️ Step 2: Set Up PostgreSQL Database

### 2.1 Install PostgreSQL (if not already installed)

**On macOS:**
```bash
brew install postgresql
brew services start postgresql
```

**On Ubuntu/Debian:**
```bash
sudo apt update
sudo apt install postgresql postgresql-contrib
sudo systemctl start postgresql
```

**On Windows:**
Download and install from PostgreSQL official website

### 2.2 Create Database and User

```bash
# Connect to PostgreSQL
psql -U postgres

# Create database
CREATE DATABASE messaging_db;

# Create user (optional, or use existing postgres user)
CREATE USER messaging_user WITH PASSWORD 'your_password_here';
GRANT ALL PRIVILEGES ON DATABASE messaging_db TO messaging_user;

# Exit
\q
```

## 📨 Step 3: Send a Test Message to Your Topic

### 3.1 Using Azure CLI

```bash
# Install Azure CLI if not already installed
# Login to Azure
az login

# Send a test message
az servicebus topic message send \
  --resource-group YOUR_RESOURCE_GROUP \
  --namespace-name YOUR_NAMESPACE_NAME \
  --topic-name YOUR_TOPIC_NAME \
  --body '{"customerId": "CUST001", "orderId": "ORD123", "eventType": "OrderCreated", "eventData": {"amount": 99.99, "currency": "USD"}, "sourceSystem": "ECommerce", "version": "1.0"}'
```

### 3.2 Using Azure Portal

1. Go to Azure Portal → Your Service Bus Namespace
2. Navigate to your Topic
3. Click "Service Bus Explorer"
4. Click "Send messages"
5. Add this sample JSON message:

```json
{
  "customerId": "CUST001",
  "orderId": "ORD123", 
  "eventType": "OrderCreated",
  "eventData": {
    "amount": 99.99,
    "currency": "USD",
    "items": [
      {"id": "ITEM001", "name": "Product A", "quantity": 2}
    ]
  },
  "sourceSystem": "ECommerce",
  "version": "1.0"
}
```

## 🚀 Step 4: Run the Message Processor

### 4.1 Navigate to the Project Directory

```bash
cd Messaging.AzureServiceBus
```

### 4.2 Build the Project

```bash
dotnet build
```

### 4.3 Run the Application

**Option A: One-time Processing (Recommended for Testing)**
```bash
dotnet run -- --once
```

**Option B: Continuous Processing**
```bash
dotnet run
```

**Option C: With Specific Configuration**
```bash
dotnet run --environment Development -- --once
```

## 📊 Step 5: Monitor the Processing

### 5.1 Check Console Output

You should see logs similar to:
```
info: Starting Azure Service Bus Worker Service
info: Created receiver for topic: YOUR_TOPIC_NAME, subscription: YOUR_SUBSCRIPTION_NAME
info: Starting Azure Service Bus message processing for batch: [BATCH_ID]
info: Received 1 messages from Azure Service Bus
info: Processing 1 Azure Service Bus messages
info: Importing 1 Azure message entities
```

### 5.2 Check Database

Connect to your PostgreSQL database and verify the data:

```sql
-- Connect to database
psql -U postgres -d messaging_db

-- Check processed messages
SELECT * FROM azure_messages ORDER BY created_at DESC LIMIT 5;

-- Check import logs
SELECT * FROM message_logs ORDER BY created_at DESC LIMIT 5;

-- Check specific message details
SELECT 
    message_id,
    topic_name,
    subscription_name,
    customer_id,
    order_id,
    event_type,
    processed_at
FROM azure_messages 
WHERE topic_name = 'YOUR_TOPIC_NAME';
```

## 🔧 Step 6: Troubleshooting

### 6.1 Common Issues and Solutions

**Issue: Connection String Error**
```
Error: "The connection string is invalid"
```
**Solution:** Verify your connection string format:
```
Endpoint=sb://YOUR-NAMESPACE.servicebus.windows.net/;SharedAccessKeyName=RootManageSharedAccessKey;SharedAccessKey=YOUR-KEY
```

**Issue: Topic/Subscription Not Found**
```
Error: "The messaging entity could not be found"
```
**Solution:**
- Verify topic and subscription names are correct
- Ensure the subscription exists in Azure Portal
- Check that your connection string has the right permissions

**Issue: Database Connection Error**
```
Error: "Connection to database failed"
```
**Solution:**
- Ensure PostgreSQL is running: `brew services start postgresql` (macOS)
- Verify database credentials in appsettings.json
- Test connection: `psql -U postgres -d messaging_db`

**Issue: No Messages Received**
```
Info: "No messages received from Azure Service Bus"
```
**Solution:**
- Verify messages exist in the subscription (check Azure Portal)
- Ensure subscription is not filtered out by subscription rules
- Check message lock duration hasn't expired

### 6.2 Enable Debug Logging

Update `appsettings.json` for more detailed logs:

```json
{
  "Logging": {
    "LogLevel": {
      "Default": "Debug",
      "Microsoft.EntityFrameworkCore": "Information",
      "Messaging.AzureServiceBus": "Debug"
    }
  }
}
```

### 6.3 Health Check

The application includes health checks. You can verify connectivity:

```bash
# The health check runs automatically when the service starts
# Look for logs like:
# "Azure Service Bus health check passed"
```

### 6.4 Multiple Topics Troubleshooting

**Issue: Messages Not Routing Correctly**
```
Warning: "Message did not match any routing rules"
```
**Solution:**
- Enable routing decision logging: `"LogRoutingDecisions": true`
- Check routing rule conditions and field names
- Verify message properties contain expected values
- Test routing rules with sample messages

**Issue: Subscription Filter Errors**
```
Error: "Invalid filter expression"
```
**Solution:**
- Validate SQL filter syntax in Azure Portal
- Check property names match message attributes
- Ensure correlation filter properties exist
- Test filters independently before deployment

**Issue: Multiple Topic Performance Issues**
```
Warning: "High message processing latency"
```
**Solution:**
- Adjust `MaxConcurrentMessages` per subscription
- Monitor CPU and memory usage
- Consider splitting high-volume topics
- Optimize message processing logic

**Issue: Dead Letter Queue Accumulation**
```
Warning: "Messages accumulating in dead letter queue"
```
**Solution:**
- Check dead letter topic configuration
- Review `MaxDeliveryAttempts` settings
- Investigate message processing failures
- Implement dead letter message analysis

### 6.5 Monitoring Multiple Topics

**Enable Detailed Logging:**
```json
{
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Messaging.Core.Services.MessageRouter": "Debug",
      "Messaging.AzureServiceBus.Services.AzureTopicSubscriptionManager": "Debug",
      "Messaging.AzureServiceBus.Services.AzureServiceBusService": "Debug"
    }
  }
}
```

**Monitor Topic-Specific Metrics:**
```sql
-- Check messages by topic
SELECT
    topic_name,
    COUNT(*) as message_count,
    AVG(EXTRACT(EPOCH FROM (processed_at - created_at))) as avg_processing_time_seconds
FROM azure_messages
WHERE created_at >= NOW() - INTERVAL '1 hour'
GROUP BY topic_name;

-- Check routing decisions
SELECT
    topic_name,
    subscription_name,
    properties->>'RoutedProcessor' as routed_processor,
    COUNT(*) as count
FROM azure_messages
WHERE properties ? 'RoutedProcessor'
GROUP BY topic_name, subscription_name, properties->>'RoutedProcessor';

-- Monitor failed messages
SELECT
    topic_name,
    error_message,
    COUNT(*) as error_count
FROM message_logs
WHERE status = 'Failed'
  AND created_at >= NOW() - INTERVAL '1 hour'
GROUP BY topic_name, error_message;
```

## 📈 Step 7: Advanced Configuration

### 7.1 Multiple Topics with Advanced Features

The Azure Service Bus processor now supports advanced topic-based subscriptions with powerful routing and filtering capabilities.

#### 7.1.1 Basic Multiple Topics Configuration

```json
{
  "AzureServiceBus": {
    "EnableMultipleTopics": true,
    "TopicSubscriptions": [
      {
        "SubscriptionName": "orders-subscription",
        "TopicName": "orders-topic",
        "MaxConcurrentMessages": 5,
        "AckDeadlineSeconds": 300,
        "MaxDeliveryAttempts": 3,
        "DeadLetterTopic": "orders-deadletter"
      },
      {
        "SubscriptionName": "notifications-subscription",
        "TopicName": "notifications-topic",
        "MaxConcurrentMessages": 10,
        "AckDeadlineSeconds": 600,
        "MaxDeliveryAttempts": 5
      }
    ]
  }
}
```

#### 7.1.2 Advanced Configuration with Filters and Routing

```json
{
  "AzureServiceBus": {
    "EnableMultipleTopics": true,
    "TopicSubscriptions": [
      {
        "SubscriptionName": "premium-orders-subscription",
        "TopicName": "orders-topic",
        "MaxConcurrentMessages": 5,
        "AckDeadlineSeconds": 300,
        "MaxDeliveryAttempts": 3,
        "DeadLetterTopic": "orders-deadletter",
        "EnableMessageOrdering": false,
        "Filters": [
          {
            "Name": "PremiumOrderFilter",
            "FilterType": "sql",
            "Parameters": {
              "Expression": "orderType = 'Premium' AND amount > 100"
            },
            "Enabled": true
          },
          {
            "Name": "RegionFilter",
            "FilterType": "correlation",
            "Parameters": {
              "Property.Region": "US",
              "Property.Priority": "High"
            },
            "Enabled": true
          }
        ]
      },
      {
        "SubscriptionName": "urgent-notifications",
        "TopicName": "notifications-topic",
        "MaxConcurrentMessages": 15,
        "AckDeadlineSeconds": 180,
        "Filters": [
          {
            "Name": "UrgentFilter",
            "FilterType": "sql",
            "Parameters": {
              "Expression": "priority = 'URGENT' OR severity = 'CRITICAL'"
            },
            "Enabled": true
          }
        ]
      }
    ],
    "Routing": {
      "EnableRouting": true,
      "LogRoutingDecisions": true,
      "DefaultProcessorType": "GenericProcessor",
      "Rules": [
        {
          "Name": "OrdersToOrderProcessor",
          "Description": "Route order messages to specialized order processor",
          "Priority": 100,
          "Enabled": true,
          "Conditions": [
            {
              "Field": "TopicName",
              "Operator": "Equals",
              "Value": "orders-topic"
            }
          ],
          "Action": {
            "ActionType": "RouteToProcessor",
            "ProcessorType": "OrderProcessor",
            "StopProcessing": true
          }
        },
        {
          "Name": "HighValueOrdersToSpecialProcessor",
          "Description": "Route high-value orders to special handling",
          "Priority": 200,
          "Enabled": true,
          "Conditions": [
            {
              "Field": "TopicName",
              "Operator": "Equals",
              "Value": "orders-topic"
            },
            {
              "Field": "Properties.amount",
              "Operator": "GreaterThan",
              "Value": "1000"
            }
          ],
          "Action": {
            "ActionType": "RouteToProcessor",
            "ProcessorType": "HighValueOrderProcessor",
            "StopProcessing": true
          }
        },
        {
          "Name": "NotificationsToNotificationProcessor",
          "Description": "Route notification messages",
          "Priority": 50,
          "Enabled": true,
          "Conditions": [
            {
              "Field": "TopicName",
              "Operator": "Equals",
              "Value": "notifications-topic"
            }
          ],
          "Action": {
            "ActionType": "RouteToProcessor",
            "ProcessorType": "NotificationProcessor",
            "StopProcessing": true
          }
        }
      ]
    }
  }
}
```

#### 7.1.3 Filter Types and Examples

**SQL Filters:**
```json
{
  "Name": "ComplexSQLFilter",
  "FilterType": "sql",
  "Parameters": {
    "Expression": "orderType = 'Premium' AND (amount > 100 OR priority = 'High') AND region IN ('US', 'CA')"
  }
}
```

**Correlation Filters:**
```json
{
  "Name": "CorrelationFilter",
  "FilterType": "correlation",
  "Parameters": {
    "CorrelationId": "ORDER-123",
    "MessageId": "MSG-456",
    "Subject": "OrderCreated",
    "SessionId": "SESSION-789",
    "Property.CustomerId": "CUST001",
    "Property.Region": "US"
  }
}
```

#### 7.1.4 Routing Operators

The message router supports these operators for flexible message routing:

| Operator | Description | Example |
|----------|-------------|---------|
| `Equals` | Exact match | `"Field": "MessageType", "Value": "Order"` |
| `Contains` | Contains substring | `"Field": "Body", "Value": "urgent"` |
| `StartsWith` | Starts with prefix | `"Field": "RoutingKey", "Value": "order."` |
| `EndsWith` | Ends with suffix | `"Field": "MessageType", "Value": ".created"` |
| `Regex` | Regular expression | `"Field": "Body", "Value": "\\d{4}-\\d{2}-\\d{2}"` |
| `In` | Value in list | `"Field": "Priority", "Value": "high,critical"` |
| `GreaterThan` | Numeric comparison | `"Field": "Priority", "Value": "5"` |
| `Exists` | Field exists | `"Field": "CustomerId"` |

#### 7.1.5 Benefits of Multiple Topics

**Consolidated Processing:**
- Single service handles multiple message types
- Reduced infrastructure complexity
- Centralized monitoring and logging

**Topic-Specific Configuration:**
- Different retry policies per topic
- Custom dead letter handling
- Optimized concurrency settings

**Advanced Message Routing:**
- Route messages to different processors based on content
- Apply business rules for message handling
- Support complex filtering scenarios

**Example Use Cases:**

1. **E-commerce Platform:**
   - `orders-topic` → Order processing with high reliability
   - `inventory-topic` → Inventory updates with high throughput
   - `notifications-topic` → Customer notifications with retry logic

2. **IoT Data Processing:**
   - `sensor-data` → Critical alerts with immediate processing
   - `device-telemetry` → Bulk analytics processing
   - `device-status` → Device health monitoring

3. **Financial Services:**
   - `transactions-topic` → Transaction processing with strict ordering
   - `fraud-alerts` → Real-time fraud detection
   - `compliance-reports` → Regulatory reporting with audit trails

### 7.2 Performance Tuning

Adjust these settings based on your needs:

```json
{
  "AzureServiceBus": {
    "MaxConcurrentMessages": 20,
    "MessageLockDurationMinutes": 10,
    "MaxRetryAttempts": 5
  },
  "Processing": {
    "BatchSize": 50,
    "MaxConcurrentBatches": 3
  }
}
```

## 🧪 Step 8: Testing Multiple Topics (Optional)

If you've configured multiple topics, test each subscription independently:

### 8.1 Send Test Messages to Different Topics

**Orders Topic Test Message:**
```json
{
  "customerId": "CUST001",
  "orderId": "ORD123",
  "orderType": "Premium",
  "amount": 299.99,
  "currency": "USD",
  "region": "US",
  "priority": "High",
  "eventType": "OrderCreated",
  "eventData": {
    "items": [
      {"id": "ITEM001", "name": "Premium Product", "quantity": 1}
    ]
  },
  "sourceSystem": "ECommerce",
  "timestamp": "2024-01-15T10:30:00Z"
}
```

**Notifications Topic Test Message:**
```json
{
  "notificationId": "NOTIF456",
  "customerId": "CUST001",
  "type": "OrderConfirmation",
  "priority": "URGENT",
  "severity": "NORMAL",
  "channel": "email",
  "eventType": "NotificationTriggered",
  "eventData": {
    "subject": "Order Confirmation",
    "template": "order-confirmation",
    "variables": {
      "orderNumber": "ORD123",
      "customerName": "John Doe"
    }
  },
  "sourceSystem": "NotificationService",
  "timestamp": "2024-01-15T10:31:00Z"
}
```

### 8.2 Verify Topic-Specific Processing

**Check Topic Routing:**
```sql
-- Verify messages are tagged with correct topic/subscription
SELECT
    message_id,
    topic_name,
    subscription_name,
    properties->>'RoutedProcessor' as processor,
    event_type,
    created_at
FROM azure_messages
ORDER BY created_at DESC
LIMIT 10;
```

**Check Routing Rules Application:**
```sql
-- Check if routing rules are working
SELECT
    topic_name,
    properties->>'RoutedProcessor' as routed_processor,
    COUNT(*) as message_count
FROM azure_messages
WHERE properties ? 'RoutedProcessor'
GROUP BY topic_name, properties->>'RoutedProcessor';
```

### 8.3 Test Message Filtering

Send messages that should be filtered out by your subscription filters and verify they don't appear in the database.

**Example: Send Non-Premium Order (should be filtered out if using Premium filter):**
```json
{
  "customerId": "CUST002",
  "orderId": "ORD124",
  "orderType": "Standard",
  "amount": 29.99,
  "eventType": "OrderCreated"
}
```

## ✅ Step 9: Verification Checklist

### Basic Setup:
- [ ] Configuration file updated with your connection string, topic, and subscription
- [ ] PostgreSQL database is running and accessible
- [ ] Test message sent to your Azure Service Bus topic
- [ ] Application runs without errors
- [ ] Message appears in `azure_messages` table
- [ ] Import log created in `message_logs` table
- [ ] Console shows successful processing logs

### Multiple Topics Setup (if configured):
- [ ] `EnableMultipleTopics` set to `true`
- [ ] Multiple `TopicSubscriptions` configured
- [ ] Test messages sent to different topics
- [ ] Messages tagged with correct `topic_name` and `subscription_name`
- [ ] Routing rules working (if configured)
- [ ] Message filters working (if configured)
- [ ] Dead letter topics configured (if specified)
- [ ] Performance metrics acceptable for all topics

## 🎯 Expected Results

After successful processing, you should see:

1. **Console Output:** Successful processing logs
2. **Database Records:** New entries in both `azure_messages` and `message_logs` tables
3. **Message Acknowledgment:** Message removed from Azure Service Bus subscription
4. **Structured Data:** Business fields (customerId, orderId, eventType) extracted and stored

The application will automatically:
- Connect to your Azure Service Bus topic/subscription
- Receive and process messages
- Extract business data from JSON message bodies
- Store complete message metadata and parsed data in PostgreSQL
- Acknowledge processed messages
- Log all operations for monitoring and troubleshooting

## 📞 Support

If you encounter any issues or need help with specific configuration steps, refer to the logs and error messages for troubleshooting guidance.
