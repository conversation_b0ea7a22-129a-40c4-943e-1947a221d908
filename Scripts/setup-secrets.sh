#!/bin/bash

# Setup GCP Secret Manager secrets for SFTP to PostgreSQL Importer

set -e

# Configuration
PROJECT_ID=${GCP_PROJECT_ID:-"your-gcp-project-id"}

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to create or update a secret
create_or_update_secret() {
    local secret_name=$1
    local secret_value=$2
    local description=$3
    
    print_status "Creating/updating secret: $secret_name"
    
    # Check if secret exists
    if gcloud secrets describe "$secret_name" --project="$PROJECT_ID" &>/dev/null; then
        print_status "Secret $secret_name exists, adding new version..."
        echo -n "$secret_value" | gcloud secrets versions add "$secret_name" --data-file=- --project="$PROJECT_ID"
    else
        print_status "Creating new secret: $secret_name"
        echo -n "$secret_value" | gcloud secrets create "$secret_name" \
            --data-file=- \
            --project="$PROJECT_ID" \
            --labels="component=sftp-importer" \
            --replication-policy="automatic"
    fi
}

# Function to prompt for secret value
prompt_for_secret() {
    local secret_name=$1
    local description=$2
    local is_password=$3
    
    echo
    print_status "Setting up secret: $secret_name"
    echo "Description: $description"
    
    if [[ "$is_password" == "true" ]]; then
        echo -n "Enter value (input will be hidden): "
        read -s secret_value
        echo
    else
        echo -n "Enter value: "
        read secret_value
    fi
    
    if [[ -z "$secret_value" ]]; then
        print_warning "Empty value provided for $secret_name, skipping..."
        return
    fi
    
    create_or_update_secret "$secret_name" "$secret_value" "$description"
}

# Main setup function
main() {
    print_status "Setting up GCP Secret Manager secrets for SFTP to PostgreSQL Importer"
    print_status "Project ID: $PROJECT_ID"
    
    # Check if gcloud is installed and authenticated
    if ! command -v gcloud &> /dev/null; then
        print_error "Google Cloud CLI is not installed or not in PATH"
        exit 1
    fi
    
    # Set the project
    gcloud config set project "$PROJECT_ID"
    
    # Enable Secret Manager API if not already enabled
    print_status "Enabling Secret Manager API..."
    gcloud services enable secretmanager.googleapis.com --project="$PROJECT_ID"
    
    print_status "Setting up secrets..."
    print_warning "You will be prompted to enter values for each secret."
    print_warning "Press Enter to skip any secret you don't want to set up now."
    
    # Database secrets
    prompt_for_secret "database-password" "PostgreSQL database password" "true"
    
    # Optional: Full database connection string
    echo
    print_status "You can optionally provide a full database connection string instead of individual components"
    prompt_for_secret "database-connection-string" "Full PostgreSQL connection string (optional)" "false"
    
    # SFTP secrets
    prompt_for_secret "sftp-password" "SFTP server password" "true"
    
    # Optional: SFTP private key passphrase
    prompt_for_secret "sftp-private-key-passphrase" "SFTP private key passphrase (optional)" "true"
    
    print_status "=== Secret Setup Complete ==="
    
    # List created secrets
    print_status "Created/updated secrets:"
    gcloud secrets list --project="$PROJECT_ID" --filter="labels.component=sftp-importer" --format="table(name,createTime)"
    
    print_status "Next steps:"
    print_status "1. Grant access to secrets for your Cloud Run services:"
    print_status "   gcloud projects add-iam-policy-binding $PROJECT_ID \\"
    print_status "     --member=\"serviceAccount:your-service-account@$PROJECT_ID.iam.gserviceaccount.com\" \\"
    print_status "     --role=\"roles/secretmanager.secretAccessor\""
    print_status ""
    print_status "2. Update your appsettings.Production.json with the correct GCP project ID"
    print_status ""
    print_status "3. Test secret access:"
    print_status "   gcloud secrets versions access latest --secret=\"database-password\" --project=\"$PROJECT_ID\""
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --project-id)
            PROJECT_ID="$2"
            shift 2
            ;;
        --help)
            echo "Usage: $0 [OPTIONS]"
            echo "Options:"
            echo "  --project-id    GCP Project ID (default: your-gcp-project-id)"
            echo "  --help          Show this help message"
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            exit 1
            ;;
    esac
done

# Run main function
main
