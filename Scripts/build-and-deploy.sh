#!/bin/bash

# Build and Deploy Script for SFTP to PostgreSQL Importer
# This script builds and deploys multiple Cloud Run jobs

set -e

# Configuration
PROJECT_ID=${GCP_PROJECT_ID:-"your-gcp-project-id"}
REGION=${GCP_REGION:-"us-central1"}
REGISTRY=${GCP_REGISTRY:-"gcr.io"}

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if required tools are installed
check_prerequisites() {
    print_status "Checking prerequisites..."
    
    if ! command -v docker &> /dev/null; then
        print_error "Docker is not installed or not in PATH"
        exit 1
    fi
    
    if ! command -v gcloud &> /dev/null; then
        print_error "Google Cloud CLI is not installed or not in PATH"
        exit 1
    fi
    
    if ! command -v dotnet &> /dev/null; then
        print_error ".NET CLI is not installed or not in PATH"
        exit 1
    fi
    
    print_status "Prerequisites check passed"
}

# Function to build and push Docker image
build_and_push_image() {
    local service_name=$1
    local dockerfile_path=$2
    local context_path=$3
    
    print_status "Building Docker image for $service_name..."
    
    local image_name="$REGISTRY/$PROJECT_ID/$service_name"
    local image_tag="$image_name:latest"
    local image_tag_with_timestamp="$image_name:$(date +%Y%m%d-%H%M%S)"
    
    # Build the Docker image
    docker build -f "$dockerfile_path" -t "$image_tag" -t "$image_tag_with_timestamp" "$context_path"
    
    # Push to registry
    print_status "Pushing Docker image for $service_name..."
    docker push "$image_tag"
    docker push "$image_tag_with_timestamp"
    
    echo "$image_tag"
}

# Function to deploy to Cloud Run
deploy_to_cloud_run() {
    local service_name=$1
    local image_tag=$2
    local env_vars=$3
    
    print_status "Deploying $service_name to Cloud Run..."
    
    gcloud run deploy "$service_name" \
        --image="$image_tag" \
        --platform=managed \
        --region="$REGION" \
        --project="$PROJECT_ID" \
        --no-allow-unauthenticated \
        --memory=1Gi \
        --cpu=1 \
        --timeout=3600 \
        --max-instances=10 \
        --set-env-vars="$env_vars" \
        --quiet
    
    print_status "$service_name deployed successfully"
}

# Function to create Cloud Scheduler job
create_scheduler_job() {
    local job_name=$1
    local service_name=$2
    local schedule=$3
    local description=$4
    
    print_status "Creating Cloud Scheduler job: $job_name..."
    
    # Get the service URL
    local service_url=$(gcloud run services describe "$service_name" \
        --platform=managed \
        --region="$REGION" \
        --project="$PROJECT_ID" \
        --format="value(status.url)")
    
    # Create or update the scheduler job
    gcloud scheduler jobs create http "$job_name" \
        --location="$REGION" \
        --schedule="$schedule" \
        --uri="$service_url" \
        --http-method=POST \
        --description="$description" \
        --project="$PROJECT_ID" \
        --quiet || \
    gcloud scheduler jobs update http "$job_name" \
        --location="$REGION" \
        --schedule="$schedule" \
        --uri="$service_url" \
        --http-method=POST \
        --description="$description" \
        --project="$PROJECT_ID" \
        --quiet
    
    print_status "Scheduler job $job_name created/updated successfully"
}

# Main deployment function
main() {
    print_status "Starting deployment process..."
    
    # Check prerequisites
    check_prerequisites
    
    # Set the GCP project
    gcloud config set project "$PROJECT_ID"
    
    # Configure Docker to use gcloud as a credential helper
    gcloud auth configure-docker
    
    # Build and deploy BrightStar service
    print_status "=== Deploying BrightStar Service ==="
    
    local brightstar_image=$(build_and_push_image \
        "sftp-brightstar-importer" \
        "Sftp.BrightStar/Dockerfile" \
        ".")
    
    deploy_to_cloud_run \
        "sftp-brightstar-importer" \
        "$brightstar_image" \
        "ASPNETCORE_ENVIRONMENT=Production"
    
    # Create scheduler job for BrightStar (daily at 2 AM)
    create_scheduler_job \
        "brightstar-daily-import" \
        "sftp-brightstar-importer" \
        "0 2 * * *" \
        "Daily BrightStar Tadpoles Center data import"
    
    print_status "=== Deployment Complete ==="
    print_status "Services deployed:"
    print_status "  - sftp-brightstar-importer"
    print_status "Scheduler jobs created:"
    print_status "  - brightstar-daily-import (daily at 2 AM)"
    
    print_warning "Don't forget to:"
    print_warning "  1. Configure secrets in GCP Secret Manager"
    print_warning "  2. Set up IAM permissions for the services"
    print_warning "  3. Configure the database connection"
    print_warning "  4. Test the services manually before relying on the scheduler"
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --project-id)
            PROJECT_ID="$2"
            shift 2
            ;;
        --region)
            REGION="$2"
            shift 2
            ;;
        --registry)
            REGISTRY="$2"
            shift 2
            ;;
        --help)
            echo "Usage: $0 [OPTIONS]"
            echo "Options:"
            echo "  --project-id    GCP Project ID (default: your-gcp-project-id)"
            echo "  --region        GCP Region (default: us-central1)"
            echo "  --registry      Container Registry (default: gcr.io)"
            echo "  --help          Show this help message"
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            exit 1
            ;;
    esac
done

# Run main function
main
