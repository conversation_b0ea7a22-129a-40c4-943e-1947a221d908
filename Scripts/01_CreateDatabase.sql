-- Create database (run this as a superuser)
-- CREATE DATABASE sftp_import_db;

-- Connect to the database and run the following:

-- Create bright_star_centers table
CREATE TABLE IF NOT EXISTS bright_star_centers (
    id SERIAL PRIMARY KEY,
    school_id INTEGER NOT NULL,
    school_name VARCHAR(500) NOT NULL,
    status VARCHAR(50) NOT NULL,
    center_live_date DATE,
    regions VARCHAR(100),
    state VARCHAR(10),
    school_number VARCHAR(50),
    center_primary_email VARCHAR(255),
    center_address_line_1 VARCHAR(500),
    center_address_line_2 VARCHAR(500),
    city VARCHAR(100),
    postal_code VARCHAR(20),
    center_phone_number VARCHAR(50),
    center_model VARCHAR(100),
    timezone VARCHAR(100),
    operational_hours VARCHAR(1000),
    center_director_name VARCHAR(255),
    center_director_primary_email VARCHAR(255),
    regional_manager_name VA<PERSON><PERSON><PERSON>(255),
    regional_manager_email VARCHAR(255),
    divisional_vice_president_name <PERSON><PERSON><PERSON><PERSON>(255),
    divisional_vice_president_email VARCHAR(255),
    primary_evacuation_location VARCHAR(500),
    primary_evacuation_location_address_line_1 VARCHAR(500),
    primary_evacuation_location_address_line_2 VARCHAR(500),
    primary_evacuation_city VARCHAR(100),
    primary_evacuation_state VARCHAR(10),
    primary_evacuation_postal_code VARCHAR(20),
    center_emergency_cell_phone VARCHAR(50),
    secondary_evacuation_location VARCHAR(500),
    secondary_evacuation_location_address_line_1 VARCHAR(500),
    secondary_evacuation_location_address_line_2 VARCHAR(500),
    secondary_evacuation_city VARCHAR(100),
    secondary_evacuation_state VARCHAR(10),
    secondary_evacuation_postal_code VARCHAR(20),
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    import_batch_id VARCHAR(100)
);

-- Create import_logs table
CREATE TABLE IF NOT EXISTS import_logs (
    id SERIAL PRIMARY KEY,
    batch_id VARCHAR(100) NOT NULL,
    file_name VARCHAR(500) NOT NULL,
    file_path VARCHAR(1000),
    file_size BIGINT,
    records_processed INTEGER NOT NULL DEFAULT 0,
    records_inserted INTEGER NOT NULL DEFAULT 0,
    records_updated INTEGER NOT NULL DEFAULT 0,
    records_failed INTEGER NOT NULL DEFAULT 0,
    start_time TIMESTAMP WITH TIME ZONE NOT NULL,
    end_time TIMESTAMP WITH TIME ZONE,
    status VARCHAR(50) NOT NULL DEFAULT 'Processing',
    error_message TEXT,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

-- Create indexes for bright_star_centers
CREATE INDEX IF NOT EXISTS ix_bright_star_centers_school_id ON bright_star_centers(school_id);
CREATE INDEX IF NOT EXISTS ix_bright_star_centers_status ON bright_star_centers(status);
CREATE INDEX IF NOT EXISTS ix_bright_star_centers_state ON bright_star_centers(state);
CREATE INDEX IF NOT EXISTS ix_bright_star_centers_import_batch_id ON bright_star_centers(import_batch_id);
CREATE INDEX IF NOT EXISTS ix_bright_star_centers_created_at ON bright_star_centers(created_at);

-- Create unique constraint on school_id to prevent duplicates
CREATE UNIQUE INDEX IF NOT EXISTS uk_bright_star_centers_school_id ON bright_star_centers(school_id);

-- Create indexes for import_logs
CREATE INDEX IF NOT EXISTS ix_import_logs_batch_id ON import_logs(batch_id);
CREATE INDEX IF NOT EXISTS ix_import_logs_status ON import_logs(status);
CREATE INDEX IF NOT EXISTS ix_import_logs_start_time ON import_logs(start_time);
CREATE INDEX IF NOT EXISTS ix_import_logs_created_at ON import_logs(created_at);

-- Create a function to automatically update the updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create trigger to automatically update updated_at on bright_star_centers
CREATE TRIGGER update_bright_star_centers_updated_at 
    BEFORE UPDATE ON bright_star_centers 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- Grant permissions (adjust as needed for your environment)
-- GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO your_app_user;
-- GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO your_app_user;
