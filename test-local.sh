#!/bin/bash

# Test script for local development
# This script tests the file processing functionality without requiring SFTP or database setup

echo "Testing SFTP to PostgreSQL Importer - Local File Processing"
echo "============================================================"

# Check if sample file exists
if [ ! -f "BrightStar_Tadpoles_Center_20250602.txt" ]; then
    echo "Error: Sample file BrightStar_Tadpoles_Center_20250602.txt not found"
    echo "Please copy the sample file to the current directory"
    exit 1
fi

echo "Sample file found: BrightStar_Tadpoles_Center_20250602.txt"

# Build the application
echo "Building application..."
dotnet build -c Release

if [ $? -ne 0 ]; then
    echo "Build failed!"
    exit 1
fi

echo "Build successful!"

# Note: To run the full test, you would need:
# 1. PostgreSQL database running
# 2. Database connection configured in appsettings.json
# 3. Run: dotnet run BrightStar_Tadpoles_Center_20250602.txt

echo ""
echo "To test the application:"
echo "1. Set up PostgreSQL database"
echo "2. Configure appsettings.json with your database connection"
echo "3. Run: dotnet run BrightStar_Tadpoles_Center_20250602.txt"
echo ""
echo "For SFTP testing:"
echo "1. Configure SFTP settings in appsettings.json"
echo "2. Run: dotnet run /remote/path/to/file.txt"
echo ""
echo "For cloud deployment:"
echo "1. Build Docker image: docker build -t sftp-postgres-importer ."
echo "2. Configure GCP Secret Manager"
echo "3. Deploy to Cloud Run"

echo ""
echo "Application structure verified successfully!"
