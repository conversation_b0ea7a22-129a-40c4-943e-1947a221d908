{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.EntityFrameworkCore": "Warning", "Microsoft.Hosting.Lifetime": "Information"}}, "AppConfiguration": {"Environment": "Development", "GcpProjectId": "", "Database": {"ConnectionString": "", "Host": "localhost", "Port": 5432, "Database": "sftp_import_db", "Username": "postgres", "Password": "REPLACE_WITH_YOUR_PASSWORD"}, "Sftp": {"Host": "REPLACE_WITH_YOUR_SFTP_HOST", "Port": 22, "Username": "REPLACE_WITH_YOUR_USERNAME", "Password": "REPLACE_WITH_YOUR_PASSWORD", "PrivateKeyPath": "", "PrivateKeyPassphrase": "", "ConnectionTimeoutSeconds": 30, "OperationTimeoutSeconds": 300}, "Processing": {"TempDirectory": "/tmp", "BatchSize": 1000, "DeleteTempFilesAfterProcessing": true, "FilePattern": "BrightStar_Tadpoles_Center_*.txt", "RemoteDirectory": "/"}}}