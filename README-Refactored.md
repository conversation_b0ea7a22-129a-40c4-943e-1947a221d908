# SFTP to PostgreSQL Importer - Multi-Project Architecture

A refactored .NET Core 9 solution that separates generic SFTP logic from file processing logic, enabling multiple Cloud Run jobs with different schedules to reuse core functionality.

## Architecture Overview

The solution is now split into multiple projects for better separation of concerns and reusability:

```
SftpToPostgresImporter.Core/          # Shared library with generic services
├── Abstractions/                     # File processor interfaces and base classes
├── Configuration/                    # Configuration models
├── Models/                          # Shared data models (ImportLog)
├── Services/                        # Generic services (SFTP, Configuration, Secrets)

SftpToPostgresImporter.BrightStar/    # BrightStar-specific implementation
├── Data/                            # BrightStar database context
├── Models/                          # BrightStar-specific models
├── Services/                        # BrightStar file processor and services
├── Program.cs                       # BrightStar job entry point
├── Dockerfile                       # BrightStar container configuration

SftpToPostgresImporter/               # Original monolithic project (deprecated)
Scripts/                             # Deployment and setup scripts
```

## Key Benefits

### 🔄 **Reusable Core Logic**
- Generic SFTP operations
- Configuration management with secret loading
- Database import patterns
- Logging and error handling

### 🎯 **Pluggable File Processors**
- Each data source gets its own processor implementation
- Consistent interface across all processors
- Easy to add new data sources

### 🚀 **Independent Deployments**
- Each data source can be deployed as a separate Cloud Run job
- Different schedules for different data sources
- Independent scaling and resource allocation

### 🔧 **Simplified Maintenance**
- Core logic changes benefit all processors
- Data source-specific changes are isolated
- Clear separation of concerns

## Core Abstractions

### IFileProcessor<TEntity>

<augment_code_snippet path="SftpToPostgresImporter.Core/Abstractions/IFileProcessor.cs" mode="EXCERPT">
```csharp
public interface IFileProcessor<TEntity> where TEntity : class
{
    string ProcessorType { get; }
    string[] SupportedFilePatterns { get; }
    Task<IEnumerable<TEntity>> ProcessFileAsync(string filePath, string batchId, CancellationToken cancellationToken = default);
    Task<bool> ValidateFileAsync(string filePath, CancellationToken cancellationToken = default);
    bool CanProcess(string fileName);
}
```
</augment_code_snippet>

### BaseFileProcessor<TEntity>

Provides common functionality for all file processors:
- File validation logic
- Helper methods for parsing data
- Consistent error handling
- Pattern matching utilities

## BrightStar Implementation

### BrightStarFileProcessor

<augment_code_snippet path="SftpToPostgresImporter.BrightStar/Services/BrightStarFileProcessor.cs" mode="EXCERPT">
```csharp
public class BrightStarFileProcessor : BaseFileProcessor<BrightStarCenter>
{
    public override string ProcessorType => "BrightStar_Tadpoles_Center";
    public override string[] SupportedFilePatterns => new[] { "BrightStar_Tadpoles_Center_*.txt" };
    
    // BrightStar-specific parsing logic
}
```
</augment_code_snippet>

### Independent Deployment

Each processor runs as its own Cloud Run job:

```bash
# Deploy BrightStar processor
./Scripts/build-and-deploy.sh --project-id your-project

# Creates:
# - sftp-brightstar-importer (Cloud Run service)
# - brightstar-daily-import (Cloud Scheduler job)
```

## Adding New Data Sources

To add a new data source (e.g., "AnotherSource"):

1. **Create new project:**
   ```bash
   dotnet new console -n SftpToPostgresImporter.AnotherSource
   dotnet add reference ../SftpToPostgresImporter.Core/
   ```

2. **Implement file processor:**
   ```csharp
   public class AnotherSourceFileProcessor : BaseFileProcessor<AnotherSourceEntity>
   {
       public override string ProcessorType => "AnotherSource";
       public override string[] SupportedFilePatterns => new[] { "AnotherSource_*.csv" };
       
       // Implement parsing logic specific to AnotherSource format
   }
   ```

3. **Create data models and context:**
   ```csharp
   public class AnotherSourceEntity { /* ... */ }
   public class AnotherSourceDbContext : DbContext { /* ... */ }
   ```

4. **Configure dependency injection:**
   ```csharp
   builder.Services.AddScoped<IFileProcessor<AnotherSourceEntity>, AnotherSourceFileProcessor>();
   builder.Services.AddScoped<IDataImportService<AnotherSourceEntity>, AnotherSourceDataImportService>();
   ```

5. **Deploy independently:**
   ```bash
   # Add to deployment script or create separate deployment
   docker build -t gcr.io/project/sftp-anothersource-importer .
   gcloud run deploy sftp-anothersource-importer --image=...
   ```

## Configuration Management

### Local Development (appsettings.json)
```json
{
  "AppConfiguration": {
    "Environment": "Development",
    "Database": {
      "Host": "localhost",
      "Password": "local_password"
    },
    "Sftp": {
      "Host": "sftp.example.com",
      "Password": "local_sftp_password"
    }
  }
}
```

### Cloud Deployment (GCP Secret Manager)
```json
{
  "AppConfiguration": {
    "Environment": "Production",
    "GcpProjectId": "your-project-id",
    "Database": {
      "Host": "cloud-sql-host"
      // Password loaded from Secret Manager
    },
    "Sftp": {
      "Host": "sftp.example.com"
      // Password loaded from Secret Manager
    }
  }
}
```

## Deployment

### 1. Setup Secrets
```bash
./Scripts/setup-secrets.sh --project-id your-project-id
```

### 2. Deploy Services
```bash
./Scripts/build-and-deploy.sh --project-id your-project-id --region us-central1
```

### 3. Configure Schedules
The deployment script automatically creates Cloud Scheduler jobs:
- `brightstar-daily-import`: Daily at 2 AM
- Add more schedules as needed for other data sources

## Usage Examples

### Process Local File
```bash
# BrightStar processor
cd SftpToPostgresImporter.BrightStar
dotnet run "/path/to/BrightStar_Tadpoles_Center_20250602.txt"
```

### Process Remote File
```bash
# BrightStar processor
dotnet run "/remote/path/BrightStar_Tadpoles_Center_20250602.txt"
```

### Process All Matching Files
```bash
# Processes all files matching the configured pattern
dotnet run
```

## Monitoring and Logging

Each processor logs with its specific context:
- **BrightStar logs**: Prefixed with "BrightStar"
- **Import logs**: Stored in database with `processor_type` field
- **Cloud Logging**: Structured logs for each service

### Database Tracking

The `import_logs` table now includes:
- `processor_type`: Identifies which processor handled the import
- `batch_id`: Unique identifier for each import run
- Detailed statistics and error information

## Benefits of This Architecture

### For Development Teams
- **Parallel Development**: Teams can work on different data sources independently
- **Focused Testing**: Test only the specific processor being modified
- **Clear Ownership**: Each team owns their processor implementation

### For Operations
- **Independent Scaling**: Scale each data source based on its needs
- **Flexible Scheduling**: Different schedules for different data sources
- **Isolated Failures**: One processor failure doesn't affect others
- **Resource Optimization**: Allocate resources based on data source requirements

### For Maintenance
- **Core Updates**: Bug fixes and improvements benefit all processors
- **Isolated Changes**: Data source-specific changes don't affect others
- **Easy Debugging**: Clear separation makes issues easier to trace
- **Version Management**: Deploy core library updates independently

## Migration from Monolithic Version

If you have the original monolithic version:

1. **Keep existing deployment** running during migration
2. **Deploy new BrightStar service** alongside existing service
3. **Test thoroughly** with new architecture
4. **Switch traffic** to new service
5. **Decommission** old monolithic service

The database schema is compatible between versions, so no data migration is needed.

## Future Enhancements

- **Dead Letter Queues**: Handle failed imports with retry logic
- **Metrics Dashboard**: Monitor import statistics across all processors
- **Configuration UI**: Web interface for managing processor configurations
- **Data Validation Rules**: Configurable validation rules per processor
- **Notification System**: Alerts for import failures or anomalies
