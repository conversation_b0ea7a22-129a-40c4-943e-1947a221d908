﻿using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Messaging.AzureServiceBus.Data;
using Messaging.AzureServiceBus.Models;
using Messaging.AzureServiceBus.Services;
using Messaging.Core.Abstractions;
using Messaging.Core.Configuration;
using Messaging.Core.Services;

var builder = Host.CreateApplicationBuilder(args);

// Configuration
var messagingConfig = new MessagingConfiguration();
builder.Configuration.GetSection(MessagingConfiguration.SectionName).Bind(messagingConfig);
builder.Services.AddSingleton(messagingConfig);

// Debug configuration loading
Console.WriteLine($"DEBUG: Environment = {messagingConfig.Environment}");
Console.WriteLine($"DEBUG: IsCloudEnvironmentOverride = {messagingConfig.IsCloudEnvironmentOverride}");
Console.WriteLine($"DEBUG: IsCloudEnvironment = {messagingConfig.IsCloudEnvironment}");
Console.WriteLine($"DEBUG: Database.Host = {messagingConfig.Database.Host}");
Console.WriteLine($"DEBUG: Database.ConnectionString = {messagingConfig.Database.ConnectionString}");
Console.WriteLine($"DEBUG: AzureServiceBus.QueueName = '{messagingConfig.AzureServiceBus.QueueName}'");
Console.WriteLine($"DEBUG: AzureServiceBus.TopicName = '{messagingConfig.AzureServiceBus.TopicName}'");
Console.WriteLine($"DEBUG: AzureServiceBus.SubscriptionName = '{messagingConfig.AzureServiceBus.SubscriptionName}'");
Console.WriteLine($"DEBUG: AzureServiceBus.EnableMultipleTopics = {messagingConfig.AzureServiceBus.EnableMultipleTopics}");
Console.WriteLine($"DEBUG: AzureServiceBus.UseQueue = {messagingConfig.AzureServiceBus.UseQueue}");
Console.WriteLine($"DEBUG: AzureServiceBus.UseTopic = {messagingConfig.AzureServiceBus.UseTopic}");
Console.WriteLine($"DEBUG: AzureServiceBus.UseMultipleTopics = {messagingConfig.AzureServiceBus.UseMultipleTopics}");

// Database
var connectionString = await GetConnectionStringAsync(builder.Configuration, messagingConfig);
builder.Services.AddDbContext<AzureMessageDbContext>(options =>
    options.UseNpgsql(connectionString));

// Core Services
builder.Services.AddSingleton<ISecretManagerService, SecretManagerService>();
builder.Services.AddSingleton<IConfigurationService, ConfigurationService>();

// Azure Service Bus Services
builder.Services.AddSingleton<IMessagingService, AzureServiceBusService>(provider =>
{
    var logger = provider.GetRequiredService<ILogger<AzureServiceBusService>>();
    var configService = provider.GetRequiredService<IConfigurationService>();
    var asbConfig = configService.GetAzureServiceBusConfigurationAsync().Result;
    return new AzureServiceBusService(logger, asbConfig);
});

builder.Services.AddScoped<IMessageProcessor<AzureMessageEntity>, AzureMessageProcessor>();
builder.Services.AddScoped<IMessageImportService<AzureMessageEntity>, AzureDataImportService>();
builder.Services.AddScoped<AzureOrchestrationService>();

// Worker service
builder.Services.AddHostedService<AzureWorkerService>();

// Logging
builder.Services.AddLogging(logging =>
{
    logging.ClearProviders();
    logging.AddConsole();
    if (messagingConfig.IsCloudEnvironment)
    {
        // Add Google Cloud Logging if needed
    }
});

var host = builder.Build();

// Ensure database is created
using (var scope = host.Services.CreateScope())
{
    var context = scope.ServiceProvider.GetRequiredService<AzureMessageDbContext>();
    await context.Database.EnsureCreatedAsync();
}

await host.RunAsync();

static async Task<string> GetConnectionStringAsync(IConfiguration configuration, MessagingConfiguration messagingConfig)
{
    if (messagingConfig.IsCloudEnvironment)
    {
        // In cloud environment, use configuration service to get connection string with secrets
        var serviceProvider = new ServiceCollection()
            .AddSingleton(messagingConfig)
            .AddScoped<ISecretManagerService, SecretManagerService>()
            .AddScoped<IConfigurationService, ConfigurationService>()
            .AddLogging()
            .BuildServiceProvider();

        var configService = serviceProvider.GetRequiredService<IConfigurationService>();
        return await configService.GetDatabaseConnectionStringAsync();
    }
    else
    {
        // In local environment, use connection string from appsettings
        if (!string.IsNullOrEmpty(messagingConfig.Database.ConnectionString))
        {
            return messagingConfig.Database.ConnectionString;
        }

        return $"Host={messagingConfig.Database.Host};Port={messagingConfig.Database.Port};Database={messagingConfig.Database.Database};Username={messagingConfig.Database.Username};Password={messagingConfig.Database.Password};";
    }
}
