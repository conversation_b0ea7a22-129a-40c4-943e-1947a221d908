{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.EntityFrameworkCore": "Warning", "Microsoft.Hosting.Lifetime": "Information"}}, "MessagingConfiguration": {"Environment": "Development", "IsCloudEnvironmentOverride": false, "GcpProjectId": "", "Database": {"ConnectionString": "Host=127.0.0.1;Port=5432;Database=messaging_db;Username=postgres;Password=********;", "Host": "127.0.0.1", "Port": 5432, "Database": "messaging_db", "Username": "postgres", "Password": "********"}, "AzureServiceBus": {"ConnectionString": "Endpoint=sb://mapp2mbdstaffupdates.servicebus.windows.net/;SharedAccessKeyName=RootManageSharedAccessKey;SharedAccessKey=ySMDLJFpavxFtlgJ97khabgxHGJk1hbhn+ASbFr1BBg=", "QueueName": "", "TopicName": "staffupdatestopic_dev", "SubscriptionName": "staffupdatestopic_dev", "MaxConcurrentMessages": 10, "MessageLockDurationMinutes": 5, "MaxRetryAttempts": 3, "EnableMultipleTopics": false, "TopicSubscriptions": [{"SubscriptionName": "orders-subscription", "TopicName": "orders-topic", "MaxConcurrentMessages": 5, "AckDeadlineSeconds": 300, "MaxDeliveryAttempts": 3, "DeadLetterTopic": "orders-deadletter", "EnableMessageOrdering": false, "Filters": [{"Name": "OrderTypeFilter", "FilterType": "sql", "Parameters": {"Expression": "orderType = 'Premium'"}, "Enabled": true}]}, {"SubscriptionName": "notifications-subscription", "TopicName": "notifications-topic", "MaxConcurrentMessages": 10, "AckDeadlineSeconds": 600, "MaxDeliveryAttempts": 5, "Filters": [{"Name": "Urgent<PERSON><PERSON><PERSON>", "FilterType": "correlation", "Parameters": {"Property.Priority": "High"}, "Enabled": true}]}], "Routing": {"EnableRouting": true, "LogRoutingDecisions": true, "DefaultProcessorType": "GenericProcessor", "Rules": [{"Name": "OrdersToOrderProcessor", "Description": "Route order messages to order processor", "Priority": 100, "Enabled": true, "Conditions": [{"Field": "TopicName", "Operator": "Equals", "Value": "orders-topic"}], "Action": {"ActionType": "RouteToProcessor", "ProcessorType": "OrderProcessor", "StopProcessing": true}}]}}, "GooglePubSub": {"ProjectId": "", "SubscriptionName": "", "TopicName": "", "MaxMessages": 100, "AckDeadlineSeconds": 600, "MaxRetryAttempts": 3, "EnableMessageOrdering": false}, "Processing": {"BatchSize": 100, "MaxConcurrentBatches": 5, "ProcessingTimeoutMinutes": 30, "EnableDeadLetterQueue": true, "RetryDelaySeconds": 30, "TempDirectory": "/tmp"}}}