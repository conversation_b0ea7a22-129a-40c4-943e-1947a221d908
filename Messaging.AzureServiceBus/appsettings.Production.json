{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.EntityFrameworkCore": "Warning"}}, "MessagingConfiguration": {"Environment": "Development", "IsCloudEnvironmentOverride": false, "GcpProjectId": "", "Database": {"ConnectionString": "Host=127.0.0.1;Port=5432;Database=messaging_db;Username=postgres;Password=********;", "Host": "127.0.0.1", "Port": 5432, "Database": "messaging_db", "Username": "postgres", "Password": "********"}, "AzureServiceBus": {"ConnectionString": "Endpoint=sb://mapp2mbdstaffupdates.servicebus.windows.net/;SharedAccessKeyName=RootManageSharedAccessKey;SharedAccessKey=ySMDLJFpavxFtlgJ97khabgxHGJk1hbhn+ASbFr1BBg=", "QueueName": "", "TopicName": "staffupdatestopic_dev", "SubscriptionName": "staffupdatestopic_dev", "MaxConcurrentMessages": 10, "MessageLockDurationMinutes": 5, "MaxRetryAttempts": 3, "EnableMultipleTopics": false}, "GooglePubSub": {"ProjectId": "", "SubscriptionName": "", "TopicName": "", "MaxMessages": 100, "AckDeadlineSeconds": 600, "MaxRetryAttempts": 3, "EnableMessageOrdering": false}, "Processing": {"BatchSize": 200, "MaxConcurrentBatches": 10, "ProcessingTimeoutMinutes": 30, "EnableDeadLetterQueue": true, "RetryDelaySeconds": 30, "TempDirectory": "/tmp"}}}