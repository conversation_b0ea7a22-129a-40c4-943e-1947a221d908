using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Messaging.AzureServiceBus.Models;

/// <summary>
/// Entity model representing a processed Azure Service Bus message with comprehensive metadata and business data.
/// Stores complete message information including Service Bus properties, routing details, and extracted business entities.
/// </summary>
/// <remarks>
/// This entity model captures complete information about Azure Service Bus messages including:
///
/// **Message Identification:**
/// - Unique message identifiers and correlation tracking
/// - Session-based message grouping support
/// - Message type classification and subject information
///
/// **Service Bus Metadata:**
/// - Enqueue timing and scheduling information
/// - Delivery count and retry tracking
/// - Time-to-live and expiration management
/// - Queue, topic, and subscription routing details
///
/// **Message Content:**
/// - Complete message body preservation
/// - Content type and encoding information
/// - Serialized message properties and headers
///
/// **Business Data Extraction:**
/// - Customer and order identification fields
/// - Event type classification and data storage
/// - Source system tracking and version information
/// - Extensible structure for additional business entities
///
/// **Processing Metadata:**
/// - Processing timestamps and batch tracking
/// - Import operation identification for data lineage
/// - Audit trail information for compliance and troubleshooting
///
/// This model is populated from Azure Service Bus messages and provides a comprehensive
/// view of message content, routing, and business context for reporting and analysis.
/// </remarks>
[Table("azure_messages")]
public class AzureMessageEntity
{
    /// <summary>
    /// Gets or sets the unique database identifier for this message record.
    /// Primary key for the database table.
    /// </summary>
    [Key]
    [Column("id")]
    public int Id { get; set; }

    /// <summary>
    /// Gets or sets the unique message identifier from Azure Service Bus.
    /// Used for message tracking, deduplication, and correlation across systems.
    /// </summary>
    [Column("message_id")]
    [MaxLength(500)]
    public string MessageId { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the correlation identifier for linking related messages.
    /// Used for tracking message flows and request-response patterns.
    /// </summary>
    [Column("correlation_id")]
    [MaxLength(500)]
    public string? CorrelationId { get; set; }

    /// <summary>
    /// Gets or sets the session identifier for message grouping.
    /// Used for maintaining message order and session-based processing.
    /// </summary>
    [Column("session_id")]
    [MaxLength(500)]
    public string? SessionId { get; set; }

    /// <summary>
    /// Gets or sets the message type classification.
    /// Used for message routing and processor selection.
    /// </summary>
    [Column("message_type")]
    [MaxLength(100)]
    public string? MessageType { get; set; }

    /// <summary>
    /// Gets or sets the message subject or title.
    /// Provides human-readable description of the message content.
    /// </summary>
    [Column("subject")]
    [MaxLength(500)]
    public string? Subject { get; set; }

    /// <summary>
    /// Gets or sets the complete message body content.
    /// Contains the actual message payload, typically in JSON or XML format.
    /// </summary>
    [Column("body")]
    public string Body { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the content type of the message body.
    /// Indicates the format and encoding of the message content.
    /// </summary>
    [Column("content_type")]
    [MaxLength(100)]
    public string? ContentType { get; set; }

    /// <summary>
    /// Gets or sets the timestamp when the message was enqueued in Azure Service Bus.
    /// Used for message age calculation and processing time analysis.
    /// </summary>
    [Column("enqueued_time")]
    public DateTime EnqueuedTime { get; set; }

    /// <summary>
    /// Gets or sets the scheduled enqueue time for delayed message delivery.
    /// Used for implementing delayed processing and scheduled operations.
    /// </summary>
    [Column("scheduled_enqueue_time")]
    public DateTime? ScheduledEnqueueTime { get; set; }

    /// <summary>
    /// Gets or sets the number of delivery attempts for this message.
    /// Used for tracking retry attempts and identifying problematic messages.
    /// </summary>
    [Column("delivery_count")]
    public int DeliveryCount { get; set; }

    /// <summary>
    /// Gets or sets the time-to-live duration in seconds.
    /// Indicates how long the message should remain in the queue before expiring.
    /// </summary>
    [Column("time_to_live_seconds")]
    public long? TimeToLiveSeconds { get; set; }

    /// <summary>
    /// Gets or sets the name of the queue where the message was received.
    /// Used for tracking message routing and source identification.
    /// </summary>
    [Column("queue_name")]
    [MaxLength(200)]
    public string? QueueName { get; set; }

    /// <summary>
    /// Gets or sets the name of the topic where the message was published.
    /// Used for tracking message routing in publish-subscribe scenarios.
    /// </summary>
    [Column("topic_name")]
    [MaxLength(200)]
    public string? TopicName { get; set; }

    /// <summary>
    /// Gets or sets the name of the subscription that received the message.
    /// Used for tracking message routing and subscription-specific processing.
    /// </summary>
    [Column("subscription_name")]
    [MaxLength(200)]
    public string? SubscriptionName { get; set; }

    /// <summary>
    /// Gets or sets the serialized message properties and headers.
    /// Contains additional metadata and custom properties in JSON format.
    /// </summary>
    [Column("properties")]
    public string? Properties { get; set; }

    /// <summary>
    /// Gets or sets the timestamp when the message was processed.
    /// Automatically set to the current UTC time when the entity is created.
    /// </summary>
    [Column("processed_at")]
    public DateTime ProcessedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// Gets or sets the timestamp when this message record was created.
    /// Automatically set to the current UTC time when the entity is created.
    /// </summary>
    [Column("created_at")]
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// Gets or sets the import batch identifier for data lineage tracking.
    /// Links this record to the specific import operation that created it.
    /// </summary>
    [Column("import_batch_id")]
    [MaxLength(100)]
    public string? ImportBatchId { get; set; }

    /// <summary>
    /// Gets or sets the customer identifier extracted from the message.
    /// Business entity field for customer-related message processing.
    /// </summary>
    [Column("customer_id")]
    [MaxLength(100)]
    public string? CustomerId { get; set; }

    /// <summary>
    /// Gets or sets the order identifier extracted from the message.
    /// Business entity field for order-related message processing.
    /// </summary>
    [Column("order_id")]
    [MaxLength(100)]
    public string? OrderId { get; set; }

    /// <summary>
    /// Gets or sets the event type classification extracted from the message.
    /// Used for event-driven architecture and business process routing.
    /// </summary>
    [Column("event_type")]
    [MaxLength(100)]
    public string? EventType { get; set; }

    /// <summary>
    /// Gets or sets the serialized event data extracted from the message.
    /// Contains business-specific event information in JSON format.
    /// </summary>
    [Column("event_data")]
    public string? EventData { get; set; }

    /// <summary>
    /// Gets or sets the source system that generated the message.
    /// Used for tracking message origins and system integration patterns.
    /// </summary>
    [Column("source_system")]
    [MaxLength(100)]
    public string? SourceSystem { get; set; }

    /// <summary>
    /// Gets or sets the message schema or format version.
    /// Used for handling message evolution and backward compatibility.
    /// </summary>
    [Column("version")]
    [MaxLength(20)]
    public string? Version { get; set; }
}
