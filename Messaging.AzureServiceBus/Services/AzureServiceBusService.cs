using Azure.Messaging.ServiceBus;
using Azure.Messaging.ServiceBus.Administration;
using Microsoft.Extensions.Logging;
using Messaging.Core.Abstractions;
using Messaging.Core.Configuration;
using Messaging.Core.Models;
using System.Text.Json;

namespace Messaging.AzureServiceBus.Services;

public class AzureServiceBusService : IMessagingService
{
    private readonly ILogger<AzureServiceBusService> _logger;
    private readonly AzureServiceBusConfiguration _config;
    private readonly IMessageRouter? _messageRouter;
    private ServiceBusClient? _client;

    private ServiceBusReceiver? _receiver;
    private readonly Dictionary<string, ServiceBusReceiver> _topicReceivers = new();

    public string PlatformName => "AzureServiceBus";

    public AzureServiceBusService(
        ILogger<AzureServiceBusService> logger,
        AzureServiceBusConfiguration config,
        IMessageRouter? messageRouter = null)
    {
        _logger = logger;
        _config = config;
        _messageRouter = messageRouter;
    }

    public async Task StartAsync(CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Starting Azure Service Bus service");

        try
        {
            _client = new ServiceBusClient(_config.ConnectionString);

            if (_config.UseQueue)
            {
                _receiver = _client.CreateReceiver(_config.QueueName);
                _logger.LogInformation("Created receiver for queue: {QueueName}", _config.QueueName);
            }
            else if (_config.UseMultipleTopics)
            {
                // Create receivers for multiple topic subscriptions
                foreach (var subscription in _config.TopicSubscriptions)
                {
                    var receiver = _client.CreateReceiver(subscription.TopicName, subscription.SubscriptionName);
                    var key = $"{subscription.TopicName}/{subscription.SubscriptionName}";
                    _topicReceivers[key] = receiver;
                    _logger.LogInformation("Created receiver for topic: {TopicName}, subscription: {SubscriptionName}",
                        subscription.TopicName, subscription.SubscriptionName);
                }

                if (!_topicReceivers.Any())
                {
                    throw new InvalidOperationException("No topic subscriptions configured for multiple topic mode");
                }
            }
            else if (_config.UseTopic)
            {
                _receiver = _client.CreateReceiver(_config.TopicName, _config.SubscriptionName);
                _logger.LogInformation("Created receiver for topic: {TopicName}, subscription: {SubscriptionName}",
                    _config.TopicName, _config.SubscriptionName);
            }
            else
            {
                throw new InvalidOperationException("Either QueueName, TopicName/SubscriptionName, or TopicSubscriptions must be configured");
            }

            _logger.LogInformation("Azure Service Bus service started successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to start Azure Service Bus service");
            throw;
        }
    }

    public async Task StopAsync(CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Stopping Azure Service Bus service");

        try
        {


            if (_receiver != null)
            {
                await _receiver.DisposeAsync();
            }

            if (_client != null)
            {
                await _client.DisposeAsync();
            }

            _logger.LogInformation("Azure Service Bus service stopped successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error stopping Azure Service Bus service");
        }
    }

    public async Task<IEnumerable<MessageData>> ReceiveMessagesAsync(int maxMessages, CancellationToken cancellationToken = default)
    {
        if (_config.UseMultipleTopics)
        {
            return await ReceiveFromMultipleTopicsAsync(maxMessages, cancellationToken);
        }

        if (_receiver == null)
        {
            throw new InvalidOperationException("Service Bus receiver is not initialized. Call StartAsync first.");
        }

        try
        {
            _logger.LogDebug("Receiving up to {MaxMessages} messages from Azure Service Bus", maxMessages);

            var messages = await _receiver.ReceiveMessagesAsync(maxMessages, TimeSpan.FromSeconds(30), cancellationToken);
            var messageDataList = new List<MessageData>();

            foreach (var message in messages)
            {
                var messageData = ConvertToMessageData(message);
                messageDataList.Add(messageData);
            }

            _logger.LogDebug("Received {MessageCount} messages from Azure Service Bus", messageDataList.Count);
            return messageDataList;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to receive messages from Azure Service Bus");
            return Enumerable.Empty<MessageData>();
        }
    }

    public async Task<AcknowledgmentResult> CompleteMessageAsync(MessageData messageData, CancellationToken cancellationToken = default)
    {
        if (_receiver == null)
        {
            return new AcknowledgmentResult
            {
                MessageId = messageData.MessageId,
                IsSuccess = false,
                ErrorMessage = "Service Bus receiver is not initialized",
                Action = AcknowledgmentAction.Complete
            };
        }

        try
        {
            if (messageData.PlatformMessage is ServiceBusReceivedMessage serviceBusMessage)
            {
                await _receiver.CompleteMessageAsync(serviceBusMessage, cancellationToken);
                
                _logger.LogDebug("Completed message: {MessageId}", messageData.MessageId);
                
                return new AcknowledgmentResult
                {
                    MessageId = messageData.MessageId,
                    IsSuccess = true,
                    Action = AcknowledgmentAction.Complete
                };
            }

            return new AcknowledgmentResult
            {
                MessageId = messageData.MessageId,
                IsSuccess = false,
                ErrorMessage = "Invalid platform message type",
                Action = AcknowledgmentAction.Complete
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to complete message: {MessageId}", messageData.MessageId);
            
            return new AcknowledgmentResult
            {
                MessageId = messageData.MessageId,
                IsSuccess = false,
                ErrorMessage = ex.Message,
                Action = AcknowledgmentAction.Complete
            };
        }
    }

    public async Task<AcknowledgmentResult> AbandonMessageAsync(MessageData messageData, CancellationToken cancellationToken = default)
    {
        if (_receiver == null)
        {
            return new AcknowledgmentResult
            {
                MessageId = messageData.MessageId,
                IsSuccess = false,
                ErrorMessage = "Service Bus receiver is not initialized",
                Action = AcknowledgmentAction.Abandon
            };
        }

        try
        {
            if (messageData.PlatformMessage is ServiceBusReceivedMessage serviceBusMessage)
            {
                await _receiver.AbandonMessageAsync(serviceBusMessage, cancellationToken: cancellationToken);
                
                _logger.LogDebug("Abandoned message: {MessageId}", messageData.MessageId);
                
                return new AcknowledgmentResult
                {
                    MessageId = messageData.MessageId,
                    IsSuccess = true,
                    Action = AcknowledgmentAction.Abandon
                };
            }

            return new AcknowledgmentResult
            {
                MessageId = messageData.MessageId,
                IsSuccess = false,
                ErrorMessage = "Invalid platform message type",
                Action = AcknowledgmentAction.Abandon
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to abandon message: {MessageId}", messageData.MessageId);
            
            return new AcknowledgmentResult
            {
                MessageId = messageData.MessageId,
                IsSuccess = false,
                ErrorMessage = ex.Message,
                Action = AcknowledgmentAction.Abandon
            };
        }
    }

    public async Task<AcknowledgmentResult> DeadLetterMessageAsync(MessageData messageData, string reason, CancellationToken cancellationToken = default)
    {
        if (_receiver == null)
        {
            return new AcknowledgmentResult
            {
                MessageId = messageData.MessageId,
                IsSuccess = false,
                ErrorMessage = "Service Bus receiver is not initialized",
                Action = AcknowledgmentAction.DeadLetter
            };
        }

        try
        {
            if (messageData.PlatformMessage is ServiceBusReceivedMessage serviceBusMessage)
            {
                await _receiver.DeadLetterMessageAsync(serviceBusMessage, reason, cancellationToken: cancellationToken);
                
                _logger.LogWarning("Dead lettered message: {MessageId}, Reason: {Reason}", messageData.MessageId, reason);
                
                return new AcknowledgmentResult
                {
                    MessageId = messageData.MessageId,
                    IsSuccess = true,
                    Action = AcknowledgmentAction.DeadLetter
                };
            }

            return new AcknowledgmentResult
            {
                MessageId = messageData.MessageId,
                IsSuccess = false,
                ErrorMessage = "Invalid platform message type",
                Action = AcknowledgmentAction.DeadLetter
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to dead letter message: {MessageId}", messageData.MessageId);
            
            return new AcknowledgmentResult
            {
                MessageId = messageData.MessageId,
                IsSuccess = false,
                ErrorMessage = ex.Message,
                Action = AcknowledgmentAction.DeadLetter
            };
        }
    }

    public async Task<HealthCheckResult> CheckHealthAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            if (_client == null)
            {
                return new HealthCheckResult
                {
                    IsHealthy = false,
                    ErrorMessage = "Service Bus client is not initialized"
                };
            }

            // Try to get queue/topic properties as a health check
            var managementClient = new ServiceBusAdministrationClient(_config.ConnectionString);
            
            if (_config.UseQueue)
            {
                var queueProperties = await managementClient.GetQueueAsync(_config.QueueName, cancellationToken);
                return new HealthCheckResult
                {
                    IsHealthy = true,
                    Details = new Dictionary<string, object>
                    {
                        ["QueueName"] = _config.QueueName,
                        ["Status"] = queueProperties.Value.Status.ToString(),
                        ["MaxSizeInMegabytes"] = queueProperties.Value.MaxSizeInMegabytes
                    }
                };
            }
            else if (_config.UseTopic)
            {
                var topicProperties = await managementClient.GetTopicAsync(_config.TopicName, cancellationToken);
                var subscriptionProperties = await managementClient.GetSubscriptionAsync(_config.TopicName, _config.SubscriptionName, cancellationToken);

                return new HealthCheckResult
                {
                    IsHealthy = true,
                    Details = new Dictionary<string, object>
                    {
                        ["TopicName"] = _config.TopicName,
                        ["SubscriptionName"] = _config.SubscriptionName,
                        ["TopicStatus"] = topicProperties.Value.Status.ToString(),
                        ["SubscriptionStatus"] = subscriptionProperties.Value.Status.ToString()
                    }
                };
            }

            return new HealthCheckResult
            {
                IsHealthy = false,
                ErrorMessage = "Neither queue nor topic/subscription is configured"
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Health check failed for Azure Service Bus");
            
            return new HealthCheckResult
            {
                IsHealthy = false,
                ErrorMessage = ex.Message
            };
        }
    }

    public Dictionary<string, object> GetConnectionInfo()
    {
        return new Dictionary<string, object>
        {
            ["PlatformName"] = PlatformName,
            ["QueueName"] = _config.QueueName ?? "N/A",
            ["TopicName"] = _config.TopicName ?? "N/A",
            ["SubscriptionName"] = _config.SubscriptionName ?? "N/A",
            ["MaxConcurrentMessages"] = _config.MaxConcurrentMessages,
            ["MessageLockDurationMinutes"] = _config.MessageLockDurationMinutes
        };
    }

    private MessageData ConvertToMessageData(ServiceBusReceivedMessage message)
    {
        var properties = new Dictionary<string, object>();
        
        // Add application properties
        foreach (var prop in message.ApplicationProperties)
        {
            properties[prop.Key] = prop.Value;
        }

        // Add system properties
        properties["Subject"] = message.Subject ?? string.Empty;
        properties["ContentType"] = message.ContentType ?? string.Empty;
        properties["ReplyTo"] = message.ReplyTo ?? string.Empty;
        properties["To"] = message.To ?? string.Empty;

        var bodyContent = message.Body.ToString();

        // Debug logging for message body
        _logger.LogDebug("Processing message {MessageId}: Body length = {BodyLength}, First 100 chars = {BodyPreview}",
            message.MessageId, bodyContent.Length,
            bodyContent.Length > 100 ? bodyContent.Substring(0, 100) + "..." : bodyContent);

        var messageData = new MessageData
        {
            MessageId = message.MessageId,
            MessageType = message.Subject,
            Body = bodyContent,
            Properties = properties,
            EnqueuedTime = message.EnqueuedTime.UtcDateTime,
            ScheduledEnqueueTime = message.ScheduledEnqueueTime != DateTimeOffset.MinValue ? message.ScheduledEnqueueTime.UtcDateTime : null,
            DeliveryCount = message.DeliveryCount,
            CorrelationId = message.CorrelationId,
            SessionId = message.SessionId,
            TimeToLive = message.TimeToLive,
            ContentType = message.ContentType,
            RoutingKey = message.Subject,
            PlatformMessage = message
        };

        // Add labels from application properties
        foreach (var prop in message.ApplicationProperties.Where(p => p.Value is string))
        {
            messageData.Labels[prop.Key] = prop.Value.ToString()!;
        }

        return messageData;
    }

    private async Task<IEnumerable<MessageData>> ReceiveFromMultipleTopicsAsync(int maxMessages, CancellationToken cancellationToken)
    {
        var allMessages = new List<MessageData>();
        var messagesPerReceiver = Math.Max(1, maxMessages / _topicReceivers.Count);

        var receiveTasks = _topicReceivers.Select(async kvp =>
        {
            var (key, receiver) = (kvp.Key, kvp.Value);
            var parts = key.Split('/');
            var topicName = parts[0];
            var subscriptionName = parts[1];

            try
            {
                _logger.LogDebug("Receiving up to {MaxMessages} messages from topic {TopicName}, subscription {SubscriptionName}",
                    messagesPerReceiver, topicName, subscriptionName);

                var messages = await receiver.ReceiveMessagesAsync(messagesPerReceiver, TimeSpan.FromSeconds(10), cancellationToken);
                var messageDataList = new List<MessageData>();

                foreach (var message in messages)
                {
                    var messageData = ConvertToMessageData(message);
                    messageData.TopicName = topicName;
                    messageData.SubscriptionName = subscriptionName;

                    // Apply message routing if configured
                    if (_messageRouter != null)
                    {
                        var routedProcessor = _messageRouter.RouteMessage(messageData);
                        if (!string.IsNullOrEmpty(routedProcessor))
                        {
                            messageData.Properties["RoutedProcessor"] = routedProcessor;
                        }
                    }

                    messageDataList.Add(messageData);
                }

                _logger.LogDebug("Received {MessageCount} messages from topic {TopicName}, subscription {SubscriptionName}",
                    messageDataList.Count, topicName, subscriptionName);

                return messageDataList;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to receive messages from topic {TopicName}, subscription {SubscriptionName}",
                    topicName, subscriptionName);
                return Enumerable.Empty<MessageData>();
            }
        });

        var results = await Task.WhenAll(receiveTasks);

        foreach (var result in results)
        {
            allMessages.AddRange(result);
        }

        _logger.LogDebug("Received total of {MessageCount} messages from {ReceiverCount} topic receivers",
            allMessages.Count, _topicReceivers.Count);

        return allMessages;
    }
}
