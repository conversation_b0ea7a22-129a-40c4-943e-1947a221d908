using Microsoft.Extensions.Logging;
using Messaging.AzureServiceBus.Models;
using Messaging.Core.Abstractions;
using Messaging.Core.Models;
using System.Text.Json;

namespace Messaging.AzureServiceBus.Services;

public class AzureMessageProcessor : BaseMessageProcessor<AzureMessageEntity>
{
    public override string ProcessorType => "AzureServiceBus_Generic";
    public override string[] SupportedMessageTypes => new[] { "*" }; // Support all message types
    public override string PlatformName => "AzureServiceBus";

    public AzureMessageProcessor(ILogger<AzureMessageProcessor> logger) : base(logger)
    {
    }

    public override async Task<ProcessingResult<AzureMessageEntity>> ProcessMessagesAsync(
        IEnumerable<MessageData> messages, 
        string batchId, 
        CancellationToken cancellationToken = default)
    {
        Logger.LogInformation("Starting to process {MessageCount} Azure Service Bus messages with batch ID: {BatchId}", 
            messages.Count(), batchId);

        var result = new ProcessingResult<AzureMessageEntity>();
        var entities = new List<AzureMessageEntity>();
        var messagesList = messages.ToList();

        result.MessagesProcessed = messagesList.Count;

        try
        {
            foreach (var message in messagesList)
            {
                cancellationToken.ThrowIfCancellationRequested();

                try
                {
                    var entity = await ProcessSingleMessageAsync(message, batchId, cancellationToken);
                    if (entity != null)
                    {
                        entities.Add(entity);
                        result.MessagesValid++;
                    }
                    else
                    {
                        result.MessagesInvalid++;
                        result.Errors.Add($"Failed to process message: {message.MessageId}");
                    }
                }
                catch (Exception ex)
                {
                    Logger.LogWarning(ex, "Failed to process message {MessageId}", message.MessageId);
                    result.MessagesInvalid++;
                    result.Errors.Add($"Error processing message {message.MessageId}: {ex.Message}");
                }
            }

            result.Entities = entities;
            UpdateProcessingStats(result.MessagesProcessed, result.MessagesValid, result.MessagesInvalid);

            Logger.LogInformation("Successfully processed {ValidCount}/{TotalCount} Azure Service Bus messages", 
                result.MessagesValid, result.MessagesProcessed);

            return result;
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Failed to process Azure Service Bus messages for batch ID: {BatchId}", batchId);
            result.Errors.Add($"Batch processing failed: {ex.Message}");
            return result;
        }
    }

    protected override async Task<bool> ValidateMessageContentAsync(MessageData message, CancellationToken cancellationToken)
    {
        try
        {
            // Basic validation for Azure Service Bus messages
            if (string.IsNullOrEmpty(message.Body))
            {
                Logger.LogWarning("Message {MessageId} has empty body", message.MessageId);
                return false;
            }

            // Try to parse as JSON if it looks like JSON
            if (message.Body.TrimStart().StartsWith("{") || message.Body.TrimStart().StartsWith("["))
            {
                try
                {
                    JsonDocument.Parse(message.Body);
                }
                catch (JsonException ex)
                {
                    Logger.LogWarning("Message {MessageId} has invalid JSON body: {Error}", message.MessageId, ex.Message);
                    return false;
                }
            }

            return true;
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error validating Azure Service Bus message: {MessageId}", message.MessageId);
            return false;
        }
    }

    private async Task<AzureMessageEntity?> ProcessSingleMessageAsync(MessageData message, string batchId, CancellationToken cancellationToken)
    {
        try
        {
            // Debug logging for message body before creating entity
            Logger.LogDebug("Creating entity for message {MessageId}: Body length = {BodyLength}, Body preview = {BodyPreview}",
                message.MessageId, message.Body.Length,
                message.Body.Length > 50 ? message.Body.Substring(0, 50) + "..." : message.Body);

            var entity = new AzureMessageEntity
            {
                MessageId = message.MessageId,
                CorrelationId = message.CorrelationId,
                SessionId = message.SessionId,
                MessageType = message.MessageType,
                Body = message.Body,
                EnqueuedTime = message.EnqueuedTime,
                ScheduledEnqueueTime = message.ScheduledEnqueueTime,
                DeliveryCount = message.DeliveryCount,
                TimeToLiveSeconds = message.TimeToLive?.TotalSeconds != null ? (long)message.TimeToLive.Value.TotalSeconds : null,
                ImportBatchId = batchId,
                ProcessedAt = DateTime.UtcNow,
                CreatedAt = DateTime.UtcNow
            };

            // Extract properties from message
            if (message.Properties.Count > 0)
            {
                entity.Properties = JsonSerializer.Serialize(message.Properties);
                
                // Extract common properties
                entity.Subject = GetMessageProperty<string>(message, "Subject");
                entity.ContentType = GetMessageProperty<string>(message, "ContentType");
                entity.QueueName = GetMessageProperty<string>(message, "QueueName");
                entity.TopicName = GetMessageProperty<string>(message, "TopicName");
                entity.SubscriptionName = GetMessageProperty<string>(message, "SubscriptionName");
            }

            // Parse message body for common business data
            await ParseBusinessDataAsync(entity, message, cancellationToken);

            return entity;
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error processing single Azure Service Bus message: {MessageId}", message.MessageId);
            return null;
        }
    }

    private async Task ParseBusinessDataAsync(AzureMessageEntity entity, MessageData message, CancellationToken cancellationToken)
    {
        try
        {
            // Try to parse common business data from JSON body
            if (message.Body.TrimStart().StartsWith("{"))
            {
                var jsonDoc = JsonDocument.Parse(message.Body);
                var root = jsonDoc.RootElement;

                // Extract common fields - customize based on your message structure
                if (root.TryGetProperty("customerId", out var customerIdElement))
                {
                    entity.CustomerId = customerIdElement.GetString();
                }

                if (root.TryGetProperty("orderId", out var orderIdElement))
                {
                    entity.OrderId = orderIdElement.GetString();
                }

                if (root.TryGetProperty("eventType", out var eventTypeElement))
                {
                    entity.EventType = eventTypeElement.GetString();
                }

                if (root.TryGetProperty("sourceSystem", out var sourceSystemElement))
                {
                    entity.SourceSystem = sourceSystemElement.GetString();
                }

                if (root.TryGetProperty("version", out var versionElement))
                {
                    entity.Version = versionElement.GetString();
                }

                // Store the entire event data as JSON
                if (root.TryGetProperty("data", out var dataElement))
                {
                    entity.EventData = dataElement.GetRawText();
                }
                else
                {
                    // If no specific "data" property, store the entire message
                    entity.EventData = message.Body;
                }
            }
            else
            {
                // For non-JSON messages, store as plain text
                entity.EventData = message.Body;
                entity.EventType = message.MessageType ?? "Unknown";
            }
        }
        catch (Exception ex)
        {
            Logger.LogWarning(ex, "Failed to parse business data from message {MessageId}, storing as raw data", message.MessageId);
            entity.EventData = message.Body;
            entity.EventType = message.MessageType ?? "Unknown";
        }

        await Task.CompletedTask; // Placeholder for async operations if needed
    }
}
