using Azure.Messaging.ServiceBus;
using Azure.Messaging.ServiceBus.Administration;
using Microsoft.Extensions.Logging;
using Messaging.Core.Abstractions;
using Messaging.Core.Configuration;
using Messaging.Core.Models;

namespace Messaging.AzureServiceBus.Services;

/// <summary>
/// Azure Service Bus implementation of topic subscription management
/// </summary>
public class AzureTopicSubscriptionManager : ITopicSubscriptionManager
{
    private readonly ILogger<AzureTopicSubscriptionManager> _logger;
    private readonly AzureServiceBusConfiguration _config;
    private readonly ServiceBusAdministrationClient _adminClient;

    public string PlatformName => "AzureServiceBus";

    public AzureTopicSubscriptionManager(
        ILogger<AzureTopicSubscriptionManager> logger,
        AzureServiceBusConfiguration config)
    {
        _logger = logger;
        _config = config;
        _adminClient = new ServiceBusAdministrationClient(_config.ConnectionString);
    }

    public async Task<IEnumerable<TopicSubscription>> GetActiveSubscriptionsAsync(CancellationToken cancellationToken = default)
    {
        var subscriptions = new List<TopicSubscription>();

        try
        {
            // Get all topics first
            await foreach (var topicProperties in _adminClient.GetTopicsAsync(cancellationToken))
            {
                // Get subscriptions for each topic
                await foreach (var subscriptionProperties in _adminClient.GetSubscriptionsAsync(topicProperties.Name, cancellationToken))
                {
                    var subscription = new TopicSubscription
                    {
                        SubscriptionName = subscriptionProperties.SubscriptionName,
                        TopicName = subscriptionProperties.TopicName,
                        Status = MapSubscriptionStatus(subscriptionProperties.Status),
                        CreatedAt = DateTime.UtcNow, // Azure Service Bus doesn't expose creation time
                        UpdatedAt = DateTime.UtcNow,
                        Configuration = new TopicSubscriptionConfig
                        {
                            SubscriptionName = subscriptionProperties.SubscriptionName,
                            TopicName = subscriptionProperties.TopicName,
                            MaxConcurrentMessages = 10, // Default value, not exposed in properties
                            AckDeadlineSeconds = (int)subscriptionProperties.LockDuration.TotalSeconds,
                            MaxDeliveryAttempts = subscriptionProperties.MaxDeliveryCount,
                            DeadLetterTopic = subscriptionProperties.ForwardDeadLetteredMessagesTo
                        },
                        PlatformDetails = new Dictionary<string, object>
                        {
                            ["Status"] = subscriptionProperties.Status.ToString(),
                            ["Status"] = subscriptionProperties.Status.ToString(),
                            ["LockDuration"] = subscriptionProperties.LockDuration.ToString(),
                            ["DefaultMessageTimeToLive"] = subscriptionProperties.DefaultMessageTimeToLive.ToString()
                        }
                    };

                    subscriptions.Add(subscription);
                }
            }

            _logger.LogInformation("Retrieved {Count} active subscriptions", subscriptions.Count);
            return subscriptions;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get active subscriptions");
            throw;
        }
    }

    public async Task<TopicSubscription> CreateSubscriptionAsync(TopicSubscriptionConfig subscription, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Creating subscription {SubscriptionName} for topic {TopicName}", 
                subscription.SubscriptionName, subscription.TopicName);

            // Ensure topic exists
            if (!await _adminClient.TopicExistsAsync(subscription.TopicName, cancellationToken))
            {
                var topicOptions = new CreateTopicOptions(subscription.TopicName)
                {
                    EnablePartitioning = false,
                    SupportOrdering = subscription.EnableMessageOrdering
                };
                await _adminClient.CreateTopicAsync(topicOptions, cancellationToken);
                _logger.LogInformation("Created topic {TopicName}", subscription.TopicName);
            }

            // Create subscription
            var subscriptionOptions = new CreateSubscriptionOptions(subscription.TopicName, subscription.SubscriptionName)
            {
                LockDuration = TimeSpan.FromSeconds(subscription.AckDeadlineSeconds),
                MaxDeliveryCount = subscription.MaxDeliveryAttempts,
                ForwardDeadLetteredMessagesTo = subscription.DeadLetterTopic
            };

            var createdSubscription = await _adminClient.CreateSubscriptionAsync(subscriptionOptions, cancellationToken);

            // Add filters if specified
            await AddFiltersToSubscriptionAsync(subscription, cancellationToken);

            _logger.LogInformation("Successfully created subscription {SubscriptionName} for topic {TopicName}", 
                subscription.SubscriptionName, subscription.TopicName);

            return new TopicSubscription
            {
                SubscriptionName = createdSubscription.Value.SubscriptionName,
                TopicName = createdSubscription.Value.TopicName,
                Status = MapSubscriptionStatus(createdSubscription.Value.Status),
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow,
                Configuration = subscription
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to create subscription {SubscriptionName} for topic {TopicName}", 
                subscription.SubscriptionName, subscription.TopicName);
            throw;
        }
    }

    public async Task<TopicSubscription> UpdateSubscriptionAsync(string subscriptionName, TopicSubscriptionConfig config, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Updating subscription {SubscriptionName} for topic {TopicName}", 
                subscriptionName, config.TopicName);

            var existingSubscription = await _adminClient.GetSubscriptionAsync(config.TopicName, subscriptionName, cancellationToken);
            
            // Update properties
            existingSubscription.Value.LockDuration = TimeSpan.FromSeconds(config.AckDeadlineSeconds);
            existingSubscription.Value.MaxDeliveryCount = config.MaxDeliveryAttempts;
            existingSubscription.Value.ForwardDeadLetteredMessagesTo = config.DeadLetterTopic;

            var updatedSubscription = await _adminClient.UpdateSubscriptionAsync(existingSubscription.Value, cancellationToken);

            // Update filters
            await UpdateFiltersForSubscriptionAsync(config, cancellationToken);

            _logger.LogInformation("Successfully updated subscription {SubscriptionName}", subscriptionName);

            return new TopicSubscription
            {
                SubscriptionName = updatedSubscription.Value.SubscriptionName,
                TopicName = updatedSubscription.Value.TopicName,
                Status = MapSubscriptionStatus(updatedSubscription.Value.Status),
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow,
                Configuration = config
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to update subscription {SubscriptionName}", subscriptionName);
            throw;
        }
    }

    public async Task DeleteSubscriptionAsync(string subscriptionName, CancellationToken cancellationToken = default)
    {
        try
        {
            // Find the topic for this subscription
            await foreach (var topicProperties in _adminClient.GetTopicsAsync(cancellationToken))
            {
                if (await _adminClient.SubscriptionExistsAsync(topicProperties.Name, subscriptionName, cancellationToken))
                {
                    await _adminClient.DeleteSubscriptionAsync(topicProperties.Name, subscriptionName, cancellationToken);
                    _logger.LogInformation("Deleted subscription {SubscriptionName} from topic {TopicName}", 
                        subscriptionName, topicProperties.Name);
                    return;
                }
            }

            _logger.LogWarning("Subscription {SubscriptionName} not found", subscriptionName);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to delete subscription {SubscriptionName}", subscriptionName);
            throw;
        }
    }

    public async Task<SubscriptionHealth> GetSubscriptionHealthAsync(string subscriptionName, CancellationToken cancellationToken = default)
    {
        try
        {
            // Find the subscription across all topics
            await foreach (var topicProperties in _adminClient.GetTopicsAsync(cancellationToken))
            {
                if (await _adminClient.SubscriptionExistsAsync(topicProperties.Name, subscriptionName, cancellationToken))
                {
                    var subscriptionProperties = await _adminClient.GetSubscriptionAsync(topicProperties.Name, subscriptionName, cancellationToken);
                    var runtimeProperties = await _adminClient.GetSubscriptionRuntimePropertiesAsync(topicProperties.Name, subscriptionName, cancellationToken);

                    return new SubscriptionHealth
                    {
                        SubscriptionName = subscriptionName,
                        IsHealthy = subscriptionProperties.Value.Status == EntityStatus.Active,
                        CheckedAt = DateTime.UtcNow,
                        PendingMessages = runtimeProperties.Value.ActiveMessageCount,
                        UnacknowledgedMessages = runtimeProperties.Value.ActiveMessageCount,
                        Metrics = new Dictionary<string, object>
                        {
                            ["TotalMessageCount"] = runtimeProperties.Value.TotalMessageCount,
                            ["DeadLetterMessageCount"] = runtimeProperties.Value.DeadLetterMessageCount,
                            ["TransferMessageCount"] = runtimeProperties.Value.TransferMessageCount,
                            ["TransferDeadLetterMessageCount"] = runtimeProperties.Value.TransferDeadLetterMessageCount,
                            ["CreatedAt"] = runtimeProperties.Value.CreatedAt,
                            ["UpdatedAt"] = DateTime.UtcNow,
                            ["AccessedAt"] = runtimeProperties.Value.AccessedAt
                        }
                    };
                }
            }

            return new SubscriptionHealth
            {
                SubscriptionName = subscriptionName,
                IsHealthy = false,
                CheckedAt = DateTime.UtcNow,
                ErrorMessage = "Subscription not found"
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get health for subscription {SubscriptionName}", subscriptionName);
            
            return new SubscriptionHealth
            {
                SubscriptionName = subscriptionName,
                IsHealthy = false,
                CheckedAt = DateTime.UtcNow,
                ErrorMessage = ex.Message
            };
        }
    }

    public async Task<bool> TestSubscriptionAsync(string subscriptionName, CancellationToken cancellationToken = default)
    {
        try
        {
            var health = await GetSubscriptionHealthAsync(subscriptionName, cancellationToken);
            return health.IsHealthy;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to test subscription {SubscriptionName}", subscriptionName);
            return false;
        }
    }

    private static SubscriptionStatus MapSubscriptionStatus(EntityStatus status)
    {
        if (status == EntityStatus.Active)
            return SubscriptionStatus.Active;
        else if (status == EntityStatus.Disabled)
            return SubscriptionStatus.Inactive;
        else
            return SubscriptionStatus.Unknown;
    }

    private async Task AddFiltersToSubscriptionAsync(TopicSubscriptionConfig subscription, CancellationToken cancellationToken)
    {
        // Remove default filter first
        try
        {
            await _adminClient.DeleteRuleAsync(subscription.TopicName, subscription.SubscriptionName, "$Default", cancellationToken);
        }
        catch
        {
            // Ignore if default filter doesn't exist
        }

        // Add custom filters
        foreach (var filter in subscription.Filters.Where(f => f.Enabled))
        {
            await AddFilterRuleAsync(subscription.TopicName, subscription.SubscriptionName, filter, cancellationToken);
        }
    }

    private async Task UpdateFiltersForSubscriptionAsync(TopicSubscriptionConfig config, CancellationToken cancellationToken)
    {
        // Get existing rules
        var existingRules = new List<string>();
        await foreach (var rule in _adminClient.GetRulesAsync(config.TopicName, config.SubscriptionName, cancellationToken))
        {
            existingRules.Add(rule.Name);
        }

        // Remove rules that are no longer needed
        var newFilterNames = config.Filters.Where(f => f.Enabled).Select(f => f.Name).ToHashSet();
        foreach (var ruleName in existingRules.Where(r => !newFilterNames.Contains(r) && r != "$Default"))
        {
            await _adminClient.DeleteRuleAsync(config.TopicName, config.SubscriptionName, ruleName, cancellationToken);
        }

        // Add or update filters
        foreach (var filter in config.Filters.Where(f => f.Enabled))
        {
            if (existingRules.Contains(filter.Name))
            {
                // Update existing rule
                await _adminClient.DeleteRuleAsync(config.TopicName, config.SubscriptionName, filter.Name, cancellationToken);
            }
            await AddFilterRuleAsync(config.TopicName, config.SubscriptionName, filter, cancellationToken);
        }
    }

    private async Task AddFilterRuleAsync(string topicName, string subscriptionName, MessageFilterConfig filter, CancellationToken cancellationToken)
    {
        try
        {
            RuleFilter ruleFilter = filter.FilterType.ToLowerInvariant() switch
            {
                "sql" => new SqlRuleFilter(filter.Parameters.GetValueOrDefault("Expression", "1=1").ToString()!),
                "correlation" => CreateCorrelationFilter(filter.Parameters),
                _ => new TrueRuleFilter()
            };

            var ruleOptions = new CreateRuleOptions(filter.Name, ruleFilter);
            await _adminClient.CreateRuleAsync(topicName, subscriptionName, ruleOptions, cancellationToken);
            
            _logger.LogDebug("Added filter rule {FilterName} to subscription {SubscriptionName}", 
                filter.Name, subscriptionName);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to add filter rule {FilterName} to subscription {SubscriptionName}", 
                filter.Name, subscriptionName);
        }
    }

    private static CorrelationRuleFilter CreateCorrelationFilter(Dictionary<string, object> parameters)
    {
        var filter = new CorrelationRuleFilter();
        
        if (parameters.TryGetValue("CorrelationId", out var correlationId))
            filter.CorrelationId = correlationId.ToString();
        
        if (parameters.TryGetValue("MessageId", out var messageId))
            filter.MessageId = messageId.ToString();
        
        if (parameters.TryGetValue("Subject", out var subject))
            filter.Subject = subject.ToString();
        
        if (parameters.TryGetValue("SessionId", out var sessionId))
            filter.SessionId = sessionId.ToString();

        // Add custom properties
        foreach (var kvp in parameters.Where(p => p.Key.StartsWith("Property.")))
        {
            var propertyName = kvp.Key.Substring(9); // Remove "Property." prefix
            filter.ApplicationProperties[propertyName] = kvp.Value;
        }

        return filter;
    }
}
