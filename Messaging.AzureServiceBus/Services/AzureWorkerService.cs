using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Messaging.Core.Configuration;

namespace Messaging.AzureServiceBus.Services;

public class AzureWorkerService : BackgroundService
{
    private readonly ILogger<AzureWorkerService> _logger;
    private readonly IServiceProvider _serviceProvider;
    private readonly MessagingConfiguration _config;

    public AzureWorkerService(
        ILogger<AzureWorkerService> logger,
        IServiceProvider serviceProvider,
        MessagingConfiguration config)
    {
        _logger = logger;
        _serviceProvider = serviceProvider;
        _config = config;
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        _logger.LogInformation("Azure Service Bus Worker Service started");

        try
        {
            using var scope = _serviceProvider.CreateScope();
            var orchestrationService = scope.ServiceProvider.GetRequiredService<AzureOrchestrationService>();

            // Check if this is a one-time run or continuous processing
            var args = Environment.GetCommandLineArgs();
            var isOneTimeRun = args.Contains("--once") || args.Contains("-o");

            if (isOneTimeRun)
            {
                _logger.LogInformation("Running Azure Service Bus processing once");
                var success = await orchestrationService.ProcessMessagesAsync(stoppingToken);
                
                if (success)
                {
                    _logger.LogInformation("Azure Service Bus one-time processing completed successfully");
                }
                else
                {
                    _logger.LogError("Azure Service Bus one-time processing failed");
                    Environment.ExitCode = 1;
                }
            }
            else
            {
                _logger.LogInformation("Starting continuous Azure Service Bus processing");
                await RunContinuousProcessingAsync(orchestrationService, stoppingToken);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Azure Service Bus Worker Service encountered an error");
            Environment.ExitCode = 1;
        }
        finally
        {
            _logger.LogInformation("Azure Service Bus Worker Service completed");
        }
    }

    private async Task RunContinuousProcessingAsync(AzureOrchestrationService orchestrationService, CancellationToken stoppingToken)
    {
        var processingInterval = TimeSpan.FromSeconds(30); // Default polling interval
        var consecutiveFailures = 0;
        const int maxConsecutiveFailures = 5;

        while (!stoppingToken.IsCancellationRequested)
        {
            try
            {
                _logger.LogDebug("Starting Azure Service Bus processing cycle");
                
                var success = await orchestrationService.ProcessMessagesAsync(stoppingToken);
                
                if (success)
                {
                    consecutiveFailures = 0;
                    _logger.LogDebug("Azure Service Bus processing cycle completed successfully");
                }
                else
                {
                    consecutiveFailures++;
                    _logger.LogWarning("Azure Service Bus processing cycle failed. Consecutive failures: {FailureCount}", 
                        consecutiveFailures);

                    if (consecutiveFailures >= maxConsecutiveFailures)
                    {
                        _logger.LogError("Maximum consecutive failures ({MaxFailures}) reached. Stopping service.", 
                            maxConsecutiveFailures);
                        Environment.ExitCode = 1;
                        break;
                    }
                }

                // Wait before next processing cycle
                await Task.Delay(processingInterval, stoppingToken);
            }
            catch (OperationCanceledException)
            {
                _logger.LogInformation("Azure Service Bus processing cancelled");
                break;
            }
            catch (Exception ex)
            {
                consecutiveFailures++;
                _logger.LogError(ex, "Error in Azure Service Bus processing cycle. Consecutive failures: {FailureCount}", 
                    consecutiveFailures);

                if (consecutiveFailures >= maxConsecutiveFailures)
                {
                    _logger.LogError("Maximum consecutive failures ({MaxFailures}) reached. Stopping service.", 
                        maxConsecutiveFailures);
                    Environment.ExitCode = 1;
                    break;
                }

                // Wait longer after an error
                await Task.Delay(TimeSpan.FromMinutes(1), stoppingToken);
            }
        }
    }

    public override async Task StartAsync(CancellationToken cancellationToken)
    {
        _logger.LogInformation("Starting Azure Service Bus messaging service");

        try
        {
            using var scope = _serviceProvider.CreateScope();
            var messagingService = scope.ServiceProvider.GetRequiredService<Core.Abstractions.IMessagingService>();
            
            await messagingService.StartAsync(cancellationToken);
            
            _logger.LogInformation("Azure Service Bus messaging service started successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to start Azure Service Bus messaging service");
            throw;
        }

        await base.StartAsync(cancellationToken);
    }

    public override async Task StopAsync(CancellationToken cancellationToken)
    {
        _logger.LogInformation("Stopping Azure Service Bus messaging service");

        try
        {
            using var scope = _serviceProvider.CreateScope();
            var messagingService = scope.ServiceProvider.GetRequiredService<Core.Abstractions.IMessagingService>();
            
            await messagingService.StopAsync(cancellationToken);
            
            _logger.LogInformation("Azure Service Bus messaging service stopped successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error stopping Azure Service Bus messaging service");
        }

        await base.StopAsync(cancellationToken);
    }
}
