using Microsoft.EntityFrameworkCore;
using Messaging.AzureServiceBus.Models;
using Messaging.Core.Models;

namespace Messaging.AzureServiceBus.Data;

/// <summary>
/// Entity Framework DbContext for managing Azure Service Bus message data and processing logs.
/// Provides data access for Azure Service Bus message entities and shared message logging functionality.
/// </summary>
/// <remarks>
/// This context manages two primary entities:
/// - AzureMessageEntity: Stores processed Azure Service Bus messages with metadata and business data
/// - MessageLog: Tracks message processing operations, batch information, and processing statistics
///
/// The context includes optimized database indexes for common query patterns including:
/// - Message identification and correlation
/// - Time-based queries for processing and enqueueing
/// - Business entity lookups (customer, order)
/// - Batch processing tracking
/// </remarks>
public class AzureMessageDbContext : DbContext
{
    /// <summary>
    /// Initializes a new instance of the AzureMessageDbContext with the specified options.
    /// </summary>
    /// <param name="options">The options to be used by the DbContext. Contains connection string and provider configuration.</param>
    public AzureMessageDbContext(DbContextOptions<AzureMessageDbContext> options) : base(options)
    {
    }

    /// <summary>
    /// Gets or sets the DbSet for Azure Service Bus message entities.
    /// Represents processed messages from Azure Service Bus with full metadata, properties, and extracted business data.
    /// </summary>
    /// <value>
    /// A DbSet containing AzureMessageEntity records that store:
    /// - Message metadata (ID, correlation ID, session ID, type)
    /// - Service Bus specific properties (enqueue time, delivery count, TTL)
    /// - Queue/Topic/Subscription routing information
    /// - Extracted business data (customer ID, order ID, event type)
    /// - Processing timestamps and batch tracking
    /// </value>
    public DbSet<AzureMessageEntity> AzureMessages { get; set; }

    /// <summary>
    /// Gets or sets the DbSet for message processing logs.
    /// Tracks processing operations, batch statistics, and error information across all message processors.
    /// </summary>
    /// <value>
    /// A DbSet containing MessageLog records that store:
    /// - Batch processing information and statistics
    /// - Processing duration and status tracking
    /// - Error messages and retry counts
    /// - Source system identification
    /// - Message throughput metrics
    /// </value>
    public DbSet<MessageLog> MessageLogs { get; set; }

    /// <summary>
    /// Configures the database model and relationships when the model is being created.
    /// Sets up indexes, constraints, and database-specific configurations for optimal query performance.
    /// </summary>
    /// <param name="modelBuilder">The builder being used to construct the model for this context.</param>
    /// <remarks>
    /// This method configures:
    ///
    /// For AzureMessageEntity:
    /// - Performance indexes on frequently queried fields (MessageId, CorrelationId, MessageType, etc.)
    /// - Time-based indexes for processing and enqueueing timestamps
    /// - Business entity indexes for customer and order lookups
    /// - Unique constraint on MessageId to prevent duplicate message processing
    ///
    /// For MessageLog:
    /// - Batch processing indexes for tracking and reporting
    /// - Status and timing indexes for monitoring and alerting
    /// - Source system indexes for multi-platform message processing
    ///
    /// All indexes use explicit database names following the naming convention:
    /// - ix_{table_name}_{column_name} for regular indexes
    /// - uk_{table_name}_{column_name} for unique constraints
    /// </remarks>
    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);

        // Configure AzureMessageEntity
        modelBuilder.Entity<AzureMessageEntity>(entity =>
        {
            entity.HasIndex(e => e.MessageId)
                .HasDatabaseName("ix_azure_messages_message_id");

            entity.HasIndex(e => e.CorrelationId)
                .HasDatabaseName("ix_azure_messages_correlation_id");

            entity.HasIndex(e => e.MessageType)
                .HasDatabaseName("ix_azure_messages_message_type");

            entity.HasIndex(e => e.EventType)
                .HasDatabaseName("ix_azure_messages_event_type");

            entity.HasIndex(e => e.CustomerId)
                .HasDatabaseName("ix_azure_messages_customer_id");

            entity.HasIndex(e => e.OrderId)
                .HasDatabaseName("ix_azure_messages_order_id");

            entity.HasIndex(e => e.EnqueuedTime)
                .HasDatabaseName("ix_azure_messages_enqueued_time");

            entity.HasIndex(e => e.ProcessedAt)
                .HasDatabaseName("ix_azure_messages_processed_at");

            entity.HasIndex(e => e.ImportBatchId)
                .HasDatabaseName("ix_azure_messages_import_batch_id");

            entity.HasIndex(e => e.CreatedAt)
                .HasDatabaseName("ix_azure_messages_created_at");

            // Unique constraint on MessageId to prevent duplicates
            entity.HasIndex(e => e.MessageId)
                .IsUnique()
                .HasDatabaseName("uk_azure_messages_message_id");
        });

        // Configure MessageLog
        modelBuilder.Entity<MessageLog>(entity =>
        {
            entity.HasIndex(e => e.BatchId)
                .HasDatabaseName("ix_message_logs_batch_id");

            entity.HasIndex(e => e.MessageId)
                .HasDatabaseName("ix_message_logs_message_id");

            entity.HasIndex(e => e.SourceSystem)
                .HasDatabaseName("ix_message_logs_source_system");

            entity.HasIndex(e => e.ProcessorType)
                .HasDatabaseName("ix_message_logs_processor_type");

            entity.HasIndex(e => e.Status)
                .HasDatabaseName("ix_message_logs_status");

            entity.HasIndex(e => e.StartTime)
                .HasDatabaseName("ix_message_logs_start_time");

            entity.HasIndex(e => e.CreatedAt)
                .HasDatabaseName("ix_message_logs_created_at");
        });
    }
}
