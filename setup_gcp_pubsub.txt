# Instructions to Process Test Messages from Google Cloud Pub/Sub

## 📋 Prerequisites

You'll need:
- Google Cloud Platform (GCP) account with Pub/Sub API enabled
- GCP Project ID
- Topic name and Subscription name (or we'll create them)
- Service Account with Pub/Sub permissions
- PostgreSQL database (for storing processed messages)
- .NET 8 SDK installed

## 🔧 Step 1: Set Up Google Cloud Authentication

### 1.1 Create Service Account (if not already done)

```bash
# Install Google Cloud CLI if not already installed
# https://cloud.google.com/sdk/docs/install

# Login to Google Cloud
gcloud auth login

# Set your project
gcloud config set project YOUR_PROJECT_ID

# Create service account
gcloud iam service-accounts create pubsub-processor \
    --description="Service account for Pub/Sub message processing" \
    --display-name="Pub/Sub Processor"

# Grant necessary permissions
gcloud projects add-iam-policy-binding YOUR_PROJECT_ID \
    --member="serviceAccount:pubsub-processor@YOUR_PROJECT_ID.iam.gserviceaccount.com" \
    --role="roles/pubsub.subscriber"

gcloud projects add-iam-policy-binding YOUR_PROJECT_ID \
    --member="serviceAccount:pubsub-processor@YOUR_PROJECT_ID.iam.gserviceaccount.com" \
    --role="roles/pubsub.publisher"

# Create and download service account key
gcloud iam service-accounts keys create ~/pubsub-key.json \
    --iam-account=pubsub-processor@YOUR_PROJECT_ID.iam.gserviceaccount.com
```

### 1.2 Set Environment Variable

**On macOS/Linux:**
```bash
export GOOGLE_APPLICATION_CREDENTIALS="$HOME/pubsub-key.json"
```

**On Windows:**
```cmd
set GOOGLE_APPLICATION_CREDENTIALS=C:\path\to\your\pubsub-key.json
```

## 🔧 Step 2: Create Topic and Subscription

### 2.1 Using gcloud CLI

```bash
# Create topic
gcloud pubsub topics create YOUR_TOPIC_NAME

# Create subscription
gcloud pubsub subscriptions create YOUR_SUBSCRIPTION_NAME \
    --topic=YOUR_TOPIC_NAME \
    --ack-deadline=600

# Verify creation
gcloud pubsub topics list
gcloud pubsub subscriptions list
```

### 2.2 Using Google Cloud Console

1. Go to Google Cloud Console → Pub/Sub
2. Click "Create Topic"
3. Enter topic name: `YOUR_TOPIC_NAME`
4. Click "Create Topic"
5. Click on your topic → "Create Subscription"
6. Enter subscription name: `YOUR_SUBSCRIPTION_NAME`
7. Set acknowledgment deadline: 600 seconds
8. Click "Create"

## 🔧 Step 3: Configure the Application

### 3.1 Update Configuration File

Edit the `Messaging.GooglePubSub/appsettings.json` file:

Replace these values in the GooglePubSub section:
```json
"GooglePubSub": {
  "ProjectId": "YOUR_PROJECT_ID",
  "SubscriptionName": "YOUR_SUBSCRIPTION_NAME",
  "TopicName": "YOUR_TOPIC_NAME",
  "MaxMessages": 100,
  "AckDeadlineSeconds": 600,
  "MaxRetryAttempts": 3,
  "EnableMessageOrdering": false
},
```

### 3.2 Update Database Configuration

Update the database settings in the same file:
```json
"Database": {
  "ConnectionString": "",
  "Host": "localhost",
  "Port": 5432,
  "Database": "messaging_db",
  "Username": "postgres",
  "Password": "YOUR_POSTGRES_PASSWORD_HERE"
},
```

### 3.3 Update GCP Project ID

```json
"MessagingConfiguration": {
  "Environment": "Development",
  "GcpProjectId": "YOUR_PROJECT_ID",
```

## 🗄️ Step 4: Set Up PostgreSQL Database

### 4.1 Install PostgreSQL (if not already installed)

**On macOS:**
```bash
brew install postgresql
brew services start postgresql
```

**On Ubuntu/Debian:**
```bash
sudo apt update
sudo apt install postgresql postgresql-contrib
sudo systemctl start postgresql
```

### 4.2 Create Database and User

```bash
# Connect to PostgreSQL
psql -U postgres

# Create database
CREATE DATABASE messaging_db;

# Create user (optional, or use existing postgres user)
CREATE USER messaging_user WITH PASSWORD 'your_password_here';
GRANT ALL PRIVILEGES ON DATABASE messaging_db TO messaging_user;

# Exit
\q
```

## 📨 Step 5: Send a Test Message to Your Topic

### 5.1 Using gcloud CLI

```bash
# Send a simple test message
gcloud pubsub topics publish YOUR_TOPIC_NAME \
    --message='{"eventId": "EVT001", "userId": "USER123", "eventType": "UserRegistered", "eventData": {"email": "<EMAIL>", "name": "John Doe"}, "sourceSystem": "WebApp", "schemaVersion": "1.0"}'

# Send message with attributes
gcloud pubsub topics publish YOUR_TOPIC_NAME \
    --message='{"eventId": "EVT002", "userId": "USER456", "eventType": "OrderPlaced", "eventData": {"orderId": "ORD789", "amount": 99.99}, "sourceSystem": "ECommerce", "schemaVersion": "1.0"}' \
    --attribute=priority=high,source=api
```

### 5.2 Using Google Cloud Console

1. Go to Google Cloud Console → Pub/Sub → Topics
2. Click on your topic name
3. Click "Publish Message"
4. Add this sample JSON message:

```json
{
  "eventId": "EVT001",
  "userId": "USER123",
  "eventType": "UserRegistered",
  "eventData": {
    "email": "<EMAIL>",
    "name": "John Doe",
    "registrationDate": "2024-01-15T10:30:00Z"
  },
  "sourceSystem": "WebApp",
  "schemaVersion": "1.0",
  "traceId": "trace-12345"
}
```

5. Optionally add attributes:
   - Key: `priority`, Value: `high`
   - Key: `source`, Value: `web`
6. Click "Publish"

### 5.3 Using Python Script

Create a file `send-test-message.py`:

```python
from google.cloud import pubsub_v1
import json
import os

# Set up the publisher
project_id = "YOUR_PROJECT_ID"
topic_name = "YOUR_TOPIC_NAME"

publisher = pubsub_v1.PublisherClient()
topic_path = publisher.topic_path(project_id, topic_name)

# Message data
message_data = {
    "eventId": "EVT001",
    "userId": "USER123",
    "eventType": "UserRegistered",
    "eventData": {
        "email": "<EMAIL>",
        "name": "John Doe"
    },
    "sourceSystem": "WebApp",
    "schemaVersion": "1.0"
}

# Convert to JSON and encode
message_json = json.dumps(message_data)
data = message_json.encode("utf-8")

# Publish message
future = publisher.publish(topic_path, data, priority="high", source="script")
print(f"Published message ID: {future.result()}")
```

Run the script:
```bash
pip install google-cloud-pubsub
python send-test-message.py
```

## 🚀 Step 6: Run the Message Processor

### 6.1 Navigate to the Project Directory

```bash
cd Messaging.GooglePubSub
```

### 6.2 Build the Project

```bash
dotnet build
```

### 6.3 Run the Application

**Option A: One-time Processing (Recommended for Testing)**
```bash
dotnet run -- --once
```

**Option B: Continuous Processing**
```bash
dotnet run
```

**Option C: With Specific Configuration**
```bash
dotnet run --environment Development -- --once
```

## 📊 Step 7: Monitor the Processing

### 7.1 Check Console Output

You should see logs similar to:
```
info: Starting Google Pub/Sub Worker Service
info: Google Pub/Sub messaging service started successfully
info: Starting Google Pub/Sub message processing for batch: [BATCH_ID]
info: Received 1 messages from Google Pub/Sub
info: Processing 1 Google Pub/Sub messages
info: Importing 1 Google message entities
```

### 7.2 Check Database

Connect to your PostgreSQL database and verify the data:

```sql
-- Connect to database
psql -U postgres -d messaging_db

-- Check processed messages
SELECT * FROM google_messages ORDER BY created_at DESC LIMIT 5;

-- Check import logs
SELECT * FROM message_logs ORDER BY created_at DESC LIMIT 5;

-- Check specific message details
SELECT
    message_id,
    topic_name,
    subscription_name,
    user_id,
    event_id,
    event_type,
    processed_at
FROM google_messages
WHERE topic_name = 'YOUR_TOPIC_NAME';
```

## 🔧 Step 8: Troubleshooting

### 8.1 Common Issues and Solutions

**Issue: Authentication Error**
```
Error: "The Application Default Credentials are not available"
```
**Solution:**
- Ensure GOOGLE_APPLICATION_CREDENTIALS environment variable is set
- Verify the service account key file exists and is readable
- Try: `gcloud auth application-default login` for local development

**Issue: Permission Denied**
```
Error: "User not authorized to perform this action"
```
**Solution:**
- Verify service account has Pub/Sub Subscriber and Publisher roles
- Check that the topic and subscription exist in the correct project
- Ensure you're using the correct project ID

**Issue: Topic/Subscription Not Found**
```
Error: "Resource not found"
```
**Solution:**
- Verify topic and subscription names are correct
- Ensure they exist in the specified GCP project
- Check project ID in configuration matches your GCP project

**Issue: Database Connection Error**
```
Error: "Connection to database failed"
```
**Solution:**
- Ensure PostgreSQL is running
- Verify database credentials in appsettings.json
- Test connection: `psql -U postgres -d messaging_db`

**Issue: No Messages Received**
```
Info: "No messages received from Google Pub/Sub"
```
**Solution:**
- Verify messages exist in the subscription (check Google Cloud Console)
- Check subscription acknowledgment deadline hasn't expired
- Ensure subscription is pulling from the correct topic

### 8.2 Enable Debug Logging

Update `appsettings.json` for more detailed logs:

```json
{
  "Logging": {
    "LogLevel": {
      "Default": "Debug",
      "Microsoft.EntityFrameworkCore": "Information",
      "Messaging.GooglePubSub": "Debug"
    }
  }
}
```

### 8.3 Health Check

The application includes health checks. You can verify connectivity:

```bash
# The health check runs automatically when the service starts
# Look for logs like:
# "Google Pub/Sub health check passed"
```

### 8.4 Verify Pub/Sub Setup

```bash
# Check if topic exists
gcloud pubsub topics describe YOUR_TOPIC_NAME

# Check if subscription exists
gcloud pubsub subscriptions describe YOUR_SUBSCRIPTION_NAME

# Check messages in subscription
gcloud pubsub subscriptions pull YOUR_SUBSCRIPTION_NAME --limit=5 --auto-ack
```

## 📈 Step 9: Advanced Configuration

### 9.1 Multiple Topics (Optional)

If you want to process multiple topics, update the configuration:

```json
{
  "GooglePubSub": {
    "EnableMultipleTopics": true,
    "TopicSubscriptions": [
      {
        "SubscriptionName": "user-events-subscription",
        "TopicName": "user-events-topic",
        "MaxConcurrentMessages": 10,
        "AckDeadlineSeconds": 300,
        "MaxDeliveryAttempts": 3
      },
      {
        "SubscriptionName": "analytics-subscription",
        "TopicName": "analytics-topic",
        "MaxConcurrentMessages": 20,
        "AckDeadlineSeconds": 600,
        "MaxDeliveryAttempts": 5
      }
    ]
  }
}
```

### 9.2 Performance Tuning

Adjust these settings based on your needs:

```json
{
  "GooglePubSub": {
    "MaxMessages": 200,
    "AckDeadlineSeconds": 300,
    "MaxRetryAttempts": 5
  },
  "Processing": {
    "BatchSize": 50,
    "MaxConcurrentBatches": 3
  }
}
```

### 9.3 Message Ordering (Optional)

For ordered message processing:

```json
{
  "GooglePubSub": {
    "EnableMessageOrdering": true
  }
}
```

When publishing ordered messages:
```bash
gcloud pubsub topics publish YOUR_TOPIC_NAME \
    --message='{"eventId": "EVT001", "userId": "USER123"}' \
    --ordering-key=USER123
```
#useful command: netstat -anv | grep 8009 | grep LISTEN
## ✅ Step 10: Verification Checklist

- [ ] Google Cloud authentication configured (service account key)
- [ ] Topic and subscription created in GCP
- [ ] Configuration file updated with your project ID, topic, and subscription
- [ ] PostgreSQL database is running and accessible
- [ ] Test message sent to your Pub/Sub topic
- [ ] Application runs without errors
- [ ] Message appears in `google_messages` table
- [ ] Import log created in `message_logs` table
- [ ] Console shows successful processing logs

## 🎯 Expected Results

After successful processing, you should see:

1. **Console Output:** Successful processing logs
2. **Database Records:** New entries in both `google_messages` and `message_logs` tables
3. **Message Acknowledgment:** Message removed from Pub/Sub subscription
4. **Structured Data:** Business fields (eventId, userId, eventType) extracted and stored

The application will automatically:
- Connect to your Google Pub/Sub subscription
- Receive and process messages
- Extract business data from JSON message bodies
- Store complete message metadata and parsed data in PostgreSQL
- Acknowledge processed messages
- Log all operations for monitoring and troubleshooting

## 📞 Support

If you encounter any issues or need help with specific configuration steps, refer to the logs and error messages for troubleshooting guidance.

## 🔗 Additional Resources

- [Google Cloud Pub/Sub Documentation](https://cloud.google.com/pubsub/docs)
- [Google Cloud CLI Installation](https://cloud.google.com/sdk/docs/install)
- [Service Account Authentication](https://cloud.google.com/docs/authentication/getting-started)
